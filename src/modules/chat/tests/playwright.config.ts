/**
 * Playwright 配置文件 - 聊天模块端到端测试
 */

import { defineConfig, devices } from '@playwright/test'

/**
 * 从环境变量读取配置
 */
const baseURL = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173'
const headless = process.env.CI === 'true'
const workers = process.env.CI ? 1 : undefined

export default defineConfig({
  // 测试目录
  testDir: './e2e',
  
  // 测试文件匹配模式
  testMatch: '**/*.spec.ts',
  
  // 全局设置
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: workers,
  
  // 报告器配置
  reporter: [
    ['html', { outputFolder: './test-results/playwright-report' }],
    ['json', { outputFile: './test-results/playwright-results.json' }],
    ['junit', { outputFile: './test-results/playwright-junit.xml' }],
    process.env.CI ? ['github'] : ['list']
  ],
  
  // 全局测试配置
  use: {
    // 基础 URL
    baseURL,
    
    // 浏览器配置
    headless,
    
    // 视口大小
    viewport: { width: 1280, height: 720 },
    
    // 忽略 HTTPS 错误
    ignoreHTTPSErrors: true,
    
    // 截图配置
    screenshot: 'only-on-failure',
    
    // 视频录制
    video: 'retain-on-failure',
    
    // 追踪配置
    trace: 'retain-on-failure',
    
    // 操作超时
    actionTimeout: 10000,
    
    // 导航超时
    navigationTimeout: 30000,
    
    // 等待超时
    expect: {
      timeout: 5000
    },
    
    // 语言环境
    locale: 'zh-CN',
    
    // 时区
    timezoneId: 'Asia/Shanghai',
    
    // 权限
    permissions: ['notifications', 'microphone', 'camera'],
    
    // 存储状态
    storageState: undefined
  },
  
  // 项目配置 - 不同浏览器和设备
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    },

    // 移动端测试
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] }
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] }
    },

    // 平板端测试
    {
      name: 'Tablet',
      use: { ...devices['iPad Pro'] }
    },

    // 认证状态测试
    {
      name: 'authenticated',
      use: {
        ...devices['Desktop Chrome'],
        storageState: './test-data/auth-state.json'
      },
      dependencies: ['setup']
    },

    // 设置项目
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
      teardown: 'cleanup'
    },

    // 清理项目
    {
      name: 'cleanup',
      testMatch: /.*\.cleanup\.ts/
    }
  ],
  
  // 输出目录
  outputDir: './test-results/playwright-artifacts',
  
  // Web 服务器配置（用于开发环境）
  webServer: process.env.CI ? undefined : {
    command: 'npm run dev',
    url: baseURL,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
    env: {
      NODE_ENV: 'test'
    }
  },
  
  // 全局设置和清理
  globalSetup: require.resolve('./setup/global-setup.ts'),
  globalTeardown: require.resolve('./setup/global-teardown.ts'),
  
  // 测试超时
  timeout: 30000,
  
  // 期望超时
  expect: {
    timeout: 5000,
    toHaveScreenshot: {
      threshold: 0.2,
      mode: 'strict'
    },
    toMatchSnapshot: {
      threshold: 0.2
    }
  },
  
  // 元数据
  metadata: {
    'test-type': 'e2e',
    'module': 'chat',
    'environment': process.env.NODE_ENV || 'test'
  }
})