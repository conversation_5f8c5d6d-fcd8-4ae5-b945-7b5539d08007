/**
 * 自定义 Jest 匹配器
 * 为聊天模块测试提供专用的断言方法
 */

import { expect } from '@jest/globals'
import type { MatcherFunction } from 'expect'
import type { User, Session, Message, FileInfo } from '../../types'

/**
 * 扩展 Jest 匹配器类型
 */
declare global {
  namespace jest {
    interface Matchers<R> {
      // 用户相关匹配器
      toBeOnlineUser(): R
      toBeOfflineUser(): R
      toHaveValidUserStructure(): R
      
      // 会话相关匹配器
      toBePrivateSession(): R
      toBeGroupSession(): R
      toBeChannelSession(): R
      toHaveUnreadMessages(): R
      toBePinnedSession(): R
      toBeMutedSession(): R
      toHaveValidSessionStructure(): R
      
      // 消息相关匹配器
      toBeTextMessage(): R
      toBeImageMessage(): R
      toBeFileMessage(): R
      toBeSystemMessage(): R
      toBeReplyMessage(): R
      toHaveValidMessageStructure(): R
      toBeRecentMessage(minutes?: number): R
      toHaveMentions(userIds?: string[]): R
      toHaveReactions(): R
      
      // 文件相关匹配器
      toBeImageFile(): R
      toBeDocumentFile(): R
      toBeVideoFile(): R
      toBeAudioFile(): R
      toHaveValidFileStructure(): R
      toBeWithinSizeLimit(maxSize: number): R
      
      // Vue 组件相关匹配器
      toHaveEmittedEvent(eventName: string): R
      toHaveEmittedEventWith(eventName: string, payload: any): R
      toHaveRenderedComponent(componentName: string): R
      toHaveClass(className: string): R
      toHaveStyle(property: string, value: string): R
      
      // API 响应相关匹配器
      toBeSuccessResponse(): R
      toBeErrorResponse(): R
      toHavePagination(): R
      
      // WebSocket 相关匹配器
      toBeValidWebSocketEvent(): R
      toHaveEventType(eventType: string): R
    }
  }
}

/**
 * 用户相关匹配器
 */
const toBeOnlineUser: MatcherFunction<[received: User]> = function (received) {
  const pass = received && received.status === 'online'
  
  return {
    message: () => `expected user to ${pass ? 'not ' : ''}be online`,
    pass
  }
}

const toBeOfflineUser: MatcherFunction<[received: User]> = function (received) {
  const pass = received && received.status === 'offline'
  
  return {
    message: () => `expected user to ${pass ? 'not ' : ''}be offline`,
    pass
  }
}

const toHaveValidUserStructure: MatcherFunction<[received: User]> = function (received) {
  const requiredFields = ['id', 'name', 'email', 'status']
  const hasAllFields = requiredFields.every(field => received && received.hasOwnProperty(field))
  const hasValidId = received && typeof received.id === 'string' && received.id.length > 0
  const hasValidEmail = received && typeof received.email === 'string' && received.email.includes('@')
  const hasValidStatus = received && ['online', 'offline', 'away', 'busy'].includes(received.status)
  
  const pass = hasAllFields && hasValidId && hasValidEmail && hasValidStatus
  
  return {
    message: () => `expected user to ${pass ? 'not ' : ''}have valid structure`,
    pass
  }
}

/**
 * 会话相关匹配器
 */
const toBePrivateSession: MatcherFunction<[received: Session]> = function (received) {
  const pass = received && received.type === 'private'
  
  return {
    message: () => `expected session to ${pass ? 'not ' : ''}be private`,
    pass
  }
}

const toBeGroupSession: MatcherFunction<[received: Session]> = function (received) {
  const pass = received && received.type === 'group'
  
  return {
    message: () => `expected session to ${pass ? 'not ' : ''}be group`,
    pass
  }
}

const toBeChannelSession: MatcherFunction<[received: Session]> = function (received) {
  const pass = received && received.type === 'channel'
  
  return {
    message: () => `expected session to ${pass ? 'not ' : ''}be channel`,
    pass
  }
}

const toHaveUnreadMessages: MatcherFunction<[received: Session]> = function (received) {
  const pass = received && received.unreadCount > 0
  
  return {
    message: () => `expected session to ${pass ? 'not ' : ''}have unread messages`,
    pass
  }
}

const toBePinnedSession: MatcherFunction<[received: Session]> = function (received) {
  const pass = received && received.isPinned === true
  
  return {
    message: () => `expected session to ${pass ? 'not ' : ''}be pinned`,
    pass
  }
}

const toBeMutedSession: MatcherFunction<[received: Session]> = function (received) {
  const pass = received && received.isMuted === true
  
  return {
    message: () => `expected session to ${pass ? 'not ' : ''}be muted`,
    pass
  }
}

const toHaveValidSessionStructure: MatcherFunction<[received: Session]> = function (received) {
  const requiredFields = ['id', 'type', 'participants']
  const hasAllFields = requiredFields.every(field => received && received.hasOwnProperty(field))
  const hasValidId = received && typeof received.id === 'string' && received.id.length > 0
  const hasValidType = received && ['private', 'group', 'channel'].includes(received.type)
  const hasValidParticipants = received && Array.isArray(received.participants) && received.participants.length > 0
  
  const pass = hasAllFields && hasValidId && hasValidType && hasValidParticipants
  
  return {
    message: () => `expected session to ${pass ? 'not ' : ''}have valid structure`,
    pass
  }
}

/**
 * 消息相关匹配器
 */
const toBeTextMessage: MatcherFunction<[received: Message]> = function (received) {
  const pass = received && received.type === 'text'
  
  return {
    message: () => `expected message to ${pass ? 'not ' : ''}be text message`,
    pass
  }
}

const toBeImageMessage: MatcherFunction<[received: Message]> = function (received) {
  const pass = received && received.type === 'image'
  
  return {
    message: () => `expected message to ${pass ? 'not ' : ''}be image message`,
    pass
  }
}

const toBeFileMessage: MatcherFunction<[received: Message]> = function (received) {
  const pass = received && received.type === 'file'
  
  return {
    message: () => `expected message to ${pass ? 'not ' : ''}be file message`,
    pass
  }
}

const toBeSystemMessage: MatcherFunction<[received: Message]> = function (received) {
  const pass = received && received.type === 'system'
  
  return {
    message: () => `expected message to ${pass ? 'not ' : ''}be system message`,
    pass
  }
}

const toBeReplyMessage: MatcherFunction<[received: Message]> = function (received) {
  const pass = received && received.replyTo && received.replyTo.length > 0
  
  return {
    message: () => `expected message to ${pass ? 'not ' : ''}be reply message`,
    pass
  }
}

const toHaveValidMessageStructure: MatcherFunction<[received: Message]> = function (received) {
  const requiredFields = ['id', 'content', 'type', 'senderId', 'sessionId', 'timestamp']
  const hasAllFields = requiredFields.every(field => received && received.hasOwnProperty(field))
  const hasValidId = received && typeof received.id === 'string' && received.id.length > 0
  const hasValidType = received && ['text', 'image', 'file', 'system'].includes(received.type)
  const hasValidTimestamp = received && received.timestamp instanceof Date
  
  const pass = hasAllFields && hasValidId && hasValidType && hasValidTimestamp
  
  return {
    message: () => `expected message to ${pass ? 'not ' : ''}have valid structure`,
    pass
  }
}

const toBeRecentMessage: MatcherFunction<[received: Message, minutes?: number]> = function (received, minutes = 5) {
  if (!received || !received.timestamp) {
    return {
      message: () => 'expected message to have timestamp',
      pass: false
    }
  }
  
  const now = new Date()
  const messageTime = new Date(received.timestamp)
  const diffMinutes = (now.getTime() - messageTime.getTime()) / (1000 * 60)
  const pass = diffMinutes <= minutes
  
  return {
    message: () => `expected message to ${pass ? 'not ' : ''}be within ${minutes} minutes`,
    pass
  }
}

const toHaveMentions: MatcherFunction<[received: Message, userIds?: string[]]> = function (received, userIds) {
  if (!received) {
    return {
      message: () => 'expected message to exist',
      pass: false
    }
  }
  
  const hasMentions = received.mentions && received.mentions.length > 0
  
  if (!userIds) {
    return {
      message: () => `expected message to ${hasMentions ? 'not ' : ''}have mentions`,
      pass: hasMentions
    }
  }
  
  const hasSpecificMentions = userIds.every(id => received.mentions?.includes(id))
  
  return {
    message: () => `expected message to ${hasSpecificMentions ? 'not ' : ''}mention specific users`,
    pass: hasSpecificMentions
  }
}

const toHaveReactions: MatcherFunction<[received: Message]> = function (received) {
  const hasReactions = received && received.reactions && Object.keys(received.reactions).length > 0
  
  return {
    message: () => `expected message to ${hasReactions ? 'not ' : ''}have reactions`,
    pass: !!hasReactions
  }
}

/**
 * 文件相关匹配器
 */
const toBeImageFile: MatcherFunction<[received: FileInfo]> = function (received) {
  const pass = received && received.type.startsWith('image/')
  
  return {
    message: () => `expected file to ${pass ? 'not ' : ''}be image`,
    pass
  }
}

const toBeDocumentFile: MatcherFunction<[received: FileInfo]> = function (received) {
  const documentTypes = ['application/pdf', 'application/msword', 'text/plain']
  const pass = received && documentTypes.some(type => received.type.includes(type))
  
  return {
    message: () => `expected file to ${pass ? 'not ' : ''}be document`,
    pass
  }
}

const toBeVideoFile: MatcherFunction<[received: FileInfo]> = function (received) {
  const pass = received && received.type.startsWith('video/')
  
  return {
    message: () => `expected file to ${pass ? 'not ' : ''}be video`,
    pass
  }
}

const toBeAudioFile: MatcherFunction<[received: FileInfo]> = function (received) {
  const pass = received && received.type.startsWith('audio/')
  
  return {
    message: () => `expected file to ${pass ? 'not ' : ''}be audio`,
    pass
  }
}

const toHaveValidFileStructure: MatcherFunction<[received: FileInfo]> = function (received) {
  const requiredFields = ['id', 'name', 'size', 'type']
  const hasAllFields = requiredFields.every(field => received && received.hasOwnProperty(field))
  const hasValidSize = received && typeof received.size === 'number' && received.size > 0
  const hasValidType = received && typeof received.type === 'string' && received.type.includes('/')
  
  const pass = hasAllFields && hasValidSize && hasValidType
  
  return {
    message: () => `expected file to ${pass ? 'not ' : ''}have valid structure`,
    pass
  }
}

const toBeWithinSizeLimit: MatcherFunction<[received: FileInfo, maxSize: number]> = function (received, maxSize) {
  const pass = received && received.size <= maxSize
  
  return {
    message: () => `expected file size ${received?.size} to ${pass ? 'not ' : ''}be within limit ${maxSize}`,
    pass
  }
}

/**
 * Vue 组件相关匹配器
 */
const toHaveEmittedEvent: MatcherFunction<[received: any, eventName: string]> = function (received, eventName) {
  const emitted = received.emitted && received.emitted()
  const pass = emitted && emitted[eventName] && emitted[eventName].length > 0
  
  return {
    message: () => `expected component to ${pass ? 'not ' : ''}have emitted event '${eventName}'`,
    pass
  }
}

const toHaveEmittedEventWith: MatcherFunction<[received: any, eventName: string, payload: any]> = function (received, eventName, payload) {
  const emitted = received.emitted && received.emitted()
  const hasEvent = emitted && emitted[eventName] && emitted[eventName].length > 0
  
  if (!hasEvent) {
    return {
      message: () => `expected component to have emitted event '${eventName}'`,
      pass: false
    }
  }
  
  const hasPayload = emitted[eventName].some((args: any[]) => 
    JSON.stringify(args[0]) === JSON.stringify(payload)
  )
  
  return {
    message: () => `expected component to ${hasPayload ? 'not ' : ''}have emitted event '${eventName}' with payload`,
    pass: hasPayload
  }
}

const toHaveRenderedComponent: MatcherFunction<[received: any, componentName: string]> = function (received, componentName) {
  const component = received.findComponent({ name: componentName })
  const pass = component.exists()
  
  return {
    message: () => `expected to ${pass ? 'not ' : ''}have rendered component '${componentName}'`,
    pass
  }
}

const toHaveClass: MatcherFunction<[received: any, className: string]> = function (received, className) {
  const pass = received.classes && received.classes().includes(className)
  
  return {
    message: () => `expected element to ${pass ? 'not ' : ''}have class '${className}'`,
    pass
  }
}

const toHaveStyle: MatcherFunction<[received: any, property: string, value: string]> = function (received, property, value) {
  const style = received.element?.style
  const pass = style && style[property] === value
  
  return {
    message: () => `expected element to ${pass ? 'not ' : ''}have style '${property}: ${value}'`,
    pass
  }
}

/**
 * API 响应相关匹配器
 */
const toBeSuccessResponse: MatcherFunction<[received: any]> = function (received) {
  const pass = received && received.success === true && received.data !== undefined
  
  return {
    message: () => `expected response to ${pass ? 'not ' : ''}be success response`,
    pass
  }
}

const toBeErrorResponse: MatcherFunction<[received: any]> = function (received) {
  const pass = received && received.success === false && received.error !== undefined
  
  return {
    message: () => `expected response to ${pass ? 'not ' : ''}be error response`,
    pass
  }
}

const toHavePagination: MatcherFunction<[received: any]> = function (received) {
  const pagination = received?.data?.pagination
  const hasRequiredFields = pagination && 
    typeof pagination.page === 'number' &&
    typeof pagination.pageSize === 'number' &&
    typeof pagination.total === 'number'
  
  return {
    message: () => `expected response to ${hasRequiredFields ? 'not ' : ''}have pagination`,
    pass: !!hasRequiredFields
  }
}

/**
 * WebSocket 相关匹配器
 */
const toBeValidWebSocketEvent: MatcherFunction<[received: any]> = function (received) {
  const hasType = received && typeof received.type === 'string'
  const hasData = received && received.data !== undefined
  const hasTimestamp = received && received.timestamp
  
  const pass = hasType && hasData && hasTimestamp
  
  return {
    message: () => `expected to ${pass ? 'not ' : ''}be valid WebSocket event`,
    pass
  }
}

const toHaveEventType: MatcherFunction<[received: any, eventType: string]> = function (received, eventType) {
  const pass = received && received.type === eventType
  
  return {
    message: () => `expected WebSocket event to ${pass ? 'not ' : ''}have type '${eventType}'`,
    pass
  }
}

/**
 * 注册所有自定义匹配器
 */
expect.extend({
  // 用户相关
  toBeOnlineUser,
  toBeOfflineUser,
  toHaveValidUserStructure,
  
  // 会话相关
  toBePrivateSession,
  toBeGroupSession,
  toBeChannelSession,
  toHaveUnreadMessages,
  toBePinnedSession,
  toBeMutedSession,
  toHaveValidSessionStructure,
  
  // 消息相关
  toBeTextMessage,
  toBeImageMessage,
  toBeFileMessage,
  toBeSystemMessage,
  toBeReplyMessage,
  toHaveValidMessageStructure,
  toBeRecentMessage,
  toHaveMentions,
  toHaveReactions,
  
  // 文件相关
  toBeImageFile,
  toBeDocumentFile,
  toBeVideoFile,
  toBeAudioFile,
  toHaveValidFileStructure,
  toBeWithinSizeLimit,
  
  // Vue 组件相关
  toHaveEmittedEvent,
  toHaveEmittedEventWith,
  toHaveRenderedComponent,
  toHaveClass,
  toHaveStyle,
  
  // API 响应相关
  toBeSuccessResponse,
  toBeErrorResponse,
  toHavePagination,
  
  // WebSocket 相关
  toBeValidWebSocketEvent,
  toHaveEventType
})

/**
 * 导出匹配器函数供其他地方使用
 */
export {
  toBeOnlineUser,
  toBeOfflineUser,
  toHaveValidUserStructure,
  toBePrivateSession,
  toBeGroupSession,
  toBeChannelSession,
  toHaveUnreadMessages,
  toBePinnedSession,
  toBeMutedSession,
  toHaveValidSessionStructure,
  toBeTextMessage,
  toBeImageMessage,
  toBeFileMessage,
  toBeSystemMessage,
  toBeReplyMessage,
  toHaveValidMessageStructure,
  toBeRecentMessage,
  toHaveMentions,
  toHaveReactions,
  toBeImageFile,
  toBeDocumentFile,
  toBeVideoFile,
  toBeAudioFile,
  toHaveValidFileStructure,
  toBeWithinSizeLimit,
  toHaveEmittedEvent,
  toHaveEmittedEventWith,
  toHaveRenderedComponent,
  toHaveClass,
  toHaveStyle,
  toBeSuccessResponse,
  toBeErrorResponse,
  toHavePagination,
  toBeValidWebSocketEvent,
  toHaveEventType
}