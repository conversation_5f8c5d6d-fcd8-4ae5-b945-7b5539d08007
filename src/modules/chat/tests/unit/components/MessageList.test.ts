/**
 * MessageList 组件单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import MessageList from '../../../components/MessageList.vue'
import { useMessageStore } from '../../../stores/message'
import { useSessionStore } from '../../../stores/session'
import { useChatStore } from '../../../stores/chat'
import type { Message } from '../../../types'

// Mock 组件
vi.mock('@/components/ui/Avatar.vue', () => ({
  default: {
    name: 'Avatar',
    template: '<div class="avatar">{{ name?.[0] }}</div>',
    props: ['src', 'name', 'size']
  }
}))

vi.mock('@/components/ui/Icon.vue', () => ({
  default: {
    name: 'Icon',
    template: '<span class="icon">{{ name }}</span>',
    props: ['name']
  }
}))

// Mock 虚拟滚动组件
vi.mock('@/components/ui/VirtualList.vue', () => ({
  default: {
    name: 'VirtualList',
    template: '<div class="virtual-list"><slot v-for="item in items" :item="item" :index="item.index" /></div>',
    props: ['items', 'itemHeight', 'height']
  }
}))

describe('MessageList', () => {
  let wrapper: any
  let messageStore: any
  let sessionStore: any
  let chatStore: any

  const mockMessages: Message[] = [
    {
      id: 'msg1',
      content: 'Hello everyone!',
      senderId: 'user1',
      senderName: 'Alice',
      senderAvatar: 'avatar1.jpg',
      sessionId: 'session1',
      timestamp: new Date('2024-01-15T10:00:00Z'),
      type: 'text',
      status: 'sent',
      isOwn: false
    },
    {
      id: 'msg2',
      content: 'Hi Alice! How are you?',
      senderId: 'user2',
      senderName: 'Bob',
      senderAvatar: 'avatar2.jpg',
      sessionId: 'session1',
      timestamp: new Date('2024-01-15T10:01:00Z'),
      type: 'text',
      status: 'delivered',
      isOwn: true
    },
    {
      id: 'msg3',
      content: 'Check out this image!',
      senderId: 'user1',
      senderName: 'Alice',
      senderAvatar: 'avatar1.jpg',
      sessionId: 'session1',
      timestamp: new Date('2024-01-15T10:02:00Z'),
      type: 'image',
      status: 'read',
      isOwn: false,
      attachments: [{
        id: 'file1',
        name: 'image.jpg',
        url: 'https://example.com/image.jpg',
        type: 'image/jpeg',
        size: 1024000
      }]
    },
    {
      id: 'msg4',
      content: 'User Alice joined the chat',
      senderId: 'system',
      senderName: 'System',
      sessionId: 'session1',
      timestamp: new Date('2024-01-15T09:59:00Z'),
      type: 'system',
      status: 'sent',
      isOwn: false
    },
    {
      id: 'msg5',
      content: 'This is a reply message',
      senderId: 'user2',
      senderName: 'Bob',
      senderAvatar: 'avatar2.jpg',
      sessionId: 'session1',
      timestamp: new Date('2024-01-15T10:03:00Z'),
      type: 'text',
      status: 'sending',
      isOwn: true,
      replyTo: {
        id: 'msg1',
        content: 'Hello everyone!',
        senderName: 'Alice'
      }
    }
  ]

  beforeEach(() => {
    setActivePinia(createPinia())
    
    messageStore = useMessageStore()
    sessionStore = useSessionStore()
    chatStore = useChatStore()
    
    // 设置 mock 数据
    messageStore.messages = mockMessages
    messageStore.selectedMessageIds = []
    messageStore.replyingTo = null
    messageStore.editingMessage = null
    sessionStore.currentSessionId = 'session1'
    chatStore.currentUser = {
      id: 'user2',
      name: 'Bob',
      avatar: 'avatar2.jpg'
    }
    
    // Mock store 方法
    vi.spyOn(messageStore, 'loadMessages').mockResolvedValue(undefined)
    vi.spyOn(messageStore, 'loadMoreMessages').mockResolvedValue(undefined)
    vi.spyOn(messageStore, 'markAsRead').mockResolvedValue(undefined)
    vi.spyOn(messageStore, 'deleteMessage').mockResolvedValue(undefined)
    vi.spyOn(messageStore, 'editMessage').mockResolvedValue(undefined)
    vi.spyOn(messageStore, 'forwardMessage').mockResolvedValue(undefined)
    vi.spyOn(messageStore, 'selectMessage').mockImplementation(() => {})
    vi.spyOn(messageStore, 'unselectMessage').mockImplementation(() => {})
    vi.spyOn(messageStore, 'setReplyingTo').mockImplementation(() => {})
    vi.spyOn(messageStore, 'setEditingMessage').mockImplementation(() => {})
    
    wrapper = mount(MessageList, {
      global: {
        plugins: [createPinia()]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染消息列表结构', () => {
      expect(wrapper.find('.message-list').exists()).toBe(true)
      expect(wrapper.find('.message-list-header').exists()).toBe(true)
      expect(wrapper.find('.message-list-body').exists()).toBe(true)
      expect(wrapper.find('.message-list-footer').exists()).toBe(true)
    })

    it('应该渲染所有消息', () => {
      const messageItems = wrapper.findAll('.message-item')
      expect(messageItems.length).toBeGreaterThan(0)
    })

    it('应该显示会话信息', () => {
      const header = wrapper.find('.message-list-header')
      expect(header.exists()).toBe(true)
    })

    it('应该显示滚动到底部按钮', () => {
      wrapper.vm.showScrollToBottom = true
      wrapper.vm.$nextTick()
      
      expect(wrapper.find('.scroll-to-bottom-btn').exists()).toBe(true)
    })
  })

  describe('消息项渲染', () => {
    it('应该正确显示文本消息', () => {
      const textMessage = wrapper.findAll('.message-item')[0]
      
      expect(textMessage.find('.message-content').text()).toBe(mockMessages[0].content)
      expect(textMessage.find('.sender-name').text()).toBe(mockMessages[0].senderName)
    })

    it('应该正确显示图片消息', () => {
      const imageMessage = wrapper.findAll('.message-item').find(item => 
        item.classes().includes('message-image')
      )
      
      expect(imageMessage?.find('.message-image').exists()).toBe(true)
    })

    it('应该正确显示系统消息', () => {
      const systemMessage = wrapper.findAll('.message-item').find(item => 
        item.classes().includes('message-system')
      )
      
      expect(systemMessage?.exists()).toBe(true)
      expect(systemMessage?.classes()).toContain('message-system')
    })

    it('应该正确显示回复消息', () => {
      const replyMessage = wrapper.findAll('.message-item').find(item => 
        item.find('.reply-info').exists()
      )
      
      expect(replyMessage?.find('.reply-info').exists()).toBe(true)
      expect(replyMessage?.find('.reply-content').text()).toBe(mockMessages[0].content)
    })

    it('应该区分自己和他人的消息', () => {
      const ownMessage = wrapper.findAll('.message-item').find(item => 
        item.classes().includes('message-own')
      )
      const otherMessage = wrapper.findAll('.message-item').find(item => 
        item.classes().includes('message-other')
      )
      
      expect(ownMessage?.exists()).toBe(true)
      expect(otherMessage?.exists()).toBe(true)
    })

    it('应该显示消息状态', () => {
      const messageWithStatus = wrapper.findAll('.message-item').find(item => 
        item.find('.message-status').exists()
      )
      
      expect(messageWithStatus?.find('.message-status').exists()).toBe(true)
    })

    it('应该显示消息时间', () => {
      const messageItem = wrapper.find('.message-item')
      expect(messageItem.find('.message-time').exists()).toBe(true)
    })

    it('应该显示发送者头像', () => {
      const messageItem = wrapper.find('.message-item')
      expect(messageItem.find('.avatar').exists()).toBe(true)
    })
  })

  describe('消息操作', () => {
    it('应该显示消息操作菜单', async () => {
      const messageItem = wrapper.find('.message-item')
      await messageItem.trigger('mouseenter')
      
      expect(wrapper.find('.message-actions').exists()).toBe(true)
    })

    it('应该能够回复消息', async () => {
      const messageItem = wrapper.find('.message-item')
      await messageItem.trigger('mouseenter')
      
      const replyBtn = wrapper.find('.reply-btn')
      await replyBtn.trigger('click')
      
      expect(messageStore.setReplyingTo).toHaveBeenCalledWith(mockMessages[0])
      expect(wrapper.emitted('reply')).toBeTruthy()
    })

    it('应该能够编辑消息', async () => {
      // 选择自己的消息
      const ownMessageItem = wrapper.findAll('.message-item').find(item => 
        item.classes().includes('message-own')
      )
      
      await ownMessageItem?.trigger('mouseenter')
      
      const editBtn = wrapper.find('.edit-btn')
      await editBtn.trigger('click')
      
      expect(messageStore.setEditingMessage).toHaveBeenCalled()
    })

    it('应该能够删除消息', async () => {
      const messageItem = wrapper.find('.message-item')
      await messageItem.trigger('mouseenter')
      
      const deleteBtn = wrapper.find('.delete-btn')
      await deleteBtn.trigger('click')
      
      // 应该显示确认对话框
      expect(wrapper.find('.delete-confirm-dialog').exists()).toBe(true)
      
      // 确认删除
      const confirmBtn = wrapper.find('.confirm-delete-btn')
      await confirmBtn.trigger('click')
      
      expect(messageStore.deleteMessage).toHaveBeenCalledWith(mockMessages[0].id)
    })

    it('应该能够转发消息', async () => {
      const messageItem = wrapper.find('.message-item')
      await messageItem.trigger('mouseenter')
      
      const forwardBtn = wrapper.find('.forward-btn')
      await forwardBtn.trigger('click')
      
      expect(wrapper.find('.forward-dialog').exists()).toBe(true)
    })

    it('应该能够复制消息', async () => {
      // Mock clipboard API
      Object.assign(navigator, {
        clipboard: {
          writeText: vi.fn().mockResolvedValue(undefined)
        }
      })
      
      const messageItem = wrapper.find('.message-item')
      await messageItem.trigger('mouseenter')
      
      const copyBtn = wrapper.find('.copy-btn')
      await copyBtn.trigger('click')
      
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(mockMessages[0].content)
    })

    it('应该能够选择消息', async () => {
      const messageItem = wrapper.find('.message-item')
      
      // 长按选择
      await messageItem.trigger('mousedown')
      
      // 模拟长按
      setTimeout(async () => {
        await messageItem.trigger('mouseup')
        expect(messageStore.selectMessage).toHaveBeenCalledWith(mockMessages[0].id)
      }, 500)
    })
  })

  describe('消息分组', () => {
    it('应该按日期分组消息', () => {
      const dateGroups = wrapper.findAll('.date-group')
      expect(dateGroups.length).toBeGreaterThan(0)
    })

    it('应该显示日期分隔符', () => {
      const dateSeparator = wrapper.find('.date-separator')
      expect(dateSeparator.exists()).toBe(true)
    })

    it('应该合并连续的同一发送者消息', () => {
      const messageGroups = wrapper.findAll('.message-group')
      expect(messageGroups.length).toBeGreaterThan(0)
    })
  })

  describe('滚动功能', () => {
    it('应该能够滚动到底部', async () => {
      const scrollContainer = wrapper.find('.message-list-body')
      vi.spyOn(scrollContainer.element, 'scrollTo').mockImplementation(() => {})
      
      await wrapper.vm.scrollToBottom()
      
      expect(scrollContainer.element.scrollTo).toHaveBeenCalledWith({
        top: scrollContainer.element.scrollHeight,
        behavior: 'smooth'
      })
    })

    it('应该在新消息到达时自动滚动到底部', async () => {
      wrapper.vm.isAtBottom = true
      vi.spyOn(wrapper.vm, 'scrollToBottom').mockImplementation(() => {})
      
      // 添加新消息
      messageStore.messages.push({
        id: 'new-msg',
        content: 'New message',
        senderId: 'user1',
        senderName: 'Alice',
        sessionId: 'session1',
        timestamp: new Date(),
        type: 'text',
        status: 'sent',
        isOwn: false
      })
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.scrollToBottom).toHaveBeenCalled()
    })

    it('应该检测是否在底部', async () => {
      const scrollContainer = wrapper.find('.message-list-body')
      
      // 模拟滚动到底部
      Object.defineProperty(scrollContainer.element, 'scrollTop', {
        value: 1000,
        writable: true
      })
      Object.defineProperty(scrollContainer.element, 'scrollHeight', {
        value: 1100,
        writable: true
      })
      Object.defineProperty(scrollContainer.element, 'clientHeight', {
        value: 500,
        writable: true
      })
      
      await scrollContainer.trigger('scroll')
      
      expect(wrapper.vm.isAtBottom).toBe(true)
    })

    it('应该在滚动到顶部时加载更多消息', async () => {
      const scrollContainer = wrapper.find('.message-list-body')
      
      // 模拟滚动到顶部
      Object.defineProperty(scrollContainer.element, 'scrollTop', {
        value: 0,
        writable: true
      })
      
      await scrollContainer.trigger('scroll')
      
      expect(messageStore.loadMoreMessages).toHaveBeenCalled()
    })
  })

  describe('消息搜索', () => {
    it('应该能够搜索消息', async () => {
      const searchInput = wrapper.find('.search-input')
      await searchInput.setValue('Hello')
      
      expect(wrapper.vm.searchQuery).toBe('Hello')
    })

    it('应该高亮搜索结果', async () => {
      wrapper.vm.searchQuery = 'Hello'
      await wrapper.vm.$nextTick()
      
      const highlightedText = wrapper.find('.highlight')
      expect(highlightedText.exists()).toBe(true)
    })

    it('应该显示搜索结果数量', async () => {
      wrapper.vm.searchQuery = 'Hello'
      await wrapper.vm.$nextTick()
      
      const searchResults = wrapper.find('.search-results-count')
      expect(searchResults.exists()).toBe(true)
    })
  })

  describe('消息选择模式', () => {
    it('应该进入选择模式', async () => {
      await wrapper.vm.enterSelectionMode()
      
      expect(wrapper.vm.isSelectionMode).toBe(true)
      expect(wrapper.find('.selection-toolbar').exists()).toBe(true)
    })

    it('应该退出选择模式', async () => {
      wrapper.vm.isSelectionMode = true
      await wrapper.vm.exitSelectionMode()
      
      expect(wrapper.vm.isSelectionMode).toBe(false)
    })

    it('应该能够全选消息', async () => {
      wrapper.vm.isSelectionMode = true
      await wrapper.vm.$nextTick()
      
      const selectAllBtn = wrapper.find('.select-all-btn')
      await selectAllBtn.trigger('click')
      
      expect(messageStore.selectedMessageIds.length).toBe(mockMessages.length)
    })

    it('应该能够批量删除选中的消息', async () => {
      wrapper.vm.isSelectionMode = true
      messageStore.selectedMessageIds = ['msg1', 'msg2']
      await wrapper.vm.$nextTick()
      
      const deleteSelectedBtn = wrapper.find('.delete-selected-btn')
      await deleteSelectedBtn.trigger('click')
      
      expect(wrapper.find('.delete-confirm-dialog').exists()).toBe(true)
    })
  })

  describe('图片预览', () => {
    it('应该能够预览图片', async () => {
      const imageMessage = wrapper.find('.message-image img')
      await imageMessage.trigger('click')
      
      expect(wrapper.find('.image-preview-modal').exists()).toBe(true)
    })

    it('应该能够关闭图片预览', async () => {
      wrapper.vm.showImagePreview = true
      await wrapper.vm.$nextTick()
      
      const closeBtn = wrapper.find('.close-preview-btn')
      await closeBtn.trigger('click')
      
      expect(wrapper.vm.showImagePreview).toBe(false)
    })
  })

  describe('文件下载', () => {
    it('应该能够下载文件', async () => {
      // Mock URL.createObjectURL
      global.URL.createObjectURL = vi.fn().mockReturnValue('blob:url')
      
      const fileMessage = wrapper.find('.file-attachment')
      const downloadBtn = fileMessage.find('.download-btn')
      
      await downloadBtn.trigger('click')
      
      expect(wrapper.emitted('file-download')).toBeTruthy()
    })
  })

  describe('消息状态', () => {
    it('应该显示发送中状态', () => {
      const sendingMessage = wrapper.findAll('.message-item').find(item => 
        item.find('.status-sending').exists()
      )
      
      expect(sendingMessage?.find('.status-sending').exists()).toBe(true)
    })

    it('应该显示已发送状态', () => {
      const sentMessage = wrapper.findAll('.message-item').find(item => 
        item.find('.status-sent').exists()
      )
      
      expect(sentMessage?.find('.status-sent').exists()).toBe(true)
    })

    it('应该显示已送达状态', () => {
      const deliveredMessage = wrapper.findAll('.message-item').find(item => 
        item.find('.status-delivered').exists()
      )
      
      expect(deliveredMessage?.find('.status-delivered').exists()).toBe(true)
    })

    it('应该显示已读状态', () => {
      const readMessage = wrapper.findAll('.message-item').find(item => 
        item.find('.status-read').exists()
      )
      
      expect(readMessage?.find('.status-read').exists()).toBe(true)
    })
  })

  describe('键盘快捷键', () => {
    it('应该支持 Ctrl+A 全选消息', async () => {
      const messageList = wrapper.find('.message-list')
      
      await messageList.trigger('keydown', {
        key: 'a',
        ctrlKey: true
      })
      
      expect(wrapper.vm.isSelectionMode).toBe(true)
    })

    it('应该支持 Delete 键删除选中消息', async () => {
      wrapper.vm.isSelectionMode = true
      messageStore.selectedMessageIds = ['msg1']
      
      const messageList = wrapper.find('.message-list')
      await messageList.trigger('keydown', { key: 'Delete' })
      
      expect(wrapper.find('.delete-confirm-dialog').exists()).toBe(true)
    })

    it('应该支持 Escape 键退出选择模式', async () => {
      wrapper.vm.isSelectionMode = true
      
      const messageList = wrapper.find('.message-list')
      await messageList.trigger('keydown', { key: 'Escape' })
      
      expect(wrapper.vm.isSelectionMode).toBe(false)
    })
  })

  describe('加载状态', () => {
    it('应该显示加载指示器', async () => {
      messageStore.loading = true
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.loading-indicator').exists()).toBe(true)
    })

    it('应该显示空状态', async () => {
      messageStore.messages = []
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.empty-state').exists()).toBe(true)
    })

    it('应该显示错误状态', async () => {
      messageStore.error = 'Failed to load messages'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.error-state').exists()).toBe(true)
    })
  })

  describe('虚拟滚动', () => {
    it('应该使用虚拟滚动优化性能', () => {
      expect(wrapper.find('.virtual-list').exists()).toBe(true)
    })

    it('应该正确计算可见消息', () => {
      expect(wrapper.vm.visibleMessages.length).toBeGreaterThan(0)
    })
  })
})