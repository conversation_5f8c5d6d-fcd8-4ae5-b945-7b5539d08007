/**
 * 测试设置文件 - 全局测试配置和模拟
 */

import { vi, beforeEach, afterEach } from 'vitest'
import { config } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'

// 全局模拟

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readyState = MockWebSocket.CONNECTING
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null
  url: string
  protocol: string
  bufferedAmount = 0
  extensions = ''
  binaryType: BinaryType = 'blob'

  constructor(url: string, protocols?: string | string[]) {
    this.url = url
    this.protocol = Array.isArray(protocols) ? protocols[0] : protocols || ''
    
    // 模拟异步连接
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN
      this.onopen?.(new Event('open'))
    }, 100)
  }

  send(data: string | ArrayBufferLike | Blob | ArrayBufferView) {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open')
    }
    
    // 模拟服务器响应
    setTimeout(() => {
      try {
        const message = typeof data === 'string' ? JSON.parse(data) : data
        this.simulateServerResponse(message)
      } catch (error) {
        console.warn('Failed to parse WebSocket message:', error)
      }
    }, 50)
  }

  close(code?: number, reason?: string) {
    this.readyState = MockWebSocket.CLOSED
    this.onclose?.(new CloseEvent('close', { code, reason }))
  }

  addEventListener(type: string, listener: EventListener) {
    switch (type) {
      case 'open':
        this.onopen = listener as any
        break
      case 'close':
        this.onclose = listener as any
        break
      case 'message':
        this.onmessage = listener as any
        break
      case 'error':
        this.onerror = listener as any
        break
    }
  }

  removeEventListener(type: string, listener: EventListener) {
    switch (type) {
      case 'open':
        this.onopen = null
        break
      case 'close':
        this.onclose = null
        break
      case 'message':
        this.onmessage = null
        break
      case 'error':
        this.onerror = null
        break
    }
  }

  dispatchEvent(event: Event): boolean {
    switch (event.type) {
      case 'open':
        this.onopen?.(event)
        break
      case 'close':
        this.onclose?.(event as CloseEvent)
        break
      case 'message':
        this.onmessage?.(event as MessageEvent)
        break
      case 'error':
        this.onerror?.(event)
        break
    }
    return true
  }

  private simulateServerResponse(message: any) {
    if (typeof message === 'object' && message.type) {
      switch (message.type) {
        case 'send_message':
          this.onmessage?.(new MessageEvent('message', {
            data: JSON.stringify({
              type: 'message_sent',
              data: {
                id: `msg_${Date.now()}`,
                ...message.data,
                timestamp: new Date().toISOString(),
                status: 'sent'
              }
            })
          }))
          break
        case 'join_session':
          this.onmessage?.(new MessageEvent('message', {
            data: JSON.stringify({
              type: 'session_joined',
              data: { sessionId: message.data.sessionId }
            })
          }))
          break
        case 'typing_start':
          this.onmessage?.(new MessageEvent('message', {
            data: JSON.stringify({
              type: 'user_typing',
              data: {
                userId: message.data.userId,
                sessionId: message.data.sessionId,
                isTyping: true
              }
            })
          }))
          break
      }
    }
  }
}

// Mock Notification API
class MockNotification {
  static permission: NotificationPermission = 'default'
  static requestPermission = vi.fn().mockResolvedValue('granted')
  
  title: string
  body?: string
  icon?: string
  tag?: string
  onclick: ((this: Notification, ev: Event) => any) | null = null
  onclose: ((this: Notification, ev: Event) => any) | null = null
  onerror: ((this: Notification, ev: Event) => any) | null = null
  onshow: ((this: Notification, ev: Event) => any) | null = null

  constructor(title: string, options?: NotificationOptions) {
    this.title = title
    this.body = options?.body
    this.icon = options?.icon
    this.tag = options?.tag
    
    // 模拟显示通知
    setTimeout(() => {
      this.onshow?.(new Event('show'))
    }, 10)
  }

  close() {
    setTimeout(() => {
      this.onclose?.(new Event('close'))
    }, 10)
  }

  addEventListener(type: string, listener: EventListener) {
    switch (type) {
      case 'click':
        this.onclick = listener as any
        break
      case 'close':
        this.onclose = listener as any
        break
      case 'error':
        this.onerror = listener as any
        break
      case 'show':
        this.onshow = listener as any
        break
    }
  }

  removeEventListener(type: string, listener: EventListener) {
    switch (type) {
      case 'click':
        this.onclick = null
        break
      case 'close':
        this.onclose = null
        break
      case 'error':
        this.onerror = null
        break
      case 'show':
        this.onshow = null
        break
    }
  }
}

// Mock Audio API
class MockAudio {
  src = ''
  currentTime = 0
  duration = 0
  paused = true
  volume = 1
  muted = false
  
  play = vi.fn().mockResolvedValue(undefined)
  pause = vi.fn()
  load = vi.fn()
  
  addEventListener = vi.fn()
  removeEventListener = vi.fn()
  
  constructor(src?: string) {
    if (src) {
      this.src = src
    }
  }
}

// Mock URL API
const mockURL = {
  createObjectURL: vi.fn().mockReturnValue('blob:mock-url'),
  revokeObjectURL: vi.fn()
}

// Mock navigator.clipboard
const mockClipboard = {
  writeText: vi.fn().mockResolvedValue(undefined),
  readText: vi.fn().mockResolvedValue(''),
  write: vi.fn().mockResolvedValue(undefined),
  read: vi.fn().mockResolvedValue([])
}

// Mock navigator.vibrate
const mockVibrate = vi.fn().mockReturnValue(true)

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0
}

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0
}

// Mock IntersectionObserver
class MockIntersectionObserver {
  observe = vi.fn()
  disconnect = vi.fn()
  unobserve = vi.fn()
  
  constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
    // Mock implementation
  }
}

// Mock ResizeObserver
class MockResizeObserver {
  observe = vi.fn()
  disconnect = vi.fn()
  unobserve = vi.fn()
  
  constructor(callback: ResizeObserverCallback) {
    // Mock implementation
  }
}

// Mock File API
class MockFile extends File {
  constructor(bits: BlobPart[], name: string, options?: FilePropertyBag) {
    super(bits, name, options)
  }
}

// Mock FileReader
class MockFileReader {
  result: string | ArrayBuffer | null = null
  error: DOMException | null = null
  readyState = FileReader.EMPTY
  
  onload: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null
  onerror: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null
  onprogress: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null
  
  readAsText = vi.fn().mockImplementation(() => {
    this.readyState = FileReader.DONE
    this.result = 'mock file content'
    setTimeout(() => {
      this.onload?.(new ProgressEvent('load') as any)
    }, 10)
  })
  
  readAsDataURL = vi.fn().mockImplementation(() => {
    this.readyState = FileReader.DONE
    this.result = 'data:text/plain;base64,bW9jayBmaWxlIGNvbnRlbnQ='
    setTimeout(() => {
      this.onload?.(new ProgressEvent('load') as any)
    }, 10)
  })
  
  readAsArrayBuffer = vi.fn().mockImplementation(() => {
    this.readyState = FileReader.DONE
    this.result = new ArrayBuffer(8)
    setTimeout(() => {
      this.onload?.(new ProgressEvent('load') as any)
    }, 10)
  })
  
  abort = vi.fn()
  addEventListener = vi.fn()
  removeEventListener = vi.fn()
}

// 设置全局模拟
Object.defineProperty(global, 'WebSocket', {
  value: MockWebSocket,
  writable: true
})

Object.defineProperty(global, 'Notification', {
  value: MockNotification,
  writable: true
})

Object.defineProperty(global, 'Audio', {
  value: MockAudio,
  writable: true
})

Object.defineProperty(global, 'URL', {
  value: mockURL,
  writable: true
})

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

Object.defineProperty(global, 'sessionStorage', {
  value: mockSessionStorage,
  writable: true
})

Object.defineProperty(global, 'IntersectionObserver', {
  value: MockIntersectionObserver,
  writable: true
})

Object.defineProperty(global, 'ResizeObserver', {
  value: MockResizeObserver,
  writable: true
})

Object.defineProperty(global, 'File', {
  value: MockFile,
  writable: true
})

Object.defineProperty(global, 'FileReader', {
  value: MockFileReader,
  writable: true
})

// 设置 navigator 模拟
Object.defineProperty(global.navigator, 'clipboard', {
  value: mockClipboard,
  writable: true
})

Object.defineProperty(global.navigator, 'vibrate', {
  value: mockVibrate,
  writable: true
})

// 设置 window 模拟
Object.defineProperty(global.window, 'matchMedia', {
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  })),
  writable: true
})

// Vue Test Utils 全局配置
config.global.plugins = [
  createPinia(),
  createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', component: { template: '<div>Home</div>' } },
      { path: '/chat', component: { template: '<div>Chat</div>' } },
      { path: '/chat/:sessionId', component: { template: '<div>Chat Session</div>' } }
    ]
  })
]

// 全局测试钩子
beforeEach(() => {
  // 重置所有模拟
  vi.clearAllMocks()
  
  // 重置 localStorage
  mockLocalStorage.getItem.mockReturnValue(null)
  mockLocalStorage.setItem.mockImplementation(() => {})
  mockLocalStorage.removeItem.mockImplementation(() => {})
  mockLocalStorage.clear.mockImplementation(() => {})
  
  // 重置 sessionStorage
  mockSessionStorage.getItem.mockReturnValue(null)
  mockSessionStorage.setItem.mockImplementation(() => {})
  mockSessionStorage.removeItem.mockImplementation(() => {})
  mockSessionStorage.clear.mockImplementation(() => {})
  
  // 重置 Notification 权限
  MockNotification.permission = 'default'
  MockNotification.requestPermission.mockResolvedValue('granted')
  
  // 重置 URL 模拟
  mockURL.createObjectURL.mockReturnValue('blob:mock-url')
  mockURL.revokeObjectURL.mockImplementation(() => {})
  
  // 重置 clipboard 模拟
  mockClipboard.writeText.mockResolvedValue(undefined)
  mockClipboard.readText.mockResolvedValue('')
  
  // 重置 vibrate 模拟
  mockVibrate.mockReturnValue(true)
})

afterEach(() => {
  // 清理定时器
  vi.clearAllTimers()
  
  // 清理模拟
  vi.restoreAllMocks()
})

// 导出测试工具函数
export const createMockWebSocket = () => new MockWebSocket('ws://localhost:3000')
export const createMockNotification = (title: string, options?: NotificationOptions) => 
  new MockNotification(title, options)
export const createMockAudio = (src?: string) => new MockAudio(src)
export const createMockFile = (content: string, name: string, type = 'text/plain') => 
  new MockFile([content], name, { type })

// 测试数据工厂
export const createTestUser = (overrides = {}) => ({
  id: 'test-user-1',
  name: 'Test User',
  email: '<EMAIL>',
  avatar: 'avatar.jpg',
  status: 'online',
  ...overrides
})

export const createTestSession = (overrides = {}) => ({
  id: 'test-session-1',
  name: 'Test Session',
  type: 'group',
  participants: [],
  lastMessage: null,
  unreadCount: 0,
  isPinned: false,
  isMuted: false,
  isArchived: false,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
})

export const createTestMessage = (overrides = {}) => ({
  id: 'test-message-1',
  content: 'Test message',
  senderId: 'test-user-1',
  senderName: 'Test User',
  sessionId: 'test-session-1',
  timestamp: new Date(),
  type: 'text',
  status: 'sent',
  isOwn: true,
  ...overrides
})

export const createTestFile = (overrides = {}) => ({
  id: 'test-file-1',
  name: 'test.jpg',
  type: 'image/jpeg',
  size: 1024,
  url: 'https://example.com/test.jpg',
  ...overrides
})

// 等待工具函数
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const waitForNextTick = () => new Promise(resolve => process.nextTick(resolve))

// 模拟 API 响应
export const mockApiResponse = (data: any, delay = 100) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({ data })
    }, delay)
  })
}

export const mockApiError = (error: string, delay = 100) => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(error))
    }, delay)
  })
}