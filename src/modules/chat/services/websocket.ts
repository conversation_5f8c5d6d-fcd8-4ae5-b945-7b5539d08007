/**
 * WebSocket连接管理器
 */

import type {
  WebSocketConfig,
  WebSocketMessage,
  // WebSocketEventListener, // 暂时注释掉未使用的导入
  WebSocketConnectionInfo,
  WebSocketStats,
  WebSocketQueueItem,
  AuthMessage,
  HeartbeatMessage
} from '../types/websocket'
import { WebSocketMessageType, WebSocketStatus } from '../types/websocket'
import type { SenderType } from '../types/message'
import { WEBSOCKET_CONFIG, ERROR_CODES, EVENT_NAMES } from '../constants'
import { EventEmitter } from '../utils/event-emitter'
import { Logger } from '../utils/logger'

/**
 * WebSocket连接管理器类
 */
export class WebSocketManager extends EventEmitter {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private status: WebSocketStatus = WebSocketStatus.DISCONNECTED
  private reconnectAttempts = 0
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private heartbeatTimeoutTimer: number | null = null
  private messageQueue: WebSocketQueueItem[] = []
  private isAuthenticated = false
  private connectionInfo: WebSocketConnectionInfo
  private stats: WebSocketStats
  private logger: Logger
  private lastPingTime = 0
  private missedHeartbeats = 0

  constructor(config: Partial<WebSocketConfig>) {
    super()
    this.config = this.mergeConfig(config)
    this.logger = new Logger('WebSocketManager')
    this.connectionInfo = this.initConnectionInfo()
    this.stats = this.initStats()
  }

  /**
   * 合并配置
   */
  private mergeConfig(config: Partial<WebSocketConfig>): WebSocketConfig {
    return {
      url: config.url || '',
      protocols: config.protocols,
      reconnect: {
        enabled: true,
        maxAttempts: WEBSOCKET_CONFIG.RECONNECT.MAX_ATTEMPTS,
        interval: WEBSOCKET_CONFIG.RECONNECT.INITIAL_DELAY,
        backoff: 'exponential',
        maxInterval: WEBSOCKET_CONFIG.RECONNECT.MAX_DELAY,
        ...config.reconnect
      },
      heartbeat: {
        enabled: true,
        interval: WEBSOCKET_CONFIG.HEARTBEAT.INTERVAL,
        timeout: WEBSOCKET_CONFIG.HEARTBEAT.TIMEOUT,
        maxMissed: WEBSOCKET_CONFIG.HEARTBEAT.MAX_MISSED,
        ...config.heartbeat
      },
      auth: {
        token: config.auth?.token || '',
        autoAuth: true,
        authTimeout: WEBSOCKET_CONFIG.AUTH.TIMEOUT,
        ...config.auth
      },
      message: {
        queueSize: WEBSOCKET_CONFIG.MESSAGE.QUEUE_SIZE,
        timeout: WEBSOCKET_CONFIG.MESSAGE.TIMEOUT,
        retryAttempts: WEBSOCKET_CONFIG.MESSAGE.RETRY_ATTEMPTS,
        ...config.message
      },
      debug: config.debug || false
    }
  }

  /**
   * 初始化连接信息
   */
  private initConnectionInfo(): WebSocketConnectionInfo {
    return {
      status: WebSocketStatus.DISCONNECTED,
      url: this.config.url,
      reconnect_count: 0,
      message_queue_size: 0,
      bytes_sent: 0,
      bytes_received: 0
    }
  }

  /**
   * 初始化统计信息
   */
  private initStats(): WebSocketStats {
    return {
      connection_count: 0,
      total_messages_sent: 0,
      total_messages_received: 0,
      total_bytes_sent: 0,
      total_bytes_received: 0,
      average_latency: 0,
      error_count: 0,
      reconnect_count: 0,
      uptime: 0
    }
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.status === 'connected' || this.status === 'connecting') {
      this.logger.warn('WebSocket is already connected or connecting')
      return
    }

    try {
      this.setStatus(WebSocketStatus.CONNECTING)
      this.logger.info(`Connecting to WebSocket: ${this.config.url}`)

      this.ws = new WebSocket(this.config.url, this.config.protocols)
      this.setupEventListeners()

      // 等待连接建立
      await this.waitForConnection()
      
      // 自动认证
      if (this.config.auth.autoAuth && this.config.auth.token) {
        await this.authenticate()
      }

      // 启动心跳
      if (this.config.heartbeat.enabled) {
        this.startHeartbeat()
      }

      this.stats.connection_count++
      this.connectionInfo.connected_at = Date.now()
      this.logger.info('WebSocket connected successfully')

    } catch (error) {
      this.logger.error('Failed to connect WebSocket:', error)
      this.handleConnectionError(error)
      throw error
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(code = 1000, reason = 'Normal closure'): void {
    this.logger.info(`Disconnecting WebSocket: ${reason}`)
    
    this.setStatus(WebSocketStatus.DISCONNECTING)
    this.stopReconnect()
    this.stopHeartbeat()
    this.clearMessageQueue()
    this.isAuthenticated = false

    if (this.ws) {
      this.ws.close(code, reason)
      this.ws = null
    }

    this.setStatus(WebSocketStatus.DISCONNECTED)
    this.emit(EVENT_NAMES.WS_DISCONNECTED, { code, reason })
  }

  /**
   * 发送消息
   */
  async sendMessage(message: WebSocketMessage): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('WebSocket is not connected')
    }

    if (!this.isAuthenticated) {
      throw new Error('WebSocket is not authenticated')
    }

    const messageStr = JSON.stringify(message)
    const queueItem: WebSocketQueueItem = {
      id: this.generateMessageId(),
      message,
      timestamp: Date.now(),
      attempts: 0,
      max_attempts: this.config.message.retryAttempts,
      timeout: this.config.message.timeout
    }

    try {
      await this.sendQueueItem(queueItem)
      this.stats.total_messages_sent++
      this.stats.total_bytes_sent += messageStr.length
      this.connectionInfo.bytes_sent += messageStr.length
      
      this.logger.debug('Message sent:', message)
    } catch (error) {
      this.logger.error('Failed to send message:', error)
      throw error
    }
  }

  /**
   * 认证
   */
  private async authenticate(): Promise<void> {
    if (!this.config.auth.token) {
      throw new Error('Authentication token is required')
    }

    const authMessage: AuthMessage = {
      type: WebSocketMessageType.AUTH,
      id: this.generateMessageId(),
      timestamp: Date.now(),
      data: {
        token: this.config.auth.token,
        user_id: 0, // 将从token中解析
        user_type: 'user' as SenderType,
        client_info: {
          platform: navigator.platform,
          version: '1.0.0',
          device_id: this.generateDeviceId()
        }
      }
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Authentication timeout'))
      }, this.config.auth.authTimeout)

      const handleAuthResponse = (message: WebSocketMessage) => {
        clearTimeout(timeout)
        this.off('message', handleAuthResponse)

        if (message.type === WebSocketMessageType.AUTH_SUCCESS) {
          this.isAuthenticated = true
          this.logger.info('Authentication successful')
          resolve()
        } else if (message.type === WebSocketMessageType.AUTH_FAILED) {
          this.logger.error('Authentication failed:', message.data)
          reject(new Error(message.data?.error_message || 'Authentication failed'))
        }
      }

      this.on('message', handleAuthResponse)
      this.ws?.send(JSON.stringify(authMessage))
    })
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.ws) return

    this.ws.onopen = (event) => {
      this.logger.info('WebSocket connection opened')
      this.setStatus(WebSocketStatus.CONNECTED)
      this.reconnectAttempts = 0
      this.emit(EVENT_NAMES.WS_CONNECTED, event)
    }

    this.ws.onclose = (event) => {
      this.logger.info(`WebSocket connection closed: ${event.code} ${event.reason}`)
      this.setStatus(WebSocketStatus.DISCONNECTED)
      this.isAuthenticated = false
      this.stopHeartbeat()
      
      this.emit(EVENT_NAMES.WS_DISCONNECTED, event)
      
      // 自动重连
      if (this.config.reconnect.enabled && !event.wasClean) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (event) => {
      this.logger.error('WebSocket error:', event)
      this.stats.error_count++
      this.stats.last_error = {
        code: ERROR_CODES.WS_CONNECTION_FAILED,
        message: 'WebSocket connection error',
        timestamp: Date.now()
      }
      this.emit(EVENT_NAMES.WS_ERROR, event)
    }

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        this.handleMessage(message)
        
        this.stats.total_messages_received++
        this.stats.total_bytes_received += event.data.length
        this.connectionInfo.bytes_received += event.data.length
        
        this.logger.debug('Message received:', message)
      } catch (error) {
        this.logger.error('Failed to parse WebSocket message:', error)
      }
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    // 处理心跳响应
    if (message.type === WebSocketMessageType.PONG) {
      this.handlePongMessage(message as HeartbeatMessage)
      return
    }

    // 处理认证响应
    if (message.type === WebSocketMessageType.AUTH_SUCCESS || 
        message.type === WebSocketMessageType.AUTH_FAILED) {
      this.emit('message', message)
      return
    }

    // 触发消息接收事件
    this.emit(EVENT_NAMES.WS_MESSAGE_RECEIVED, message)
    this.emit('message', message)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (!this.config.heartbeat.enabled) return

    this.heartbeatTimer = window.setInterval(() => {
      this.sendHeartbeat()
    }, this.config.heartbeat.interval)
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
      this.heartbeatTimeoutTimer = null
    }
  }

  /**
   * 发送心跳
   */
  private sendHeartbeat(): void {
    if (!this.isConnected()) return

    const pingMessage: HeartbeatMessage = {
      type: WebSocketMessageType.PING,
      id: this.generateMessageId(),
      timestamp: Date.now(),
      data: {
        client_time: Date.now()
      }
    }

    this.lastPingTime = Date.now()
    this.ws?.send(JSON.stringify(pingMessage))

    // 设置心跳超时
    this.heartbeatTimeoutTimer = window.setTimeout(() => {
      this.missedHeartbeats++
      this.logger.warn(`Missed heartbeat ${this.missedHeartbeats}/${this.config.heartbeat.maxMissed}`)
      
      if (this.missedHeartbeats >= this.config.heartbeat.maxMissed) {
        this.logger.error('Too many missed heartbeats, reconnecting...')
        this.disconnect(1006, 'Heartbeat timeout')
      }
    }, this.config.heartbeat.timeout)
  }

  /**
   * 处理心跳响应
   */
  private handlePongMessage(_message: HeartbeatMessage): void {
    this.missedHeartbeats = 0
    
    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
      this.heartbeatTimeoutTimer = null
    }

    // 计算延迟
    if (this.lastPingTime > 0) {
      const latency = Date.now() - this.lastPingTime
      this.connectionInfo.latency = latency
      this.updateAverageLatency(latency)
    }

    this.connectionInfo.last_pong = Date.now()
  }

  /**
   * 更新平均延迟
   */
  private updateAverageLatency(latency: number): void {
    const alpha = 0.1 // 平滑因子
    this.stats.average_latency = this.stats.average_latency * (1 - alpha) + latency * alpha
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.reconnect.maxAttempts) {
      this.logger.error('Max reconnect attempts reached')
      this.emit(EVENT_NAMES.WS_ERROR) // 使用存在的事件名称
      return
    }

    const delay = this.calculateReconnectDelay()
    this.reconnectAttempts++
    this.stats.reconnect_count++
    this.connectionInfo.reconnect_count++

    this.logger.info(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.config.reconnect.maxAttempts} in ${delay}ms`)
    
    this.setStatus(WebSocketStatus.RECONNECTING)
    this.emit(EVENT_NAMES.WS_RECONNECTING, { attempt: this.reconnectAttempts, delay })

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch((error) => {
        this.logger.error('Reconnect failed:', error)
        this.scheduleReconnect()
      })
    }, delay)
  }

  /**
   * 计算重连延迟
   */
  private calculateReconnectDelay(): number {
    const { interval, backoff, maxInterval } = this.config.reconnect
    let delay = interval

    if (backoff === 'exponential') {
      delay = Math.min(interval * Math.pow(2, this.reconnectAttempts - 1), maxInterval)
    } else {
      delay = Math.min(interval * this.reconnectAttempts, maxInterval)
    }

    // 添加抖动
    // if (this.config.reconnect.jitter) { // 属性不存在，暂时注释掉
    //   delay += Math.random() * 1000
    // }

    return delay
  }

  /**
   * 停止重连
   */
  private stopReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 等待连接建立
   */
  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'))
      }, 10000)

      const handleOpen = () => {
        clearTimeout(timeout)
        resolve()
      }

      const handleError = (error: Event) => {
        clearTimeout(timeout)
        reject(error)
      }

      this.ws?.addEventListener('open', handleOpen, { once: true })
      this.ws?.addEventListener('error', handleError, { once: true })
    })
  }

  /**
   * 发送队列项
   */
  private async sendQueueItem(item: WebSocketQueueItem): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected')
    }

    try {
      const messageStr = JSON.stringify(item.message)
      this.ws.send(messageStr)
      
      // 从队列中移除
      const index = this.messageQueue.indexOf(item)
      if (index > -1) {
        this.messageQueue.splice(index, 1)
      }
      
      this.updateQueueSize()
    } catch (error) {
      item.attempts++
      
      if (item.attempts < item.max_attempts) {
        // 重试
        setTimeout(() => {
          this.sendQueueItem(item).catch(() => {
            // 最终失败
            this.removeFromQueue(item)
          })
        }, 1000 * item.attempts)
      } else {
        this.removeFromQueue(item)
        throw error
      }
    }
  }

  /**
   * 从队列中移除项目
   */
  private removeFromQueue(item: WebSocketQueueItem): void {
    const index = this.messageQueue.indexOf(item)
    if (index > -1) {
      this.messageQueue.splice(index, 1)
      this.updateQueueSize()
    }
  }

  /**
   * 清空消息队列
   */
  private clearMessageQueue(): void {
    this.messageQueue = []
    this.updateQueueSize()
  }

  /**
   * 更新队列大小
   */
  private updateQueueSize(): void {
    this.connectionInfo.message_queue_size = this.messageQueue.length
  }

  /**
   * 处理连接错误
   */
  private handleConnectionError(error: any): void {
    this.setStatus(WebSocketStatus.ERROR)
    this.stats.error_count++
    this.stats.last_error = {
      code: ERROR_CODES.WS_CONNECTION_FAILED,
      message: error.message || 'Connection failed',
      timestamp: Date.now()
    }
  }

  /**
   * 设置连接状态
   */
  private setStatus(status: WebSocketStatus): void {
    this.status = status
    this.connectionInfo.status = status
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成设备ID
   */
  private generateDeviceId(): string {
    return `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 公共方法

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN && this.status === 'connected'
  }

  /**
   * 检查是否已认证
   */
  isAuth(): boolean {
    return this.isAuthenticated
  }

  /**
   * 获取连接状态
   */
  getStatus(): WebSocketStatus {
    return this.status
  }

  /**
   * 获取连接信息
   */
  getConnectionInfo(): WebSocketConnectionInfo {
    return { ...this.connectionInfo }
  }

  /**
   * 获取统计信息
   */
  getStats(): WebSocketStats {
    return { ...this.stats }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<WebSocketConfig>): void {
    this.config = this.mergeConfig({ ...this.config, ...config })
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    this.disconnect()
    this.removeAllListeners()
    this.logger.info('WebSocketManager destroyed')
  }
}

// 导出单例实例
let instance: WebSocketManager | null = null

export function createWebSocketManager(config: Partial<WebSocketConfig>): WebSocketManager {
  if (instance) {
    instance.destroy()
  }
  instance = new WebSocketManager(config)
  return instance
}

export function getWebSocketManager(): WebSocketManager | null {
  return instance
}