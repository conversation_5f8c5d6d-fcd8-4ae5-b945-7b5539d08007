/**
 * Playwright 全局设置文件
 */

import { chromium, FullConfig } from '@playwright/test'
import path from 'path'
import fs from 'fs'

/**
 * 全局设置函数
 * 在所有测试运行前执行一次
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for chat module e2e tests...')
  
  // 确保测试结果目录存在
  const testResultsDir = path.join(__dirname, '../test-results')
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true })
  }
  
  // 确保测试数据目录存在
  const testDataDir = path.join(__dirname, '../test-data')
  if (!fs.existsSync(testDataDir)) {
    fs.mkdirSync(testDataDir, { recursive: true })
  }
  
  // 创建认证状态
  await createAuthState(testDataDir)
  
  // 设置测试数据库（如果需要）
  await setupTestDatabase()
  
  // 启动模拟服务器（如果需要）
  await startMockServer()
  
  console.log('✅ Global setup completed successfully')
}

/**
 * 创建认证状态
 * 用于需要登录的测试
 */
async function createAuthState(testDataDir: string) {
  console.log('📝 Creating authentication state...')
  
  const browser = await chromium.launch()
  const context = await browser.newContext()
  const page = await context.newPage()
  
  try {
    // 导航到登录页面
    const baseURL = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173'
    await page.goto(`${baseURL}/login`)
    
    // 模拟登录 API 响应
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: {
              id: 'test-user-1',
              name: 'Test User',
              email: '<EMAIL>',
              avatar: 'avatar.jpg',
              status: 'online'
            },
            token: 'mock-jwt-token'
          }
        })
      })
    })
    
    // 模拟用户信息 API
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            id: 'test-user-1',
            name: 'Test User',
            email: '<EMAIL>',
            avatar: 'avatar.jpg',
            status: 'online'
          }
        })
      })
    })
    
    // 执行登录
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // 等待登录完成
    await page.waitForURL('**/chat', { timeout: 10000 })
    
    // 保存认证状态
    const authStatePath = path.join(testDataDir, 'auth-state.json')
    await context.storageState({ path: authStatePath })
    
    console.log('✅ Authentication state created successfully')
  } catch (error) {
    console.error('❌ Failed to create authentication state:', error)
    throw error
  } finally {
    await browser.close()
  }
}

/**
 * 设置测试数据库
 */
async function setupTestDatabase() {
  console.log('🗄️ Setting up test database...')
  
  try {
    // 这里可以添加数据库初始化逻辑
    // 例如：创建测试数据、清理旧数据等
    
    // 模拟数据库设置
    const testData = {
      users: [
        {
          id: 'test-user-1',
          name: 'Alice',
          email: '<EMAIL>',
          avatar: 'alice-avatar.jpg',
          status: 'online'
        },
        {
          id: 'test-user-2',
          name: 'Bob',
          email: '<EMAIL>',
          avatar: 'bob-avatar.jpg',
          status: 'offline'
        }
      ],
      sessions: [
        {
          id: 'test-session-1',
          name: 'General Chat',
          type: 'group',
          participants: ['test-user-1', 'test-user-2'],
          createdAt: new Date().toISOString()
        }
      ],
      messages: [
        {
          id: 'test-message-1',
          content: 'Hello, world!',
          senderId: 'test-user-1',
          sessionId: 'test-session-1',
          timestamp: new Date().toISOString(),
          type: 'text'
        }
      ]
    }
    
    // 保存测试数据到文件
    const testDataPath = path.join(__dirname, '../test-data/database.json')
    fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2))
    
    console.log('✅ Test database setup completed')
  } catch (error) {
    console.error('❌ Failed to setup test database:', error)
    throw error
  }
}

/**
 * 启动模拟服务器
 */
async function startMockServer() {
  console.log('🌐 Starting mock server...')
  
  try {
    // 这里可以启动一个模拟的 WebSocket 服务器
    // 用于测试实时通信功能
    
    // 模拟 WebSocket 服务器配置
    const mockServerConfig = {
      port: 3001,
      host: 'localhost',
      protocols: ['chat-protocol']
    }
    
    // 保存模拟服务器配置
    const configPath = path.join(__dirname, '../test-data/mock-server-config.json')
    fs.writeFileSync(configPath, JSON.stringify(mockServerConfig, null, 2))
    
    console.log('✅ Mock server configuration saved')
  } catch (error) {
    console.error('❌ Failed to start mock server:', error)
    throw error
  }
}

/**
 * 创建测试固件文件
 */
async function createTestFixtures() {
  console.log('📁 Creating test fixtures...')
  
  const fixturesDir = path.join(__dirname, '../fixtures')
  if (!fs.existsSync(fixturesDir)) {
    fs.mkdirSync(fixturesDir, { recursive: true })
  }
  
  // 创建测试图片文件
  const testImageContent = Buffer.from(
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    'base64'
  )
  fs.writeFileSync(path.join(fixturesDir, 'test-image.png'), testImageContent)
  
  // 创建测试文档文件
  const testDocContent = 'This is a test document for file upload testing.'
  fs.writeFileSync(path.join(fixturesDir, 'test-document.txt'), testDocContent)
  
  // 创建大文件用于测试上传进度
  const largeFileContent = 'x'.repeat(1024 * 1024) // 1MB
  fs.writeFileSync(path.join(fixturesDir, 'large-file.txt'), largeFileContent)
  
  console.log('✅ Test fixtures created successfully')
}

/**
 * 设置环境变量
 */
function setupEnvironmentVariables() {
  console.log('🔧 Setting up environment variables...')
  
  // 设置测试环境变量
  process.env.NODE_ENV = 'test'
  process.env.VITE_API_BASE_URL = 'http://localhost:3000/api'
  process.env.VITE_WS_URL = 'ws://localhost:3001/ws'
  process.env.VITE_UPLOAD_URL = 'http://localhost:3000/upload'
  
  console.log('✅ Environment variables set successfully')
}

/**
 * 清理旧的测试结果
 */
function cleanupOldResults() {
  console.log('🧹 Cleaning up old test results...')
  
  const testResultsDir = path.join(__dirname, '../test-results')
  if (fs.existsSync(testResultsDir)) {
    // 删除旧的测试报告
    const files = fs.readdirSync(testResultsDir)
    files.forEach(file => {
      const filePath = path.join(testResultsDir, file)
      if (fs.statSync(filePath).isDirectory()) {
        fs.rmSync(filePath, { recursive: true, force: true })
      } else {
        fs.unlinkSync(filePath)
      }
    })
  }
  
  console.log('✅ Old test results cleaned up')
}

/**
 * 验证测试环境
 */
function validateTestEnvironment() {
  console.log('🔍 Validating test environment...')
  
  // 检查必要的环境变量
  const requiredEnvVars = [
    'NODE_ENV',
    'VITE_API_BASE_URL',
    'VITE_WS_URL'
  ]
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`)
  }
  
  // 检查基础 URL 是否可访问
  const baseURL = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173'
  console.log(`Base URL: ${baseURL}`)
  
  console.log('✅ Test environment validation passed')
}

// 主设置函数
export default async function setup(config: FullConfig) {
  try {
    // 设置环境变量
    setupEnvironmentVariables()
    
    // 验证测试环境
    validateTestEnvironment()
    
    // 清理旧结果
    cleanupOldResults()
    
    // 创建测试固件
    await createTestFixtures()
    
    // 执行全局设置
    await globalSetup(config)
    
    console.log('🎉 All setup tasks completed successfully!')
  } catch (error) {
    console.error('💥 Global setup failed:', error)
    process.exit(1)
  }
}