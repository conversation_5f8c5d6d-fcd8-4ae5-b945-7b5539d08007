<template>
  <div class="payment-page-container">
    <el-card class="payment-card" v-if="orderInfo">
      <template #header>
        <div class="card-header">
          <span>订单支付</span>
        </div>
      </template>

      <!-- 订单信息 -->
      <div class="payment-order-info">
        <h3>订单信息</h3>
        <div class="order-details">
          <div class="order-row">
            <span class="label">订单号:</span>
            <span class="value">{{ orderInfo.orderNumber }}</span>
          </div>
          <div class="order-row" v-if="orderInfo.merchantName">
            <span class="label">商家:</span>
            <span class="value">{{ orderInfo.merchantName }}</span>
          </div>
          <div class="order-row">
            <span class="label">下单时间:</span>
            <span class="value">{{ formatDate(orderInfo.createdAt) }}</span>
          </div>
          <div class="order-row">
            <span class="label">支付金额:</span>
            <span class="value amount">¥{{ orderInfo?.payAmount?.toFixed(2) || '0.00' }}</span>
          </div>
          <div class="order-row" v-if="orderInfo?.totalAmount > orderInfo?.payAmount">
            <span class="label">商品原价:</span>
            <span class="value">¥{{ orderInfo?.totalAmount?.toFixed(2) || '0.00' }}</span>
          </div>
        </div>
      </div>

      <!-- 支付方式选择 -->
      <div class="payment-methods" v-if="!showPaymentInterface">
        <h3>选择支付方式</h3>
        <el-radio-group v-model="selectedPaymentMethod" class="payment-method-group">
          <el-radio
            v-for="method in paymentMethods"
            :key="method.value"
            :label="method.label"
            :value="method.value"
            class="payment-method-item"
            border
          >
            <div class="method-content">
              <el-icon class="method-icon">
                <component :is="method.icon" />
              </el-icon>
              <span class="method-text">{{ method.label }}</span>
            </div>
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 支付界面 -->
      <div class="payment-interface" v-if="showPaymentInterface && paymentInfo">
        <div class="payment-header">
          <h3>{{ getPaymentMethodText(paymentInfo.paymentMethod) }}</h3>
          <div class="payment-amount">支付金额: ¥{{ paymentInfo.paymentAmount.toFixed(2) }}</div>
        </div>

        <!-- 二维码支付 -->
        <div class="qr-payment" v-if="paymentInfo.qrCode || paymentInfo.codeUrl">
          <div class="qr-container">
            <div class="qr-code">
              <img v-if="paymentInfo.codeUrl" :src="paymentInfo.codeUrl" alt="支付二维码" />
              <div v-else class="qr-placeholder">
                <el-icon size="48"><QrCode /></el-icon>
                <p>二维码生成中...</p>
              </div>
            </div>
          </div>
          
          <div class="payment-tips">
            <p>请使用{{ getPaymentMethodText(paymentInfo.paymentMethod) }}扫描上方二维码完成支付</p>
            <div class="countdown" v-if="countdown > 0">
              支付剩余时间: {{ formatCountdown(countdown) }}
            </div>
          </div>
        </div>

        <!-- 支付说明 -->
        <div class="payment-notes">
          <ul>
            <li>请在15分钟内完成支付</li>
            <li>支付成功后将自动跳转到订单详情</li>
            <li>如遇问题请联系客服</li>
          </ul>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="page-footer">
        <template v-if="!showPaymentInterface">
          <el-button @click="handleCancelPayment">取消订单</el-button>
          <el-button 
            type="primary" 
            :loading="paymentCreationLoading"
            @click="handleCreatePayment"
            :disabled="!orderInfo"
          >
            立即支付 ¥{{ orderInfo?.payAmount?.toFixed(2) || '0.00' }}
          </el-button>
        </template>
        <template v-else>
          <el-button @click="handleRefreshQR" :loading="paymentCreationLoading">
            刷新二维码
          </el-button>
          <el-button @click="handleBackToMethodSelect">
            选择其他支付方式
          </el-button>
          <el-button @click="handleCancelPayment">取消支付</el-button>
        </template>
      </div>
    </el-card>
    <el-skeleton :rows="10" animated v-else />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, ElCard, ElSkeleton } from 'element-plus';
import { Wallet } from '@element-plus/icons-vue'; // QrCode, CreditCard, Money, Grid 移出，QrCode通常全局或自定义
import {
  PaymentMethod,
  PaymentStatus,
  type PaymentInfo,
  createTakeoutPayment,
  getTakeoutPaymentStatus,
  getPaymentMethodText,
  type CreatePaymentRequest
} from '@/modules/user/api/payment'; // 使用相对路径
import { getOrderDetail } from '@/modules/user/api/takeout'; // 使用相对路径

// 订单信息接口 (与PaymentDialog.vue中类似，但移除了orderType作为固定值)
// 扩展订单信息接口，增加更多字段以匹配API返回数据
interface OrderInfo {
  // 原有的标准字段
  id: number; // 对应 orderID
  orderNumber: string; // 对应 orderNo
  totalAmount: number; // 总金额
  createdAt: string; // 对应 createTime
  merchantName?: string; // 商家名称（可能为空）
  
  // 扩展字段，适配后端API的返回值
  orderID?: number; // 订单ID
  orderNo?: string; // 订单编号
  createTime?: string; // 创建时间
  payAmount: number; // 支付金额（实际需支付金额）
  deliveryFee?: number; // 配送费
  discountAmount?: number; // 折扣金额
  payStatus?: number; // 支付状态
  orderStatus?: number; // 订单状态
  // 其他可能用到的字段可根据需要添加
}

const route = useRoute();
const router = useRouter();

// 响应式数据
const orderId = ref<number | null>(null);
const orderInfo = ref<OrderInfo | null>(null);
const orderLoading = ref(false);
const paymentCreationLoading = ref(false);
const showPaymentInterface = ref(false);
const selectedPaymentMethod = ref<PaymentMethod>(PaymentMethod.WECHAT);
const paymentInfo = ref<PaymentInfo | null>(null);
const countdown = ref(0);
const pollingTimer = ref<ReturnType<typeof setInterval> | null>(null);
const countdownTimer = ref<ReturnType<typeof setInterval> | null>(null);

// 支付方式配置 (与PaymentDialog.vue一致)
const paymentMethods = [
  {
    value: PaymentMethod.WECHAT,
    label: '微信支付',
    icon: Wallet
  },
  {
    value: PaymentMethod.ALIPAY,
    label: '支付宝',
    icon: Wallet
  },
  // 根据需要保留或移除其他支付方式
  // {
  //   value: PaymentMethod.CREDITCARD,
  //   label: '信用卡',
  //   icon: CreditCard
  // },
  // {
  //   value: PaymentMethod.BANK_TRANSFER,
  //   label: '银行转账',
  //   icon: Money
  // },
  {
    value: PaymentMethod.BALANCE,
    label: '余额支付',
    icon: Wallet
  },
  // {
  //   value: PaymentMethod.COMBINATION,
  //   label: '组合支付',
  //   icon: Grid
  // }
];

/**
 * 获取订单信息
 */
async function fetchOrderInfo() {
  if (!orderId.value) return;
  orderLoading.value = true;
  try {
    // 使用类型断言将API返回数据明确指定为我们需要的对象类型
    // 定义一个临时类型，用于描述API返回的原始数据结构
    interface TakeoutOrderResponse {
      orderID: number;
      orderNo: string;
      createTime: string;
      totalAmount: number;
      payAmount: number;
      deliveryFee: number;
      discountAmount: number;
      payStatus: number;
      orderStatus: number;
      [key: string]: any; // 允许其他可能存在的字段
    }

    // 直接获取订单数据并使用类型断言
    const orderData = await getOrderDetail(orderId.value) as unknown as TakeoutOrderResponse;
    console.log('获取订单信息:', orderData);
    
    // 创建符合OrderInfo接口的对象
    orderInfo.value = {
      // 模板所需的标准字段
      id: orderData.orderID,
      orderNumber: orderData.orderNo,
      createdAt: orderData.createTime,
      merchantName: '',  // 如果原数据中无商家名称，设置为空字符串
      totalAmount: orderData.totalAmount,
      payAmount: orderData.payAmount, // 实际支付金额
      
      // 保留API返回的原始字段，方便后续处理
      orderID: orderData.orderID,
      orderNo: orderData.orderNo,
      createTime: orderData.createTime,
      deliveryFee: orderData.deliveryFee,
      discountAmount: orderData.discountAmount,
      payStatus: orderData.payStatus,
      orderStatus: orderData.orderStatus
    };

    // 如果订单已支付，跳转到订单列表
    if (orderData.payStatus === 1) {
      ElMessage.warning('该订单已支付，即将跳转到订单列表');
      setTimeout(() => {
        router.push('/user/orders');
      }, 1500);
      return; // 已支付订单直接返回
    }
    
    // 如果订单状态异常，也跳转到订单列表
    // 从控制台打印的数据来看，10可能代表待支付状态
    if (orderData.orderStatus !== 10) { // 10可能是待支付状态
      ElMessage.warning('订单状态异常，即将跳转到订单列表');
      setTimeout(() => {
        router.push('/user/orders');
      }, 1500);
    }

  } catch (error) {
    console.error('获取订单信息失败:', error);
    ElMessage.error('获取订单信息失败，请稍后重试或返回上一页');
    // 失败时跳转到订单列表
    setTimeout(() => {
      router.push('/user/orders');
    }, 2000);
  } finally {
    orderLoading.value = false;
  }
}

/**
 * 重置支付状态
 */
function resetPaymentState() {
  showPaymentInterface.value = false;
  selectedPaymentMethod.value = PaymentMethod.WECHAT;
  paymentInfo.value = null;
  countdown.value = 0;
  clearTimers();
}

/**
 * 清除定时器
 */
function clearTimers() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
}

/**
 * 创建支付
 */
async function handleCreatePayment() {
  if (!orderInfo.value) return;
  
  try {
    paymentCreationLoading.value = true;
    
    const params: CreatePaymentRequest = {
      payment_method: selectedPaymentMethod.value,
      payment_amount: orderInfo.value.totalAmount
    };
    const paymentResult = await createTakeoutPayment(orderInfo.value.id, params);
    
    // 检查支付结果（注意：响应拦截器已经返回了data部分）
    if (!paymentResult) {
      ElMessage.error('支付创建失败：服务器响应异常');
      return;
    }
    
    paymentInfo.value = {
      paymentId: paymentResult.paymentID,
      orderId: orderId.value || 0,
      paymentMethod: selectedPaymentMethod.value,
      paymentAmount: orderInfo.value?.totalAmount || 0,
      status: PaymentStatus.PENDING,
      qrCode: paymentResult.qrCodeURL,
      codeUrl: paymentResult.paymentURL,
      expiresAt: paymentResult.expireTime.toString()
    };
    
    // 检查是否为余额支付且已成功
    const appPayParams = paymentResult.appPayParams;
    const webPayParams = paymentResult.webPayParams;
    
    if (appPayParams || webPayParams) {
      try {
        const payParamsStr = appPayParams || webPayParams;
        if (payParamsStr) {
          const payParams = JSON.parse(payParamsStr);
        if (payParams.status === 'success') {
          // 余额支付成功，直接处理成功逻辑
          handlePaymentSuccess({
            paymentAmount: orderInfo.value?.totalAmount || 0,
            paymentMethod: selectedPaymentMethod.value,
            transactionNo: paymentResult.transactionNo
          });
          return;
        }
        }
      } catch (e) {
        console.warn('解析支付参数失败:', e);
      }
    }
    
    showPaymentInterface.value = true;
    startCountdown();
    startPaymentPolling();
    
  } catch (error) {
    console.error('创建支付失败:', error);
    // 修改：不仅显示ElMessage，还调用handlePaymentFailed函数显示确认弹窗
    // ElMessage.error('创建支付失败，'+ error);
    handlePaymentFailed({
      error_message: error instanceof Error ? error.message : '创建支付请求失败',
      status: PaymentStatus.FAILED
    });
  } finally {
    paymentCreationLoading.value = false;
  }
}

/**
 * 开始倒计时
 */
function startCountdown() {
  if (!paymentInfo.value?.expiresAt) return;
  
  const expiresTime = new Date(paymentInfo.value.expiresAt).getTime();
  
  countdownTimer.value = setInterval(() => {
    const now = Date.now();
    const remaining = Math.max(0, expiresTime - now);
    countdown.value = Math.floor(remaining / 1000);
    
    if (remaining <= 0) {
      handlePaymentTimeout();
    }
  }, 1000);
}

/**
 * 开始轮询支付状态
 */
function startPaymentPolling() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }
  
  pollingTimer.value = setInterval(async () => {
    await checkPaymentStatus();
  }, 3000); // 每3秒查询一次
}

/**
 * 检查支付状态
 */
async function checkPaymentStatus() {
  if (!paymentInfo.value || !orderInfo.value) return;
  
  try {
    const statusResult = await getTakeoutPaymentStatus(paymentInfo.value.paymentId);
    const status = statusResult.status;
    
    if (paymentInfo.value) { // 确保 paymentInfo 仍然存在
        paymentInfo.value.status = status;
    }
    
    if (status === PaymentStatus.PAID) {
      handlePaymentSuccess(statusResult);
    } else if (status === PaymentStatus.FAILED) {
      handlePaymentFailed(statusResult);
    }
    
  } catch (error) {
    console.error('查询支付状态失败:', error);
    // 轮询时一般不提示用户错误，避免过多打扰
  }
}

/**
 * 处理支付成功
 */
function handlePaymentSuccess(paymentData: any) {
  clearTimers();
  
  ElMessageBox.alert(
    `支付成功！\n订单号：${orderInfo.value?.orderNumber}\n支付金额：¥${paymentData.payAmount?.toFixed(2)}\n支付方式：${getPaymentMethodText(paymentData.paymentMethod)}\n交易流水：${paymentData.transactionNo}`,
    '支付成功',
    {
      confirmButtonText: '查看订单',
      type: 'success',
      callback: () => {
        // 跳转到外卖订单详情页
        router.push(`/user/orders/`);
      }
    }
  );
}

/**
 * 处理支付失败
 */
function handlePaymentFailed(paymentData: any) {
  console.log('支付失败:', paymentData);
  clearTimers();
  
  // 提取错误信息，优先使用error_message字段，如果没有则尝试其他可能包含错误描述的字段
  let errorMessage = '未知错误';
  if (paymentData) {
    if (typeof paymentData === 'string') {
      errorMessage = paymentData;
    } else if (typeof paymentData === 'object') {
      errorMessage = paymentData.error_message || paymentData.errorMessage || paymentData.message || '未知错误';
    }
  }
  
  ElMessageBox.confirm(
    `支付失败\n失败原因：${errorMessage}\n是否尝试重新支付？`,
    '支付失败',
    {
      confirmButtonText: '重新支付',
      cancelButtonText: '取消',
      type: 'error'
    }
  ).then(() => {
    resetPaymentState(); // 重置到选择支付方式界面
    // 用户可能会选择不同支付方式，所以不直接调用 handleCreatePayment
  }).catch(() => {
    // 用户取消，可以考虑跳转到订单列表
    // router.push(`/user/takeout/orders`);
  });
}

/**
 * 处理支付超时
 */
function handlePaymentTimeout() {
  clearTimers();
  showPaymentInterface.value = false; // 返回到选择支付方式的界面或订单信息界面
  
  ElMessageBox.alert(
    `支付已超时\n订单号：${orderInfo.value?.orderNumber}\n订单支付已超时，请重新创建支付或检查订单状态。`,
    '支付超时',
    {
      confirmButtonText: '确定',
      type: 'warning',
      callback: () => {
        // 可以选择刷新订单信息或返回订单列表
        fetchOrderInfo(); 
      }
    }
  );
}

/**
 * 刷新二维码 (重新创建支付)
 */
function handleRefreshQR() {
  if (orderInfo.value) {
    handleCreatePayment(); 
  }
}

/**
 * 返回支付方式选择
 */
function handleBackToMethodSelect() {
  clearTimers();
  showPaymentInterface.value = false;
  paymentInfo.value = null; // 清除旧的支付信息
}

/**
 * 取消支付/订单
 */
function handleCancelPayment() {
  ElMessageBox.confirm(
    '确定要取消支付并返回订单列表吗？',
    '取消支付',
    {
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
      type: 'warning'
    }
  ).then(() => {
    clearTimers();
    // TODO: 如果有需要，可以调用API取消订单或支付意图
    router.push('/user/takeout/orders'); // 跳转到外卖订单列表
  }).catch(() => {});
}

/**
 * 格式化日期
 */
function formatDate(dateString: string): string {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 格式化倒计时
 */
function formatCountdown(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 组件挂载时
onMounted(() => {
  const idFromRoute = route.params.orderId;
  if (idFromRoute) {
    orderId.value = Number(idFromRoute);
    fetchOrderInfo();
  } else {
    ElMessage.error('无效的订单ID');
    router.push('/user/takeout/orders'); // 跳转到订单列表
  }
});

// 组件卸载时清除定时器
onUnmounted(() => {
  clearTimers();
});

</script>

<style scoped>
.payment-page-container {
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 改为flex-start以防页面内容过少时垂直居中 */
  padding: 20px;
  min-height: calc(100vh - 100px); /* 减去header等高度 */
  background-color: #f4f6f8;
}

.payment-card {
  width: 100%;
  max-width: 700px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.payment-order-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.payment-order-info h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-row .label {
  color: #555;
  font-size: 14px;
}

.order-row .value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.order-row .value.amount {
  color: #e74c3c;
  font-size: 16px;
  font-weight: bold;
}

.payment-methods {
  margin-bottom: 24px;
}

.payment-methods h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.payment-method-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.payment-method-item {
  margin: 0 !important; /* 覆盖 el-radio 默认 margin */
  /* padding: 12px 16px; */ /* el-radio[border]自带padding */
  /* border: 1px solid #e0e0e0; */ /* el-radio[border]自带border */
  border-radius: 8px;
  transition: all 0.3s ease;
}

.payment-method-item:hover {
  border-color: #409eff;
  /* background-color: #f0f8ff; */ /* el-radio[border]自带hover效果 */
}

.payment-method-item.is-checked {
  border-color: #409eff;
  /* background-color: #e6f3ff; */ /* el-radio[border]自带选中效果 */
}

.method-content {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0; /* 微调内部元素间距 */
}

.method-icon {
  font-size: 22px;
  color: #409eff;
}

.method-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.payment-interface {
  text-align: center;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #fff;
}

.payment-header {
  margin-bottom: 20px;
}

.payment-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.payment-amount {
  font-size: 16px;
  color: #e74c3c;
  font-weight: bold;
}

.qr-payment {
  margin-bottom: 20px;
}

.qr-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.qr-code {
  width: 220px;
  height: 220px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 10px; /* 内边距，让二维码不贴边 */
}

.qr-code img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #999;
}

.qr-placeholder .el-icon {
  font-size: 56px !important; /* 确保图标够大 */
}

.qr-placeholder p {
  margin: 0;
  font-size: 14px;
}

.payment-tips {
  color: #555;
  font-size: 14px;
}

.payment-tips p {
  margin: 0 0 8px 0;
}

.countdown {
  color: #e74c3c;
  font-weight: 600;
  font-size: 16px;
}

.payment-notes {
  text-align: left;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #e9ecef;
}

.payment-notes ul {
  margin: 0;
  padding-left: 20px;
  color: #555;
  font-size: 13px;
  line-height: 1.6;
}

.payment-notes li {
  margin-bottom: 4px;
}

.page-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}
</style>
