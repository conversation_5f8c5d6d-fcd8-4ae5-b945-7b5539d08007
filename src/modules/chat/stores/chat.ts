/**
 * 聊天状态管理
 */

import { defineStore } from 'pinia'
import type {
  ChatClientStatus,
  ChatUser,
  ChatRoom,
  ChatSettings,
  QuickReply,
  MessageTemplate,
  ChatStatistics,
  UserOnlineStatus
} from '../types/chat'
import type {
  Message,
  MessageType,
  MessageStatus
} from '../types/message'
import type {
  Session,
  SessionType,
  // SessionStatus // 暂时注释掉未使用的导入
} from '../types/session'
import type {
  Notification,
  NotificationSettings
} from '../types/notification'
import { ChatClient } from '../services/chat-client'
// import { Logger } from '../utils/logger' // 暂时注释掉未使用的导入

/**
 * 聊天状态接口
 */
export interface ChatState {
  // 客户端状态
  clientStatus: ChatClientStatus
  isConnected: boolean
  reconnectAttempts: number
  lastConnectedAt: number | null
  
  // 当前用户
  currentUser: ChatUser | null
  
  // 消息相关
  messages: Map<string, Message[]> // sessionId -> messages
  messageCache: Map<string, Message> // messageId -> message
  unreadCounts: Map<string, number> // sessionId -> unreadCount
  typingUsers: Map<string, Set<string>> // sessionId -> Set<userId>
  
  // 会话相关
  sessions: Session[]
  currentSessionId: string | null
  sessionCache: Map<string, Session> // sessionId -> session
  
  // 通知相关
  notifications: Notification[]
  notificationSettings: NotificationSettings | null
  unreadNotificationCount: number
  
  // 聊天室相关
  chatRooms: ChatRoom[]
  currentRoomId: string | null
  
  // 用户相关
  onlineUsers: Map<string, UserOnlineStatus> // userId -> status
  userProfiles: Map<string, ChatUser> // userId -> profile
  
  // 设置相关
  chatSettings: ChatSettings | null
  quickReplies: QuickReply[]
  messageTemplates: MessageTemplate[]
  
  // 统计相关
  statistics: ChatStatistics | null
  
  // UI状态
  isLoading: boolean
  error: string | null
  
  // 文件上传
  uploadProgress: Map<string, number> // uploadId -> progress
}

/**
 * 聊天状态管理
 */
export const useChatStore = defineStore('chat', {
  state: (): ChatState => ({
    // 客户端状态
    clientStatus: 'DISCONNECTED' as any,
    isConnected: false,
    reconnectAttempts: 0,
    lastConnectedAt: null,
    
    // 当前用户
    currentUser: null,
    
    // 消息相关
    messages: new Map(),
    messageCache: new Map(),
    unreadCounts: new Map(),
    typingUsers: new Map(),
    
    // 会话相关
    sessions: [],
    currentSessionId: null,
    sessionCache: new Map(),
    
    // 通知相关
    notifications: [],
    notificationSettings: null,
    unreadNotificationCount: 0,
    
    // 聊天室相关
    chatRooms: [],
    currentRoomId: null,
    
    // 用户相关
    onlineUsers: new Map(),
    userProfiles: new Map(),
    
    // 设置相关
    chatSettings: null,
    quickReplies: [],
    messageTemplates: [],
    
    // 统计相关
    statistics: null,
    
    // UI状态
    isLoading: false,
    error: null,
    
    // 文件上传
    uploadProgress: new Map()
  }),

  getters: {
    /**
     * 获取当前会话
     */
    currentSession: (state): Session | null => {
      if (!state.currentSessionId) return null
      return state.sessionCache.get(state.currentSessionId) || null
    },

    /**
     * 获取当前会话的消息
     */
    currentMessages: (state): Message[] => {
      if (!state.currentSessionId) return []
      return state.messages.get(state.currentSessionId) || []
    },

    /**
     * 获取当前会话的未读数量
     */
    currentUnreadCount: (state): number => {
      if (!state.currentSessionId) return 0
      return state.unreadCounts.get(state.currentSessionId) || 0
    },

    /**
     * 获取总未读数量
     */
    totalUnreadCount: (state): number => {
      let total = 0
      for (const count of state.unreadCounts.values()) {
        total += count
      }
      return total
    },

    /**
     * 获取当前会话的正在输入用户
     */
    currentTypingUsers: (state): string[] => {
      if (!state.currentSessionId) return []
      const typingSet = state.typingUsers.get(state.currentSessionId)
      return typingSet ? Array.from(typingSet) : []
    },

    /**
     * 获取在线用户数量
     */
    onlineUserCount: (state): number => {
      let count = 0
      for (const status of state.onlineUsers.values()) {
        if (status === 'online') count++
      }
      return count
    },

    /**
     * 检查用户是否在线
     */
    isUserOnline: (state) => (userId: string): boolean => {
      return state.onlineUsers.get(userId) === 'online'
    },

    /**
     * 获取会话按类型分组
     */
    sessionsByType: (state) => {
      const grouped: Record<SessionType, Session[]> = {
        presale_consultation: [],
        after_sale_service: [],
        friend_chat: [],
        customer_service: [],
        system_notification: []
      }
      
      state.sessions.forEach(session => {
        grouped[session.type].push(session)
      })
      
      return grouped
    },

    /**
     * 获取活跃会话（有未读消息或最近活动）
     */
    activeSessions: (state): Session[] => {
      const now = Date.now()
      const activeThreshold = 24 * 60 * 60 * 1000 // 24小时
      
      return state.sessions.filter(session => {
        const hasUnread = (state.unreadCounts.get(session.id.toString()) || 0) > 0
        const isRecent = (now - new Date(session.updated_at).getTime()) < activeThreshold
        return hasUnread || isRecent
      })
    }
  },

  actions: {
    // 辅助方法：获取 ChatClient 实例
    getChatClient() {
      return new ChatClient({
        apiBaseUrl: '/api/v1/chat',
        wsBaseUrl: 'ws://localhost:8080/ws',
        userToken: '',
        userId: 0,
        userType: 'user' as any,
        autoReconnect: true,
        maxReconnectAttempts: 5,
        reconnectInterval: 1000,
        heartbeatInterval: 30000,
        messageQueueSize: 100,
        enableNotifications: true,
        enableSound: true,
        debug: false
      })
    },
    /**
     * 初始化聊天客户端
     */
    async initializeChat(config: any) {
      try {
        this.isLoading = true
        this.error = null
        
        const chatClient = new ChatClient(config)
        
        // 监听连接状态变化
        chatClient.on('statusChange', (status: ChatClientStatus) => {
          this.clientStatus = status
          this.isConnected = status === 'connected'
          
          if (status === 'connected') {
            this.lastConnectedAt = Date.now()
            this.reconnectAttempts = 0
          } else if (status === 'reconnecting') {
            this.reconnectAttempts++
          }
        })
        
        // 监听消息事件
        chatClient.on('messageReceived', (message: Message) => {
          this.addMessage(message)
        })
        
        chatClient.on('messageStatusUpdated', (messageId: string, status: MessageStatus) => {
          this.updateMessageStatus(messageId, status)
        })
        
        // 监听会话事件
        chatClient.on('sessionCreated', (session: Session) => {
          this.addSession(session)
        })
        
        chatClient.on('sessionUpdated', (session: Session) => {
          this.updateSession(session)
        })
        
        // 监听用户状态事件
        chatClient.on('userStatusChanged', (userId: string, status: UserOnlineStatus) => {
          this.updateUserStatus(userId, status)
        })
        
        // 监听输入状态事件
        chatClient.on('typingStatusChanged', (sessionId: string, userId: string, isTyping: boolean) => {
          this.updateTypingStatus(sessionId, userId, isTyping)
        })
        
        // 监听通知事件
        chatClient.on('notificationReceived', (notification: Notification) => {
          this.addNotification(notification)
        })
        
        // 连接聊天服务
        await chatClient.connect()
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '初始化聊天失败'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 断开聊天连接
     */
    async disconnectChat() {
      try {
        const chatClient = new ChatClient({
          apiBaseUrl: '/api/v1/chat',
          wsBaseUrl: 'ws://localhost:8080/ws',
          userToken: '',
          userId: 0,
          userType: 'user' as any,
          autoReconnect: true,
          maxReconnectAttempts: 5,
          reconnectInterval: 1000,
          heartbeatInterval: 30000,
          messageQueueSize: 100,
          enableNotifications: true,
          enableSound: true,
          debug: false
        })
        await chatClient.disconnect()
        
        // 重置状态
        this.clientStatus = 'DISCONNECTED' as any
        this.isConnected = false
        this.reconnectAttempts = 0
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '断开连接失败'
        throw error
      }
    },

    /**
     * 发送消息
     */
    async sendMessage(sessionId: string, content: string, type: MessageType = 'TEXT' as any) {
      try {
        const chatClient = this.getChatClient()
        const message = await chatClient.sendMessage({
          session_id: Number(sessionId),
          type,
          content,
          // metadata: {} // 属性不存在，注释掉
        })
        
        this.addMessage(message as any)
        return message
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '发送消息失败'
        throw error
      }
    },

    /**
     * 创建会话
     */
    async createSession(type: SessionType, _participants: string[], title?: string) {
      try {
        const chatClient = this.getChatClient()
        const session = await chatClient.createSession({
          type,
          title: title || '',
          receiver_id: 1, // 添加必需的属性
          receiver_type: 'USER' as any // 添加必需的属性
          // participants, // 属性不存在，注释掉
          // settings: {} // 属性不存在，注释掉
        })
        
        this.addSession(session as any)
        return session
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '创建会话失败'
        throw error
      }
    },

    /**
     * 加载会话列表
     */
    async loadSessions() {
      try {
        this.isLoading = true
        const chatClient = this.getChatClient()
        
        const response = await chatClient.getSessions({
          page: 1,
          page_size: 50
        })
        
        this.sessions = response.data?.sessions || []
        
        // 更新会话缓存
        this.sessions.forEach(session => {
          this.sessionCache.set(session.id.toString(), session)
        })
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '加载会话列表失败'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 加载消息历史
     */
    async loadMessages(sessionId: string, page = 1, perPage = 50) {
      try {
        const chatClient = this.getChatClient()
        
        const response = await chatClient.getMessages({
          session_id: Number(sessionId),
          page,
          page_size: perPage
        })
        
        const messages = response.data?.messages || []
        
        // 更新消息缓存
        messages.forEach((message: any) => {
          this.messageCache.set(message.id, message)
        })
        
        // 更新会话消息列表
        if (page === 1) {
          this.messages.set(sessionId, messages)
        } else {
          const existingMessages = this.messages.get(sessionId) || []
          this.messages.set(sessionId, [...messages, ...existingMessages])
        }
        
        return messages
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '加载消息失败'
        throw error
      }
    },

    /**
     * 切换当前会话
     */
    async switchSession(sessionId: string) {
      try {
        this.currentSessionId = sessionId
        
        // 加载消息历史（如果还没有加载）
        if (!this.messages.has(sessionId)) {
          await this.loadMessages(sessionId)
        }
        
        // 标记消息已读
        await this.markSessionRead(sessionId)
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '切换会话失败'
        throw error
      }
    },

    /**
     * 标记会话已读
     */
    async markSessionRead(sessionId: string) {
      try {
        const chatClient = this.getChatClient()
        
        await chatClient.markMessageRead({
          session_id: Number(sessionId),
          message_ids: [] // 添加必需的属性，空数组表示标记会话中所有消息为已读
        })
        
        // 更新未读数量
        this.unreadCounts.set(sessionId, 0)
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '标记已读失败'
        throw error
      }
    },

    /**
     * 上传文件
     */
    async uploadFile(file: File, sessionId: string) {
      try {
        const chatClient = this.getChatClient()
        
        const result = await chatClient.uploadFile(file, Number(sessionId))
        // 简化上传处理，移除进度回调
        
        // 发送文件消息
        if (result && typeof result === 'string') {
          await this.sendMessage(sessionId, result, 'FILE' as any)
        }
        
        // 清理上传进度
        this.uploadProgress.delete(file.name)
        
        return result
        
      } catch (error) {
        this.uploadProgress.delete(file.name)
        this.error = error instanceof Error ? error.message : '文件上传失败'
        throw error
      }
    },

    /**
     * 添加消息
     */
    addMessage(message: Message) {
      // 更新消息缓存
      this.messageCache.set(message.id.toString(), message)
      
      // 更新会话消息列表
      const sessionMessages = this.messages.get(message.session_id.toString()) || []
      sessionMessages.push(message)
      this.messages.set(message.session_id.toString(), sessionMessages)
      
      // 更新未读数量（如果不是当前会话）
      if (message.session_id.toString() !== this.currentSessionId) {
        const currentCount = this.unreadCounts.get(message.session_id.toString()) || 0
        this.unreadCounts.set(message.session_id.toString(), currentCount + 1)
      }
      
      // 更新会话的最后消息时间
      const session = this.sessionCache.get(message.session_id.toString())
      if (session) {
        session.updated_at = message.created_at
        this.updateSession(session)
      }
    },

    /**
     * 更新消息状态
     */
    updateMessageStatus(messageId: string, status: MessageStatus) {
      const message = this.messageCache.get(messageId)
      if (message) {
        message.status = status
        this.messageCache.set(messageId, message)
        
        // 更新会话消息列表中的消息
        const sessionMessages = this.messages.get(message.session_id.toString())
        if (sessionMessages) {
          const index = sessionMessages.findIndex(m => m.id.toString() === messageId)
          if (index !== -1) {
            sessionMessages[index] = message
          }
        }
      }
    },

    /**
     * 添加会话
     */
    addSession(session: Session) {
      // 更新会话缓存
      this.sessionCache.set(session.id.toString(), session)
      
      // 添加到会话列表（如果不存在）
      const existingIndex = this.sessions.findIndex(s => s.id === session.id)
      if (existingIndex === -1) {
        this.sessions.unshift(session)
      } else {
        this.sessions[existingIndex] = session
      }
      
      // 按更新时间排序
      this.sessions.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
    },

    /**
     * 更新会话
     */
    updateSession(session: Session) {
      // 更新会话缓存
      this.sessionCache.set(session.id.toString(), session)
      
      // 更新会话列表
      const index = this.sessions.findIndex(s => s.id === session.id)
      if (index !== -1) {
        this.sessions[index] = session
        
        // 重新排序
        this.sessions.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      }
    },

    /**
     * 更新用户在线状态
     */
    updateUserStatus(userId: string, status: UserOnlineStatus) {
      this.onlineUsers.set(userId, status)
    },

    /**
     * 更新输入状态
     */
    updateTypingStatus(sessionId: string, userId: string, isTyping: boolean) {
      let typingSet = this.typingUsers.get(sessionId)
      if (!typingSet) {
        typingSet = new Set()
        this.typingUsers.set(sessionId, typingSet)
      }
      
      if (isTyping) {
        typingSet.add(userId)
      } else {
        typingSet.delete(userId)
      }
    },

    /**
     * 添加通知
     */
    addNotification(notification: Notification) {
      this.notifications.unshift(notification)
      
      // 限制通知数量
      if (this.notifications.length > 100) {
        this.notifications = this.notifications.slice(0, 100)
      }
      
      // 更新未读数量
      if (notification.status === 'PENDING' as any) {
        this.unreadNotificationCount++
      }
    },

    /**
     * 标记通知已读
     */
    async markNotificationRead(notificationId: number) {
      try {
        // const chatClient = this.getChatClient() // 未使用，注释掉
        
        // await chatClient.markNotificationRead({ // 方法不存在，暂时注释掉
        //   notification_id: notificationId
        // }) // 方法不存在，暂时注释掉
        
        // 更新本地状态
        const notification = this.notifications.find(n => n.id === notificationId)
        if (notification && notification.status === 'PENDING' as any) {
          notification.status = 'READ' as any
          this.unreadNotificationCount = Math.max(0, this.unreadNotificationCount - 1)
        }
        
      } catch (error) {
        this.error = error instanceof Error ? error.message : '标记通知已读失败'
        throw error
      }
    },

    /**
     * 清除错误
     */
    clearError() {
      this.error = null
    },

    /**
     * 重置状态
     */
    resetState() {
      this.clientStatus = 'DISCONNECTED' as any
      this.isConnected = false
      this.reconnectAttempts = 0
      this.lastConnectedAt = null
      this.currentUser = null
      this.messages.clear()
      this.messageCache.clear()
      this.unreadCounts.clear()
      this.typingUsers.clear()
      this.sessions = []
      this.currentSessionId = null
      this.sessionCache.clear()
      this.notifications = []
      this.notificationSettings = null
      this.unreadNotificationCount = 0
      this.chatRooms = []
      this.currentRoomId = null
      this.onlineUsers.clear()
      this.userProfiles.clear()
      this.chatSettings = null
      this.quickReplies = []
      this.messageTemplates = []
      this.statistics = null
      this.isLoading = false
      this.error = null
      this.uploadProgress.clear()
    }
  },

  // persist: {
  //   key: 'chat-store',
  //   storage: localStorage,
  //   paths: [
  //     'currentSessionId',
  //     'chatSettings',
  //     'notificationSettings',
  //     'quickReplies',
  //     'messageTemplates'
  //   ]
  // } // 暂时注释掉 persist 配置
})

export default useChatStore