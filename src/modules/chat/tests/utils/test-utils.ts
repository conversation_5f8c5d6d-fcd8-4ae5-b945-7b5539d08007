/**
 * 测试工具函数
 * 提供通用的测试辅助功能
 */

import { Page, Locator, expect } from '@playwright/test'
import { ComponentPublicInstance } from 'vue'
import { VueWrapper } from '@vue/test-utils'

/**
 * 等待元素出现
 */
export async function waitForElement(
  page: Page,
  selector: string,
  options: { timeout?: number; state?: 'attached' | 'detached' | 'visible' | 'hidden' } = {}
): Promise<Locator> {
  const { timeout = 5000, state = 'visible' } = options
  const element = page.locator(selector)
  await element.waitFor({ state, timeout })
  return element
}

/**
 * 等待文本出现
 */
export async function waitForText(
  page: Page,
  text: string,
  options: { timeout?: number; exact?: boolean } = {}
): Promise<Locator> {
  const { timeout = 5000, exact = false } = options
  const element = exact ? page.getByText(text, { exact }) : page.getByText(text)
  await element.waitFor({ timeout })
  return element
}

/**
 * 等待 URL 变化
 */
export async function waitForURL(
  page: Page,
  url: string | RegExp,
  options: { timeout?: number } = {}
): Promise<void> {
  const { timeout = 5000 } = options
  await page.waitForURL(url, { timeout })
}

/**
 * 等待网络请求完成
 */
export async function waitForRequest(
  page: Page,
  urlPattern: string | RegExp,
  options: { timeout?: number; method?: string } = {}
): Promise<any> {
  const { timeout = 5000, method } = options
  
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error(`Request timeout: ${urlPattern}`))
    }, timeout)
    
    page.on('request', (request) => {
      const url = request.url()
      const requestMethod = request.method()
      
      const urlMatches = typeof urlPattern === 'string' 
        ? url.includes(urlPattern)
        : urlPattern.test(url)
      
      const methodMatches = !method || requestMethod === method
      
      if (urlMatches && methodMatches) {
        clearTimeout(timer)
        resolve(request)
      }
    })
  })
}

/**
 * 等待响应
 */
export async function waitForResponse(
  page: Page,
  urlPattern: string | RegExp,
  options: { timeout?: number; status?: number } = {}
): Promise<any> {
  const { timeout = 5000, status } = options
  
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error(`Response timeout: ${urlPattern}`))
    }, timeout)
    
    page.on('response', (response) => {
      const url = response.url()
      const responseStatus = response.status()
      
      const urlMatches = typeof urlPattern === 'string'
        ? url.includes(urlPattern)
        : urlPattern.test(url)
      
      const statusMatches = !status || responseStatus === status
      
      if (urlMatches && statusMatches) {
        clearTimeout(timer)
        resolve(response)
      }
    })
  })
}

/**
 * 模拟文件上传
 */
export async function uploadFile(
  page: Page,
  fileInputSelector: string,
  filePath: string
): Promise<void> {
  const fileInput = page.locator(fileInputSelector)
  await fileInput.setInputFiles(filePath)
}

/**
 * 模拟拖拽文件
 */
export async function dragAndDropFile(
  page: Page,
  targetSelector: string,
  filePath: string
): Promise<void> {
  // 创建文件数据
  const buffer = require('fs').readFileSync(filePath)
  const fileName = filePath.split('/').pop() || 'file'
  
  // 模拟拖拽事件
  await page.evaluate(({ targetSelector, fileName, buffer }) => {
    const target = document.querySelector(targetSelector)
    if (!target) throw new Error(`Target element not found: ${targetSelector}`)
    
    // 创建文件对象
    const file = new File([new Uint8Array(buffer)], fileName)
    
    // 创建拖拽事件
    const dataTransfer = new DataTransfer()
    dataTransfer.items.add(file)
    
    // 触发拖拽事件
    const dragEnterEvent = new DragEvent('dragenter', {
      bubbles: true,
      dataTransfer
    })
    
    const dragOverEvent = new DragEvent('dragover', {
      bubbles: true,
      dataTransfer
    })
    
    const dropEvent = new DragEvent('drop', {
      bubbles: true,
      dataTransfer
    })
    
    target.dispatchEvent(dragEnterEvent)
    target.dispatchEvent(dragOverEvent)
    target.dispatchEvent(dropEvent)
  }, { targetSelector, fileName, buffer: Array.from(buffer) })
}

/**
 * 截图并保存
 */
export async function takeScreenshot(
  page: Page,
  name: string,
  options: { fullPage?: boolean; clip?: any } = {}
): Promise<Buffer> {
  const { fullPage = false, clip } = options
  return await page.screenshot({
    path: `test-results/screenshots/${name}.png`,
    fullPage,
    clip
  })
}

/**
 * 等待动画完成
 */
export async function waitForAnimation(
  page: Page,
  selector?: string,
  duration: number = 500
): Promise<void> {
  if (selector) {
    // 等待特定元素的动画
    await page.locator(selector).waitFor({ state: 'visible' })
  }
  
  // 等待动画持续时间
  await page.waitForTimeout(duration)
}

/**
 * 模拟键盘快捷键
 */
export async function pressShortcut(
  page: Page,
  shortcut: string
): Promise<void> {
  await page.keyboard.press(shortcut)
}

/**
 * 滚动到元素
 */
export async function scrollToElement(
  page: Page,
  selector: string
): Promise<void> {
  await page.locator(selector).scrollIntoViewIfNeeded()
}

/**
 * 等待元素数量
 */
export async function waitForElementCount(
  page: Page,
  selector: string,
  count: number,
  timeout: number = 5000
): Promise<void> {
  await expect(page.locator(selector)).toHaveCount(count, { timeout })
}

/**
 * 模拟网络条件
 */
export async function simulateNetworkCondition(
  page: Page,
  condition: 'offline' | 'slow' | 'fast'
): Promise<void> {
  switch (condition) {
    case 'offline':
      await page.context().setOffline(true)
      break
    case 'slow':
      await page.route('**/*', async (route) => {
        await new Promise(resolve => setTimeout(resolve, 1000))
        await route.continue()
      })
      break
    case 'fast':
      await page.context().setOffline(false)
      await page.unroute('**/*')
      break
  }
}

/**
 * 模拟移动设备
 */
export async function simulateMobileDevice(
  page: Page,
  device: 'iphone' | 'android' | 'tablet'
): Promise<void> {
  const devices = {
    iphone: {
      viewport: { width: 375, height: 667 },
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    },
    android: {
      viewport: { width: 360, height: 640 },
      userAgent: 'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36'
    },
    tablet: {
      viewport: { width: 768, height: 1024 },
      userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    }
  }
  
  const deviceConfig = devices[device]
  await page.setViewportSize(deviceConfig.viewport)
  await page.setExtraHTTPHeaders({
    'User-Agent': deviceConfig.userAgent
  })
}

/**
 * 等待 WebSocket 连接
 */
export async function waitForWebSocket(
  page: Page,
  url: string | RegExp,
  timeout: number = 5000
): Promise<any> {
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error(`WebSocket connection timeout: ${url}`))
    }, timeout)
    
    page.on('websocket', (ws) => {
      const wsUrl = ws.url()
      const matches = typeof url === 'string'
        ? wsUrl.includes(url)
        : url.test(wsUrl)
      
      if (matches) {
        clearTimeout(timer)
        resolve(ws)
      }
    })
  })
}

/**
 * 模拟 WebSocket 消息
 */
export async function mockWebSocketMessage(
  page: Page,
  message: any
): Promise<void> {
  await page.evaluate((msg) => {
    // 触发自定义事件来模拟 WebSocket 消息
    window.dispatchEvent(new CustomEvent('mock-websocket-message', {
      detail: msg
    }))
  }, message)
}

/**
 * 验证元素可见性
 */
export async function verifyElementVisible(
  page: Page,
  selector: string,
  visible: boolean = true
): Promise<void> {
  const element = page.locator(selector)
  if (visible) {
    await expect(element).toBeVisible()
  } else {
    await expect(element).toBeHidden()
  }
}

/**
 * 验证文本内容
 */
export async function verifyTextContent(
  page: Page,
  selector: string,
  expectedText: string | RegExp
): Promise<void> {
  const element = page.locator(selector)
  await expect(element).toHaveText(expectedText)
}

/**
 * 验证属性值
 */
export async function verifyAttribute(
  page: Page,
  selector: string,
  attribute: string,
  expectedValue: string | RegExp
): Promise<void> {
  const element = page.locator(selector)
  await expect(element).toHaveAttribute(attribute, expectedValue)
}

/**
 * 验证 CSS 样式
 */
export async function verifyCSS(
  page: Page,
  selector: string,
  property: string,
  expectedValue: string | RegExp
): Promise<void> {
  const element = page.locator(selector)
  await expect(element).toHaveCSS(property, expectedValue)
}

/**
 * 清理测试数据
 */
export async function cleanupTestData(
  page: Page
): Promise<void> {
  // 清理本地存储
  await page.evaluate(() => {
    localStorage.clear()
    sessionStorage.clear()
  })
  
  // 清理 Cookie
  await page.context().clearCookies()
  
  // 清理缓存
  await page.evaluate(() => {
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name)
        })
      })
    }
  })
}

/**
 * 生成随机测试数据
 */
export function generateTestData() {
  const randomId = () => Math.random().toString(36).substr(2, 9)
  const randomText = (length: number = 10) => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
  
  return {
    user: {
      id: randomId(),
      name: `User_${randomText(5)}`,
      email: `${randomText(8)}@example.com`,
      avatar: `avatar_${randomId()}.jpg`
    },
    session: {
      id: randomId(),
      name: `Session_${randomText(8)}`,
      type: Math.random() > 0.5 ? 'private' : 'group'
    },
    message: {
      id: randomId(),
      content: `Message content ${randomText(20)}`,
      type: 'text',
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Vue 组件测试工具
 */
export class VueTestUtils {
  /**
   * 等待组件更新
   */
  static async waitForUpdate(wrapper: VueWrapper<ComponentPublicInstance>): Promise<void> {
    await wrapper.vm.$nextTick()
  }
  
  /**
   * 触发组件事件
   */
  static async triggerEvent(
    wrapper: VueWrapper<ComponentPublicInstance>,
    selector: string,
    event: string,
    data?: any
  ): Promise<void> {
    const element = wrapper.find(selector)
    await element.trigger(event, data)
    await this.waitForUpdate(wrapper)
  }
  
  /**
   * 设置组件数据
   */
  static async setData(
    wrapper: VueWrapper<ComponentPublicInstance>,
    data: Record<string, any>
  ): Promise<void> {
    await wrapper.setData(data)
    await this.waitForUpdate(wrapper)
  }
  
  /**
   * 设置组件属性
   */
  static async setProps(
    wrapper: VueWrapper<ComponentPublicInstance>,
    props: Record<string, any>
  ): Promise<void> {
    await wrapper.setProps(props)
    await this.waitForUpdate(wrapper)
  }
}

/**
 * 性能测试工具
 */
export class PerformanceUtils {
  /**
   * 测量函数执行时间
   */
  static async measureTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    return {
      result,
      duration: end - start
    }
  }
  
  /**
   * 测量页面加载时间
   */
  static async measurePageLoad(page: Page, url: string): Promise<number> {
    const start = performance.now()
    await page.goto(url)
    await page.waitForLoadState('networkidle')
    const end = performance.now()
    return end - start
  }
  
  /**
   * 监控内存使用
   */
  static async monitorMemory(page: Page): Promise<any> {
    return await page.evaluate(() => {
      if ('memory' in performance) {
        return {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        }
      }
      return null
    })
  }
}

/**
 * 调试工具
 */
export class DebugUtils {
  /**
   * 在浏览器中暂停执行
   */
  static async pause(page: Page): Promise<void> {
    await page.pause()
  }
  
  /**
   * 打印页面信息
   */
  static async logPageInfo(page: Page): Promise<void> {
    const url = page.url()
    const title = await page.title()
    const viewport = page.viewportSize()
    
    console.log('=== Page Info ===')
    console.log(`URL: ${url}`)
    console.log(`Title: ${title}`)
    console.log(`Viewport: ${viewport?.width}x${viewport?.height}`)
    console.log('==================')
  }
  
  /**
   * 打印元素信息
   */
  static async logElementInfo(page: Page, selector: string): Promise<void> {
    const element = page.locator(selector)
    const count = await element.count()
    
    console.log(`=== Element Info: ${selector} ===`)
    console.log(`Count: ${count}`)
    
    if (count > 0) {
      const boundingBox = await element.first().boundingBox()
      const isVisible = await element.first().isVisible()
      const textContent = await element.first().textContent()
      
      console.log(`Visible: ${isVisible}`)
      console.log(`Text: ${textContent}`)
      console.log(`Bounding Box:`, boundingBox)
    }
    
    console.log('==========================')
  }
}