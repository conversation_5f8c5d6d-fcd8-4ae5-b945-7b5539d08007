/**
 * 会话 Store 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useSessionStore } from '../../../stores/session'
import type { Session, SessionType, SessionStatus } from '../../../types'

// Mock API
vi.mock('../../../api', () => ({
  sessionApi: {
    getSessions: vi.fn().mockResolvedValue({
      data: [],
      total: 0,
      page: 1,
      size: 20
    }),
    getSession: vi.fn().mockResolvedValue({
      id: 'session1',
      name: 'Test Session',
      type: 'private',
      status: 'active'
    }),
    createSession: vi.fn().mockResolvedValue({
      id: 'new-session',
      name: 'New Session',
      type: 'private',
      status: 'active',
      createdAt: new Date()
    }),
    updateSession: vi.fn().mockResolvedValue(undefined),
    deleteSession: vi.fn().mockResolvedValue(undefined),
    addParticipant: vi.fn().mockResolvedValue(undefined),
    removeParticipant: vi.fn().mockResolvedValue(undefined),
    updateParticipant: vi.fn().mockResolvedValue(undefined),
    muteSession: vi.fn().mockResolvedValue(undefined),
    unmuteSession: vi.fn().mockResolvedValue(undefined),
    pinSession: vi.fn().mockResolvedValue(undefined),
    unpinSession: vi.fn().mockResolvedValue(undefined),
    archiveSession: vi.fn().mockResolvedValue(undefined),
    unarchiveSession: vi.fn().mockResolvedValue(undefined)
  }
}))

// Mock ChatClient
vi.mock('../../../services/client', () => ({
  ChatClient: {
    getInstance: vi.fn().mockReturnValue({
      joinSession: vi.fn().mockResolvedValue(undefined),
      leaveSession: vi.fn().mockResolvedValue(undefined),
      on: vi.fn(),
      off: vi.fn()
    })
  }
}))

describe('Session Store', () => {
  let store: ReturnType<typeof useSessionStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useSessionStore()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.sessions).toEqual([])
      expect(store.sessionCache).toEqual(new Map())
      expect(store.currentSessionId).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.searchQuery).toBe('')
      expect(store.filterType).toBe('all')
      expect(store.sortBy).toBe('lastActivity')
      expect(store.sortOrder).toBe('desc')
    })
  })

  describe('会话管理', () => {
    const mockSession: Session = {
      id: 'session1',
      name: 'Test Session',
      type: 'private' as SessionType,
      status: 'active' as SessionStatus,
      participants: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActivity: new Date(),
      unreadCount: 0,
      isPinned: false,
      isMuted: false,
      isArchived: false
    }

    it('应该能够添加会话', () => {
      store.addSession(mockSession)
      expect(store.sessions).toContain(mockSession)
      expect(store.sessionCache.get('session1')).toBe(mockSession)
    })

    it('应该能够更新会话', () => {
      store.addSession(mockSession)
      const updatedSession = { ...mockSession, name: 'Updated Session' }
      store.updateSession('session1', updatedSession)
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.name).toBe('Updated Session')
    })

    it('应该能够删除会话', () => {
      store.addSession(mockSession)
      store.removeSession('session1')
      
      expect(store.sessions.find(s => s.id === 'session1')).toBeUndefined()
      expect(store.sessionCache.has('session1')).toBe(false)
    })

    it('应该能够批量添加会话', () => {
      const sessions = [
        { ...mockSession, id: 'session1' },
        { ...mockSession, id: 'session2' },
        { ...mockSession, id: 'session3' }
      ]
      
      store.addSessions(sessions)
      expect(store.sessions).toHaveLength(3)
      expect(store.sessionCache.size).toBe(3)
    })

    it('应该能够清空会话', () => {
      store.addSession(mockSession)
      store.clearSessions()
      
      expect(store.sessions).toEqual([])
      expect(store.sessionCache.size).toBe(0)
    })
  })

  describe('会话创建', () => {
    it('应该能够创建私聊会话', async () => {
      const result = await store.createSession({
        name: 'New Private Chat',
        type: 'private',
        participants: ['user1', 'user2']
      })
      
      expect(result).toBeDefined()
      expect(result.name).toBe('New Private Chat')
      expect(result.type).toBe('private')
    })

    it('应该能够创建群聊会话', async () => {
      const result = await store.createSession({
        name: 'New Group Chat',
        type: 'group',
        participants: ['user1', 'user2', 'user3']
      })
      
      expect(result).toBeDefined()
      expect(result.name).toBe('New Group Chat')
      expect(result.type).toBe('group')
    })

    it('应该能够创建频道会话', async () => {
      const result = await store.createSession({
        name: 'New Channel',
        type: 'channel',
        participants: ['user1']
      })
      
      expect(result).toBeDefined()
      expect(result.name).toBe('New Channel')
      expect(result.type).toBe('channel')
    })
  })

  describe('会话加载', () => {
    it('应该能够加载会话列表', async () => {
      await store.loadSessions()
      
      expect(store.isLoading).toBe(false)
    })

    it('应该能够加载更多会话', async () => {
      store.pagination.page = 1
      
      await store.loadMoreSessions()
      
      expect(store.pagination.page).toBe(2)
    })

    it('应该能够刷新会话列表', async () => {
      store.pagination.page = 2
      
      await store.refreshSessions()
      
      expect(store.pagination.page).toBe(1)
      expect(store.sessions).toEqual([])
    })

    it('应该能够加载单个会话详情', async () => {
      const session = await store.loadSessionDetail('session1')
      
      expect(session).toBeDefined()
      expect(session.id).toBe('session1')
    })
  })

  describe('会话选择', () => {
    const mockSession: Session = {
      id: 'session1',
      name: 'Test Session',
      type: 'private',
      status: 'active',
      participants: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActivity: new Date(),
      unreadCount: 5,
      isPinned: false,
      isMuted: false,
      isArchived: false
    }

    beforeEach(() => {
      store.addSession(mockSession)
    })

    it('应该能够选择会话', async () => {
      await store.selectSession('session1')
      
      expect(store.currentSessionId).toBe('session1')
    })

    it('应该能够取消选择会话', () => {
      store.currentSessionId = 'session1'
      store.unselectSession()
      
      expect(store.currentSessionId).toBeNull()
    })

    it('选择会话时应该清除未读数', async () => {
      await store.selectSession('session1')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.unreadCount).toBe(0)
    })
  })

  describe('参与者管理', () => {
    const mockSession: Session = {
      id: 'session1',
      name: 'Test Session',
      type: 'group',
      status: 'active',
      participants: [
        { userId: 'user1', role: 'admin', joinedAt: new Date() },
        { userId: 'user2', role: 'member', joinedAt: new Date() }
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActivity: new Date(),
      unreadCount: 0,
      isPinned: false,
      isMuted: false,
      isArchived: false
    }

    beforeEach(() => {
      store.addSession(mockSession)
    })

    it('应该能够添加参与者', async () => {
      await store.addParticipant('session1', 'user3', 'member')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.participants).toHaveLength(3)
    })

    it('应该能够移除参与者', async () => {
      await store.removeParticipant('session1', 'user2')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.participants).toHaveLength(1)
      expect(session?.participants.find(p => p.userId === 'user2')).toBeUndefined()
    })

    it('应该能够更新参与者角色', async () => {
      await store.updateParticipant('session1', 'user2', { role: 'admin' })
      
      const session = store.sessions.find(s => s.id === 'session1')
      const participant = session?.participants.find(p => p.userId === 'user2')
      expect(participant?.role).toBe('admin')
    })
  })

  describe('会话操作', () => {
    const mockSession: Session = {
      id: 'session1',
      name: 'Test Session',
      type: 'private',
      status: 'active',
      participants: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActivity: new Date(),
      unreadCount: 0,
      isPinned: false,
      isMuted: false,
      isArchived: false
    }

    beforeEach(() => {
      store.addSession(mockSession)
    })

    it('应该能够静音会话', async () => {
      await store.muteSession('session1')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.isMuted).toBe(true)
    })

    it('应该能够取消静音会话', async () => {
      // 先设置为静音
      store.updateSession('session1', { ...mockSession, isMuted: true })
      
      await store.unmuteSession('session1')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.isMuted).toBe(false)
    })

    it('应该能够置顶会话', async () => {
      await store.pinSession('session1')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.isPinned).toBe(true)
    })

    it('应该能够取消置顶会话', async () => {
      // 先设置为置顶
      store.updateSession('session1', { ...mockSession, isPinned: true })
      
      await store.unpinSession('session1')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.isPinned).toBe(false)
    })

    it('应该能够归档会话', async () => {
      await store.archiveSession('session1')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.isArchived).toBe(true)
    })

    it('应该能够取消归档会话', async () => {
      // 先设置为归档
      store.updateSession('session1', { ...mockSession, isArchived: true })
      
      await store.unarchiveSession('session1')
      
      const session = store.sessions.find(s => s.id === 'session1')
      expect(session?.isArchived).toBe(false)
    })

    it('应该能够删除会话', async () => {
      await store.deleteSession('session1')
      
      expect(store.sessions.find(s => s.id === 'session1')).toBeUndefined()
    })
  })

  describe('搜索和过滤', () => {
    const sessions: Session[] = [
      {
        id: 'session1',
        name: 'Private Chat',
        type: 'private',
        status: 'active',
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActivity: new Date(),
        unreadCount: 0,
        isPinned: false,
        isMuted: false,
        isArchived: false
      },
      {
        id: 'session2',
        name: 'Group Chat',
        type: 'group',
        status: 'active',
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActivity: new Date(),
        unreadCount: 5,
        isPinned: true,
        isMuted: false,
        isArchived: false
      },
      {
        id: 'session3',
        name: 'Archived Chat',
        type: 'private',
        status: 'active',
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActivity: new Date(),
        unreadCount: 0,
        isPinned: false,
        isMuted: true,
        isArchived: true
      }
    ]

    beforeEach(() => {
      store.addSessions(sessions)
    })

    it('应该能够搜索会话', () => {
      store.setSearchQuery('Group')
      const filtered = store.filteredSessions
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0].name).toBe('Group Chat')
    })

    it('应该能够按类型过滤会话', () => {
      store.setFilterType('group')
      const filtered = store.filteredSessions
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0].type).toBe('group')
    })

    it('应该能够过滤未读会话', () => {
      store.setFilterType('unread')
      const filtered = store.filteredSessions
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0].unreadCount).toBeGreaterThan(0)
    })

    it('应该能够过滤置顶会话', () => {
      store.setFilterType('pinned')
      const filtered = store.filteredSessions
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0].isPinned).toBe(true)
    })

    it('应该能够过滤静音会话', () => {
      store.setFilterType('muted')
      const filtered = store.filteredSessions
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0].isMuted).toBe(true)
    })

    it('应该能够过滤归档会话', () => {
      store.setFilterType('archived')
      const filtered = store.filteredSessions
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0].isArchived).toBe(true)
    })

    it('应该能够清除搜索', () => {
      store.setSearchQuery('test')
      store.clearSearch()
      
      expect(store.searchQuery).toBe('')
    })
  })

  describe('排序', () => {
    const sessions: Session[] = [
      {
        id: 'session1',
        name: 'A Session',
        type: 'private',
        status: 'active',
        participants: [],
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-01'),
        lastActivity: new Date('2023-01-03'),
        unreadCount: 0,
        isPinned: false,
        isMuted: false,
        isArchived: false
      },
      {
        id: 'session2',
        name: 'B Session',
        type: 'group',
        status: 'active',
        participants: [],
        createdAt: new Date('2023-01-02'),
        updatedAt: new Date('2023-01-02'),
        lastActivity: new Date('2023-01-01'),
        unreadCount: 5,
        isPinned: true,
        isMuted: false,
        isArchived: false
      }
    ]

    beforeEach(() => {
      store.addSessions(sessions)
    })

    it('应该能够按最后活动时间排序', () => {
      store.setSortBy('lastActivity')
      store.setSortOrder('desc')
      const sorted = store.filteredSessions
      
      expect(sorted[0].id).toBe('session1') // 最新活动
      expect(sorted[1].id).toBe('session2')
    })

    it('应该能够按名称排序', () => {
      store.setSortBy('name')
      store.setSortOrder('asc')
      const sorted = store.filteredSessions
      
      expect(sorted[0].name).toBe('A Session')
      expect(sorted[1].name).toBe('B Session')
    })

    it('应该能够按创建时间排序', () => {
      store.setSortBy('createdAt')
      store.setSortOrder('desc')
      const sorted = store.filteredSessions
      
      expect(sorted[0].id).toBe('session2') // 更晚创建
      expect(sorted[1].id).toBe('session1')
    })

    it('置顶会话应该始终在前面', () => {
      const sorted = store.filteredSessions
      
      expect(sorted[0].isPinned).toBe(true)
      expect(sorted[1].isPinned).toBe(false)
    })
  })

  describe('Getters', () => {
    const sessions: Session[] = [
      {
        id: 'session1',
        name: 'Session 1',
        type: 'private',
        status: 'active',
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActivity: new Date(),
        unreadCount: 3,
        isPinned: true,
        isMuted: false,
        isArchived: false
      },
      {
        id: 'session2',
        name: 'Session 2',
        type: 'group',
        status: 'active',
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActivity: new Date(),
        unreadCount: 0,
        isPinned: false,
        isMuted: true,
        isArchived: false
      },
      {
        id: 'session3',
        name: 'Session 3',
        type: 'channel',
        status: 'active',
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        lastActivity: new Date(),
        unreadCount: 2,
        isPinned: false,
        isMuted: false,
        isArchived: true
      }
    ]

    beforeEach(() => {
      store.addSessions(sessions)
    })

    it('应该正确获取当前会话', () => {
      store.currentSessionId = 'session1'
      const current = store.currentSession
      
      expect(current?.id).toBe('session1')
    })

    it('应该正确获取活跃会话', () => {
      const active = store.activeSessions
      
      expect(active).toHaveLength(2) // 排除归档的
      expect(active.every(s => !s.isArchived)).toBe(true)
    })

    it('应该正确获取未读会话', () => {
      const unread = store.unreadSessions
      
      expect(unread).toHaveLength(2)
      expect(unread.every(s => s.unreadCount > 0)).toBe(true)
    })

    it('应该正确获取置顶会话', () => {
      const pinned = store.pinnedSessions
      
      expect(pinned).toHaveLength(1)
      expect(pinned[0].isPinned).toBe(true)
    })

    it('应该正确获取静音会话', () => {
      const muted = store.mutedSessions
      
      expect(muted).toHaveLength(1)
      expect(muted[0].isMuted).toBe(true)
    })

    it('应该正确获取归档会话', () => {
      const archived = store.archivedSessions
      
      expect(archived).toHaveLength(1)
      expect(archived[0].isArchived).toBe(true)
    })

    it('应该正确按类型分组会话', () => {
      const grouped = store.sessionsByType
      
      expect(grouped.private).toHaveLength(1)
      expect(grouped.group).toHaveLength(1)
      expect(grouped.channel).toHaveLength(1)
    })

    it('应该正确计算总未读数', () => {
      const totalUnread = store.totalUnreadCount
      
      expect(totalUnread).toBe(5) // 3 + 2
    })

    it('应该正确检查是否有未读消息', () => {
      expect(store.hasUnreadMessages).toBe(true)
    })
  })

  describe('错误处理', () => {
    it('应该能够设置错误', () => {
      const error = new Error('Test error')
      store.setError(error)
      
      expect(store.error).toBe(error)
    })

    it('应该能够清除错误', () => {
      const error = new Error('Test error')
      store.setError(error)
      store.clearError()
      
      expect(store.error).toBeNull()
    })
  })

  describe('状态重置', () => {
    it('应该能够重置状态', () => {
      // 设置一些状态
      store.addSession({ id: 'session1' } as Session)
      store.currentSessionId = 'session1'
      store.setSearchQuery('test')
      store.setError(new Error('Test'))
      
      // 重置状态
      store.resetState()
      
      // 验证状态已重置
      expect(store.sessions).toEqual([])
      expect(store.sessionCache.size).toBe(0)
      expect(store.currentSessionId).toBeNull()
      expect(store.searchQuery).toBe('')
      expect(store.error).toBeNull()
    })
  })
})