/**
 * 管理员 Store
 * 负责管理员登录、token 管理、信息获取等状态管理
 */
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { login, getCurrentAdmin, refreshToken, getFrontendPaths } from '../api'; // 引入 refreshToken API
import type { Admin, TokenInfo } from '../types';
import localforage from 'localforage';
import { generateDeviceInfo } from '@/utils/deviceInfo';



/**
 * 管理员 Store
 */
export const useAdminStore = defineStore('admin', () => {
  // token 相关状态
  const accessToken = ref<string>('');
  const refreshTokenValue = ref<string>('');
  const tokenType = ref<string>('Bearer');
  const tokenExpiry = ref<number>(0);

  localforage.getItem('admin_access_token').then((value) => {
    accessToken.value = value ? String(value) : '';
  });
  localforage.getItem('admin_refresh_token').then((value) => {
    refreshTokenValue.value = value ? String(value) : '';
  });
  localforage.getItem('admin_token_type').then((value) => {
    tokenType.value = value ? String(value) : 'Bearer';
  });
  localforage.getItem('admin_token_expiry').then((value) => {
    tokenExpiry.value = value ? Number(value) : 0;
  });

  // 当前管理员信息
  const currentAdmin = ref<Admin | null>(null);

  // 加载状态
  const isLoading = ref<boolean>(false);

  // 错误信息
  const error = ref<string>('');

  // 前端路径 - 使用响应式数组，并初始化为空数组
  const frontendPathsData = ref<any[]>([]);
  
  // 缓存管理
  const pathsLoaded = ref(false);
  
  // 前端路径获取状态
  const isFetchingPaths = ref(false);

  /**
   * 获取前端路径
   * @param forceRefresh 是否强制刷新缓存
   * @returns 前端路径数组的Promise
   */
  const fetchFrontendPaths = async (forceRefresh: boolean = false) => {
    // 如果已经加载过且有数据，且不需要强制刷新，则直接返回内存中的数据
    if (!forceRefresh && pathsLoaded.value && frontendPathsData.value.length > 0) {
      console.log('使用内存缓存的前端路径数据', frontendPathsData.value);
      return [...frontendPathsData.value];
    }
    
    // 如果正在获取中，避免重复请求
    if (isFetchingPaths.value) {
      // 等待获取完成
      let retryCount = 0;
      while (isFetchingPaths.value && retryCount < 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        retryCount++;
      }
      
      if (frontendPathsData.value.length > 0) {
        return [...frontendPathsData.value];
      }
    }
    
    isFetchingPaths.value = true;
    error.value = '';
    
    try {
      let localPaths = null;
      
      // 如果不是强制刷新，先尝试从localforage获取数据
      if (!forceRefresh) {
        localPaths = await localforage.getItem('admin_frontend_paths');
        
        // 检查本地存储的数据是否有效
        if (localPaths && Array.isArray(localPaths) && localPaths.length > 0) {
          console.log('从localforage获取前端路径数据', localPaths);
          // 更新内存中的数据
          frontendPathsData.value = [...localPaths];
          pathsLoaded.value = true;
          isFetchingPaths.value = false;
          return [...frontendPathsData.value];
        }
      }
      
      // localforage中没有数据或需要强制刷新，调用远程API
      console.log('从远程API获取前端路径数据');
      const response: any = await getFrontendPaths();
      console.log('fetchFrontendPaths响应', response);
      
      // 确保响应是数组
      if (Array.isArray(response)) {
        // 处理为可序列化的纯数据对象
        const serializablePaths = response.map((module: any) => {
          return {
            module: module.module,
            paths: Array.isArray(module.paths) ? module.paths.map((path: any) => ({
              path: path.path,
              title: path.title,
              count: path.count || 0,
              config_key: path.config_key || '',
              config_type: path.config_type || '',
              group: path.group || '',
              icon: path.icon || '',
              id: path.id || '',
              version_hash: path.version_hash || ''
              // 可以添加其他必要的简单属性
            })) : []
          };
        });
        
        // 验证数据的有效性
        const isValid = serializablePaths.every((module: any) => 
          module && typeof module.module === 'string' && Array.isArray(module.paths) &&
          module.paths.every((path: any) => path && typeof path.path === 'string' && typeof path.title === 'string')
        );
        console.log('前端路径数据:', serializablePaths);
        if (!isValid) {
          console.error('从API获取的前端路径数据格式无效');
          throw new Error('前端路径数据格式无效');
        }
        
        // 更新内存中的数据
        frontendPathsData.value = [...serializablePaths]; 
        pathsLoaded.value = true;
        
        // 存储到localforage
        try {
          await localforage.setItem('admin_frontend_paths', serializablePaths);
          console.log('前端路径数据已存储到localforage');
        } catch (storageError) {
          console.error('保存前端路径数据到localforage失败:', storageError);
          // 即使保存失败，也继续返回数据
        }
        
        console.log('frontendPaths成功更新为', frontendPathsData.value);
        return [...frontendPathsData.value];
      } else {
        console.error('前端路径返回格式错误', response);
        frontendPathsData.value = [];
        return [];
      }
    } catch (err: any) {
      console.error('获取前端路径失败', err);
      error.value = err.message || '获取前端路径失败';
      return [];
    } finally {
      isFetchingPaths.value = false;
    }
  };

  /**
   * 登录 Action
   * @param username 用户名
   * @param password 密码
   * @returns boolean 登录是否成功
   */
  const loginAction = async (username: string, password: string, remember: boolean) => {
    console.log('loginAction开始执行', username, password);
    isLoading.value = true;
    error.value = '';
    try {
      // 添加设备信息
      const deviceInfo = generateDeviceInfo();
      
      const loginParams = {
        username,
        password,
        device_info: deviceInfo
      };
      
      const response = await login(loginParams);
      console.log('loginAction响应', response);
      const { token_info, admin, device_id, is_new_device, risk_level } = response;

      if(remember) {
        await setlocacltoken(token_info as TokenInfo);
      } else {
        setTokens(token_info as TokenInfo);
      }

      // 保存管理员信息
      currentAdmin.value = admin;
      setAdminInfo(admin);
      
      // 保存当前设备信息到localStorage
      if (device_id) {
        const currentDeviceInfo = {
          ...deviceInfo,
          device_id
        };
        localStorage.setItem('admin_current_device_info', JSON.stringify(currentDeviceInfo));
      }
      
      // 登录成功后获取前端路径
      await fetchFrontendPaths();
      
      return {
        success: true,
        device_id,
        is_new_device,
        risk_level
      };
      
    } catch (err: any) {
      error.value = err.message || '登录失败，请稍后重试';
      return { success: false, message: error.value };
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 设置 token 信息
   * @param tokenInfo token 信息对象
   */
  const setTokens = (tokenInfo: TokenInfo) => {
    accessToken.value = tokenInfo.access_token;
    refreshTokenValue.value = tokenInfo.refresh_token;
    tokenType.value = tokenInfo.token_type;

    // 计算过期时间（当前时间 + 过期秒数）
    const expiryTime = Date.now() + tokenInfo.expires_in * 1000;
    tokenExpiry.value = expiryTime;

    //存储到sessionStorage
    sessionStorage.setItem('admin_access_token', tokenInfo.access_token);
    sessionStorage.setItem('admin_refresh_token', tokenInfo.refresh_token);
    sessionStorage.setItem('admin_token_type', tokenInfo.token_type);
    sessionStorage.setItem('admin_token_expiry', expiryTime.toString());
  };

  /**
   * 
   *  设置管理员信息
   * @param adminInfo 管理员信息
   */
  const setAdminInfo = (adminInfo: Admin) => {
    currentAdmin.value = adminInfo;
    // 存储到localforage
    //await localforage.setItem('admin_info', adminInfo);
    // 存储到sessionStorage
    sessionStorage.setItem('admin_info', JSON.stringify(adminInfo));
  };

  const setlocacltoken = async (tokenInfo: TokenInfo) => {

      const expiryTime = new Date().getTime() + tokenInfo.expires_in * 1000;

      // 存储到localforage
      await localforage.setItem('admin_access_token', tokenInfo.access_token);
      await localforage.setItem('admin_refresh_token', tokenInfo.refresh_token);
      await localforage.setItem('admin_token_type', tokenInfo.token_type);
      await localforage.setItem('admin_token_expiry', expiryTime.toString());

      //存储到sessionStorage
      sessionStorage.setItem('admin_access_token', tokenInfo.access_token);
      sessionStorage.setItem('admin_refresh_token', tokenInfo.refresh_token);
      sessionStorage.setItem('admin_token_type', tokenInfo.token_type);
      sessionStorage.setItem('admin_token_expiry', expiryTime.toString());

      accessToken.value = tokenInfo.access_token;
      refreshTokenValue.value = tokenInfo.refresh_token;
      tokenType.value = tokenInfo.token_type;
      tokenExpiry.value = expiryTime;
  };

  /**
   * 检查 token 是否过期
   * @returns boolean token 是否过期
   */
  const isTokenExpired = () => {
    return Date.now() >= tokenExpiry.value;
  };

  /**
   * 获取当前管理员信息
   * @returns boolean 是否获取成功
   */
  const fetchCurrentAdmin = async () => {
    console.log('开始获取管理员信息');
    if (!accessToken.value) {
      console.log('accessToken 为空，无法获取管理员信息');
      return false;
    }
  
    isLoading.value = true;
    error.value = '';
    try {
      console.log('调用 getCurrentAdmin API');
      const response = await getCurrentAdmin();
      console.log('getCurrentAdmin 响应:', response);
      currentAdmin.value = response;
      console.log('currentAdmin 已更新:', currentAdmin.value);
      return true;
    } catch (err: any) {
      console.error('获取管理员信息失败:', err);
      error.value = err.message || '获取管理员信息失败';
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 清除前端路径缓存
   * 在登出或需要刷新路径信息时调用
   */
  const clearFrontendPathsCache = async () => {
    // 清除内存中的缓存
    frontendPathsData.value = [];
    pathsLoaded.value = false;
    
    // 清除localforage中的缓存
    try {
      await localforage.removeItem('admin_frontend_paths');
      console.log('前端路径缓存已清除');
    } catch (err) {
      console.error('清除前端路径缓存失败', err);
    }
  };

  /**
   * 清除所有本地缓存
   * 包括token、管理员信息、前端路径等所有缓存数据
   */
  const clearAllLocalStorage = async () => {
    try {
      // 清除token信息
      await clearToken();
      
      // 清除前端路径缓存
      await clearFrontendPathsCache();
      
      // 清除其他可能的缓存
      await localforage.removeItem('admin_info');
      await localforage.removeItem('rememberMe');
      
      // 清除会话存储
      sessionStorage.clear();
      
      console.log('已清除所有本地缓存数据');
      return true;
    } catch (err) {
      console.error('清除本地缓存失败', err);
      return false;
    }
  };

  /**
   * 登出 Action
   * 清除所有认证信息和缓存
   */
  const logout = async () => {
    console.log('管理员登出');
    try {
      // 获取当前设备信息
      const currentDeviceInfo = localStorage.getItem('admin_current_device_info');
      let deviceInfo = null;
      if (currentDeviceInfo) {
        try {
          deviceInfo = JSON.parse(currentDeviceInfo);
        } catch (e) {
          console.warn('解析管理员设备信息失败:', e);
        }
      }
      
      if (deviceInfo && deviceInfo.device_id) {
        // 调用登出API
        try {
          const { logout: logoutApi } = await import('../api');
          await logoutApi(deviceInfo.device_id);
        } catch (apiError) {
          console.warn('服务器登出失败，继续清除本地状态:', apiError);
        }
      } else {
        console.warn('未找到设备ID，跳过服务器登出');
      }
      
      // 清除身份验证信息
      await clearToken();
      
      // 清除管理员信息
      currentAdmin.value = null;
      
      // 清除前端路径数据
      await clearFrontendPathsCache();
      
      // 清除设备信息
      localStorage.removeItem('admin_current_device_info');
      
      // 清除路由缓存
      // try {
      //   // 动态导入以避免循环依赖
      //   const { clearRouteCache } = await import('@/utils/routeGuard');
      //   await clearRouteCache();
      // } catch (routeError) {
      //   console.error('清除路由缓存失败:', routeError);
      // }
      
      console.log('管理员登出成功，所有缓存已清除');
      return true;
    } catch (error) {
      console.error('管理员登出过程中发生错误:', error);
      // 即使出错也要确保清除本地状态
      try {
        await clearToken();
        currentAdmin.value = null;
        await clearFrontendPathsCache();
        localStorage.removeItem('admin_current_device_info');
      } catch (cleanupError) {
        console.error('清除本地状态失败:', cleanupError);
      }
      return false;
    }
  };

  /**
   * 清除token信息
   */
  const clearToken = async () => {
    // 清除localforage中的token信息
    await localforage.removeItem('admin_access_token');
    await localforage.removeItem('admin_refresh_token');
    await localforage.removeItem('admin_token_type');
    await localforage.removeItem('admin_token_expiry');

    // 清除sessionStorage中的token信息
    sessionStorage.removeItem('admin_access_token');
    sessionStorage.removeItem('admin_refresh_token');
    sessionStorage.removeItem('admin_token_type');
    sessionStorage.removeItem('admin_token_expiry');

    accessToken.value = '';
    refreshTokenValue.value = '';
    tokenType.value = 'Bearer';
    tokenExpiry.value = 0;
  };

  /**
   * 获取长期 token
   * 直接从localforage中获取refresh_token
   */
  const getLongTermToken = async () => {
    let longTermToken = '';
    // 从sessionStorage中获取token
    const refreshToken = sessionStorage.getItem('admin_refresh_token');
    if (refreshToken) {
      longTermToken = refreshToken;
    }
    console.log('sessionStorage longTermToken', longTermToken);
    // 如果sessionStorage中没有token，从localforage中获取
    if (!longTermToken) {
      await localforage.getItem('admin_refresh_token').then((value) => {
        longTermToken = value ? String(value) : '';
      });
      console.log('localforage longTermToken', longTermToken);
    }
    return longTermToken;
  };

 /**
 * 使用长期 token 登录 Action
 * @returns boolean 登录是否成功
 */
const loginByLongTermTokenAction = async () => {
  isLoading.value = true;
  error.value = '';
  const rememberMe = await localforage.getItem('rememberMe');
  try {
    const longTermToken = await getLongTermToken();
    console.log('longTermToken', longTermToken);

    if (!longTermToken) {
      error.value = '未找到长期 token，请重新登录';
      return false;
    }

    const response = await refreshToken({ refresh_token: longTermToken }); // 调用 refreshToken API

    console.log('loginByLongTermTokenAction响应', response);

    // 显式检查并转换类型
    if (response) {
      const token_info = response as unknown as TokenInfo; // 安全类型断言
      if(rememberMe) {
        await setlocacltoken(token_info);
      } else {
        setTokens(token_info);
      }
      return true;
    } else {
      throw new Error('无效的 token_info 格式');
    }
  } catch (err: any) {
    error.value = err.message || '使用长期 token 登录失败，请稍后重试';
    return false;
  } finally {
    isLoading.value = false;
  }
};

const retoken = async () => {
  const sessionToken = sessionStorage.getItem('admin_access_token');
  if (sessionToken) {
    accessToken.value = sessionToken;
    return sessionToken;
  }

  // 如果会话token不存在，尝试从localforage获取
  const localToken = await localforage.getItem('admin_access_token');
  if (localToken) {
    sessionStorage.setItem('admin_access_token', String(localToken));
    accessToken.value = String(localToken);
    return localToken;
  }

  return false;
}

  return {
    accessToken,
    refreshToken: refreshTokenValue,
    tokenType,
    tokenExpiry,
    currentAdmin,
    isLoading,
    error,
    frontendPathsData,
    pathsLoaded,
    isFetchingPaths,
    loginAction,
    fetchCurrentAdmin,
    logout,
    isTokenExpired,
    retoken,
    fetchFrontendPaths,
    clearFrontendPathsCache,
    clearAllLocalStorage,
    get token() {
      return accessToken.value;
    },
    get frontendPaths() {
      return frontendPathsData.value?.length > 0 ? [...frontendPathsData.value] : [];
    },
    loginByLongTermTokenAction
  };
});
