/**
 * WebSocket相关类型定义
 */

import type { Message, SenderType } from './message'
import type { Session } from './session'

// WebSocket连接状态
export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTING = 'disconnecting',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// WebSocket消息类型
export enum WebSocketMessageType {
  // 认证相关
  AUTH = 'auth',
  AUTH_SUCCESS = 'auth_success',
  AUTH_FAILED = 'auth_failed',
  
  // 心跳相关
  PING = 'ping',
  PONG = 'pong',
  HEARTBEAT = 'heartbeat',
  
  // 消息相关
  MESSAGE = 'message',
  MESSAGE_ACK = 'message_ack',
  MESSAGE_DELIVERED = 'message_delivered',
  MESSAGE_READ = 'message_read',
  
  // 会话相关
  SESSION_CREATED = 'session_created',
  SESSION_UPDATED = 'session_updated',
  SESSION_CLOSED = 'session_closed',
  JOIN_SESSION = 'join_session',
  LEAVE_SESSION = 'leave_session',
  
  // 用户状态相关
  USER_ONLINE = 'user_online',
  USER_OFFLINE = 'user_offline',
  USER_STATUS_CHANGE = 'user_status_change',
  TYPING_START = 'typing_start',
  TYPING_STOP = 'typing_stop',
  
  // 通知相关
  NOTIFICATION = 'notification',
  SYSTEM_MESSAGE = 'system_message',
  
  // 错误相关
  ERROR = 'error',
  RATE_LIMIT = 'rate_limit',
  
  // 其他
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',
  BROADCAST = 'broadcast'
}

// WebSocket基础消息结构
export interface WebSocketMessage {
  type: WebSocketMessageType
  id?: string
  timestamp: number
  data?: any
  session_id?: number
  user_id?: number
  user_type?: SenderType
}

// 认证消息
export interface AuthMessage extends WebSocketMessage {
  type: WebSocketMessageType.AUTH
  data: {
    token: string
    user_id: number
    user_type: SenderType
    client_info: {
      platform: string
      version: string
      device_id: string
    }
  }
}

// 认证成功消息
export interface AuthSuccessMessage extends WebSocketMessage {
  type: WebSocketMessageType.AUTH_SUCCESS
  data: {
    user_id: number
    user_type: SenderType
    session_id: string
    server_time: number
    permissions: string[]
  }
}

// 认证失败消息
export interface AuthFailedMessage extends WebSocketMessage {
  type: WebSocketMessageType.AUTH_FAILED
  data: {
    error_code: string
    error_message: string
    retry_after?: number
  }
}

// 心跳消息
export interface HeartbeatMessage extends WebSocketMessage {
  type: WebSocketMessageType.HEARTBEAT | WebSocketMessageType.PING | WebSocketMessageType.PONG
  data?: {
    server_time?: number
    client_time?: number
  }
}

// 聊天消息
export interface ChatMessage extends WebSocketMessage {
  type: WebSocketMessageType.MESSAGE
  data: {
    message: Message
    session: Session
  }
}

// 消息确认
export interface MessageAckMessage extends WebSocketMessage {
  type: WebSocketMessageType.MESSAGE_ACK
  data: {
    message_id: number
    status: 'received' | 'delivered' | 'read'
    timestamp: number
  }
}

// 会话操作消息
export interface SessionMessage extends WebSocketMessage {
  type: WebSocketMessageType.SESSION_CREATED | WebSocketMessageType.SESSION_UPDATED | WebSocketMessageType.SESSION_CLOSED
  data: {
    session: Session
    action: string
    operator_id: number
    operator_type: SenderType
  }
}

// 加入/离开会话消息
export interface JoinLeaveSessionMessage extends WebSocketMessage {
  type: WebSocketMessageType.JOIN_SESSION | WebSocketMessageType.LEAVE_SESSION
  data: {
    session_id: number
    user_id: number
    user_type: SenderType
    user_name: string
  }
}

// 用户状态变化消息
export interface UserStatusMessage extends WebSocketMessage {
  type: WebSocketMessageType.USER_ONLINE | WebSocketMessageType.USER_OFFLINE | WebSocketMessageType.USER_STATUS_CHANGE
  data: {
    user_id: number
    user_type: SenderType
    status: 'online' | 'offline' | 'away' | 'busy'
    last_seen?: number
  }
}

// 正在输入消息
export interface TypingMessage extends WebSocketMessage {
  type: WebSocketMessageType.TYPING_START | WebSocketMessageType.TYPING_STOP
  data: {
    session_id: number
    user_id: number
    user_type: SenderType
    user_name: string
  }
}

// 通知消息
export interface NotificationMessage extends WebSocketMessage {
  type: WebSocketMessageType.NOTIFICATION
  data: {
    title: string
    content: string
    category: 'info' | 'warning' | 'error' | 'success'
    action_url?: string
    action_text?: string
    expires_at?: number
  }
}

// 系统消息
export interface WebSocketSystemMessage extends WebSocketMessage {
  type: WebSocketMessageType.SYSTEM_MESSAGE
  data: {
    message: string
    level: 'info' | 'warning' | 'error'
    broadcast: boolean
    target_users?: number[]
    target_user_types?: SenderType[]
  }
}

// 错误消息
export interface ErrorMessage extends WebSocketMessage {
  type: WebSocketMessageType.ERROR
  data: {
    error_code: string
    error_message: string
    error_details?: any
    recoverable: boolean
    retry_after?: number
  }
}

// 限流消息
export interface RateLimitMessage extends WebSocketMessage {
  type: WebSocketMessageType.RATE_LIMIT
  data: {
    limit_type: 'message' | 'file' | 'connection'
    current_count: number
    limit: number
    reset_time: number
    retry_after: number
  }
}

// 订阅/取消订阅消息
export interface SubscribeMessage extends WebSocketMessage {
  type: WebSocketMessageType.SUBSCRIBE | WebSocketMessageType.UNSUBSCRIBE
  data: {
    channels: string[]
    session_ids?: number[]
    user_ids?: number[]
  }
}

// 广播消息
export interface BroadcastMessage extends WebSocketMessage {
  type: WebSocketMessageType.BROADCAST
  data: {
    channel: string
    content: any
    sender_id: number
    sender_type: SenderType
    target_audience: 'all' | 'users' | 'merchants' | 'admins'
  }
}

// WebSocket配置
export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnect: {
    enabled: boolean
    maxAttempts: number
    interval: number
    backoff: 'linear' | 'exponential'
    maxInterval: number
  }
  heartbeat: {
    enabled: boolean
    interval: number
    timeout: number
    maxMissed: number
  }
  auth: {
    token: string
    autoAuth: boolean
    authTimeout: number
  }
  message: {
    queueSize: number
    timeout: number
    retryAttempts: number
  }
  debug: boolean
}

// WebSocket事件监听器
export interface WebSocketEventListener {
  onOpen?: (event: Event) => void
  onClose?: (event: CloseEvent) => void
  onError?: (event: Event) => void
  onMessage?: (message: WebSocketMessage) => void
  onAuth?: (success: boolean, data?: any) => void
  onReconnect?: (attempt: number) => void
  onReconnectFailed?: () => void
}

// WebSocket连接信息
export interface WebSocketConnectionInfo {
  status: WebSocketStatus
  url: string
  connected_at?: number
  last_ping?: number
  last_pong?: number
  reconnect_count: number
  message_queue_size: number
  bytes_sent: number
  bytes_received: number
  latency?: number
}

// WebSocket统计信息
export interface WebSocketStats {
  connection_count: number
  total_messages_sent: number
  total_messages_received: number
  total_bytes_sent: number
  total_bytes_received: number
  average_latency: number
  error_count: number
  reconnect_count: number
  uptime: number
  last_error?: {
    code: string
    message: string
    timestamp: number
  }
}

// WebSocket消息队列项
export interface WebSocketQueueItem {
  id: string
  message: WebSocketMessage
  timestamp: number
  attempts: number
  max_attempts: number
  timeout: number
  callback?: (success: boolean, error?: any) => void
}

// WebSocket重连策略
export interface WebSocketReconnectStrategy {
  type: 'linear' | 'exponential' | 'custom'
  baseInterval: number
  maxInterval: number
  maxAttempts: number
  jitter: boolean
  customFunction?: (attempt: number) => number
}

// WebSocket安全配置
export interface WebSocketSecurityConfig {
  validateOrigin: boolean
  allowedOrigins: string[]
  rateLimiting: {
    enabled: boolean
    maxConnections: number
    maxMessagesPerMinute: number
    maxBytesPerMinute: number
  }
  encryption: {
    enabled: boolean
    algorithm: string
    keyRotationInterval: number
  }
}