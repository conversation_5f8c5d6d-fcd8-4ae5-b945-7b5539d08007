/**
 * 用户模块状态管理
 * 处理用户登录状态、用户信息和持久化存储
 */
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { UserInfo } from '../types';
import { UserStatus } from '../types';
import { login, logout, getUserInfo, loginBySms, refreshToken, getLongTermToken } from '@/modules/user/api/auth';
import type { UserLoginParams, SmsLoginParams, LoginResponse } from '../types';
import localforage from 'localforage';

// 命名空间常量，统一标识用户模块
// 对应request.ts中使用的名称
// 保证存储键名与请求模块的期望一致
const NAMESPACE = 'user';

// 导入统一的设备信息生成工具
import { generateDeviceInfo } from '@/utils/deviceInfo';

// 初始化时尝试从存储中获取token
function getInitialToken() {
  // 先从会话存储中获取，因为这个最新鲜
  const sessionToken = sessionStorage.getItem(`${NAMESPACE}_access_token`);
  
  if (sessionToken) {
    // 确保localStorage也同步
    localStorage.setItem(`${NAMESPACE}_access_token`, sessionToken);
    return sessionToken;
  }
  
  // 如果会话中没有，尝试从本地存储获取
  const localToken = localStorage.getItem(`${NAMESPACE}_access_token`);
  
  if (localToken) {
    // 同步到会话存储
    sessionStorage.setItem(`${NAMESPACE}_access_token`, localToken);
    return localToken;
  }
  
  // 兼容旧密钥名
  const oldToken = localStorage.getItem('user_token');
  if (oldToken) {
    sessionStorage.setItem(`${NAMESPACE}_access_token`, oldToken);
    localStorage.setItem(`${NAMESPACE}_access_token`, oldToken);
    return oldToken;
  }
  
  return null;
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string | null>(getInitialToken());
  const userInfo = ref<UserInfo | null>(null);
  const loginTime = ref<number | null>(null);
  const expiresIn = ref<number>(30 * 24 * 60 * 60 * 1000); // 默认30天过期时间
  
  // 地理位置相关状态
  const userLocation = ref<{ longitude: number; latitude: number } | null>(null);
  const locationError = ref<string | null>(null);
  const isGettingLocation = ref<boolean>(false);
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const isActive = computed(() => userInfo.value?.status === UserStatus.ACTIVE);
  const isFrozen = computed(() => userInfo.value?.status === UserStatus.FROZEN);
  const username = computed(() => userInfo.value?.username || '');
  const userId = computed(() => userInfo.value?.id || '');
  const nickname = computed(() => userInfo.value?.nickname || userInfo.value?.username || '');
  const avatar = computed(() => userInfo.value?.avatar || '/images/default_avatar.png');
  
  // 地理位置相关计算属性
  const hasLocation = computed(() => !!userLocation.value);
  const currentLongitude = computed(() => userLocation.value?.longitude || null);
  const currentLatitude = computed(() => userLocation.value?.latitude || null);
  
  // 加载本地存储的用户信息
  async function loadUserFromStorage() {
    try {
      // 优先使localforage获取，因为数据可能更完整
      let userToken = await localforage.getItem<string>(`${NAMESPACE}_access_token`);
      
      // 如果没有，尝试使localStorage获取（兼容旧数据）
      if (!userToken) {
        userToken = localStorage.getItem(`${NAMESPACE}_access_token`) || localStorage.getItem('user_token');
      }
      
      // 获取用户信息
      let userInfoData = await localforage.getItem<string>(`${NAMESPACE}_info`);
      if (!userInfoData) {
        const storedUserInfo = localStorage.getItem(`${NAMESPACE}_info`) || localStorage.getItem('user_info');
        userInfoData = storedUserInfo;
      }
      
      // 获取token过期时间
      let tokenExpiry = await localforage.getItem<string>(`${NAMESPACE}_token_expiry`);
      if (!tokenExpiry) {
        const storedLoginTime = localStorage.getItem(`${NAMESPACE}_login_time`) || localStorage.getItem('user_login_time');
        if (storedLoginTime) {
          // 计算过期时间
          const loginTimeValue = parseInt(storedLoginTime);
          tokenExpiry = (loginTimeValue + expiresIn.value).toString();
        }
      }
      
      // 设置到状态
      if (userToken) {
        token.value = userToken;
      }
      
      if (userInfoData && typeof userInfoData === 'string') {
        try {
          userInfo.value = JSON.parse(userInfoData);
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      } else if (userInfoData) {
        // 如果已经是对象形式
        userInfo.value = userInfoData as any;
      }
      
      // 检查token是否过期
      const now = Date.now();
      if (tokenExpiry) {
        const expireTime = parseInt(tokenExpiry);
        if (now >= expireTime) {
          console.log('Token已过期，清除登录状态');
          await clearUserData();
          return false;
        } else {
          // 设置登录时间和过期时间
          loginTime.value = expireTime - expiresIn.value;
        }
      }
      
      return !!token.value;
    } catch (error) {
      console.error('加载用户数据出错:', error);
      return false;
    }
  }
  
  // 保存用户信息到本地存储
  async function saveUserToStorage() {
    if (token.value) {
      // 计算过期时间
      const expireTime = (loginTime.value || Date.now()) + expiresIn.value;
      
      // 先保存到会话存储，确保当前页面从SSR恢复时可用
      sessionStorage.setItem(`${NAMESPACE}_access_token`, token.value);
      sessionStorage.setItem(`${NAMESPACE}_token_expiry`, expireTime.toString());
      
      // 保存到localStorage以便新标签页快速恢复状态
      localStorage.setItem(`${NAMESPACE}_access_token`, token.value);
      localStorage.setItem(`${NAMESPACE}_token_expiry`, expireTime.toString());
      
      // 兼容旧键名
      localStorage.setItem('user_token', token.value);
      
      // 保存到localforage作为持久化存储，为request.ts使用
      await localforage.setItem(`${NAMESPACE}_access_token`, token.value);
      await localforage.setItem(`${NAMESPACE}_token_expiry`, expireTime.toString());
      
      console.log(`用户token已保存到所有存储，键名为${NAMESPACE}_access_token`);
    }
    
    if (userInfo.value) {
      // 创建一个纯对象，确保不包含Vue的响应式属性
      const userInfoData = JSON.parse(JSON.stringify(userInfo.value));
      const userInfoJson = JSON.stringify(userInfoData);
      
      try {
        // 存储到sessionStorage和localStorage
        sessionStorage.setItem(`${NAMESPACE}_info`, userInfoJson);
        localStorage.setItem(`${NAMESPACE}_info`, userInfoJson);
        
        // 存储到localforage，使用纯对象
        await localforage.setItem(`${NAMESPACE}_info`, userInfoData);
        
        // 兼容旧键名
        localStorage.setItem('user_info', userInfoJson);
      } catch (error) {
        console.error('保存用户信息到存储失败:', error);
      }
    }
    
    if (loginTime.value) {
      const loginTimeStr = loginTime.value.toString();
      sessionStorage.setItem(`${NAMESPACE}_login_time`, loginTimeStr);
      localStorage.setItem(`${NAMESPACE}_login_time`, loginTimeStr);
      await localforage.setItem(`${NAMESPACE}_login_time`, loginTimeStr);
      
      // 兼容旧键名
      localStorage.setItem('user_login_time', loginTimeStr);
    }
  }
  
  // 清除用户数据
  async function clearUserData() {
    // 清除内存中的状态
    token.value = null;
    userInfo.value = null;
    loginTime.value = null;
    
    // 清除会话存储中的数据
    sessionStorage.removeItem(`${NAMESPACE}_access_token`);
    sessionStorage.removeItem(`${NAMESPACE}_refresh_token`);
    sessionStorage.removeItem(`${NAMESPACE}_token_expiry`);
    sessionStorage.removeItem(`${NAMESPACE}_info`);
    sessionStorage.removeItem(`${NAMESPACE}_login_time`);
    
    // 清除localStorage中的数据
    localStorage.removeItem(`${NAMESPACE}_access_token`);
    localStorage.removeItem(`${NAMESPACE}_refresh_token`);
    localStorage.removeItem(`${NAMESPACE}_token_expiry`);
    localStorage.removeItem(`${NAMESPACE}_info`);
    localStorage.removeItem(`${NAMESPACE}_login_time`);
    
    // 清除旧键名的数据（兼容旧代码）
    localStorage.removeItem('user_token');
    localStorage.removeItem('user_info');
    localStorage.removeItem('user_login_time');
    
    // 清除localforage中的数据
    await localforage.removeItem(`${NAMESPACE}_access_token`);
    await localforage.removeItem(`${NAMESPACE}_refresh_token`);
    await localforage.removeItem(`${NAMESPACE}_token_expiry`);
    await localforage.removeItem(`${NAMESPACE}_info`);
    await localforage.removeItem(`${NAMESPACE}_login_time`);
    
    console.log(`清除用户数据完成，命名空间: ${NAMESPACE}`);
  }
  
  // 用户登录
  async function userLogin(loginParams: UserLoginParams): Promise<{ success: boolean; data?: any; message?: string }> {
    try {
      // 生成设备信息
      const deviceInfo = generateDeviceInfo();
      
      // 将设备信息添加到登录参数中
      const loginParamsWithDevice = {
        ...loginParams,
        device_info: deviceInfo
      };
      
      // 使用明确的类型声明
      const response: LoginResponse = await login(loginParamsWithDevice);
      console.log('userLogin response:', response);
      
      // 检查返回的token_info和access_token
      if (response?.token_info?.access_token) {
        // 设置token为access_token
        token.value = response.token_info.access_token;
        
        // 设置登录时间和过期时间
        loginTime.value = Date.now();
        
        // 如果API返回了expires_in，使用API的值（秒转毫秒）
        if (response.token_info.expires_in) {
          expiresIn.value = response.token_info.expires_in * 1000;
        } else {
          // 否则使用默认过期时间
          if (loginParams.rememberMe) {
            expiresIn.value = 30 * 24 * 60 * 60 * 1000; // 30天
          } else {
            expiresIn.value = 24 * 60 * 60 * 1000; // 1天
          }
        }
        
        // 如果API返回了refresh_token，存储它用于长期token刷新
        if (response.token_info.refresh_token) {
          await localforage.setItem('user_refresh_token', response.token_info.refresh_token);
          sessionStorage.setItem('user_refresh_token', response.token_info.refresh_token);
          // 标记为记住登录状态
          await localforage.setItem(`${NAMESPACE}_remember`, loginParams.rememberMe ? '1' : '0');
        }
        
        // 保存access_token到统一的存储位置
        await localforage.setItem('user_access_token', response.token_info.access_token);
        sessionStorage.setItem('user_access_token', response.token_info.access_token);
        
        // 如果API直接返回了用户信息，直接使用
        if (response.user) {
          userInfo.value = response.user;
        } else {
          // 否则尝试获取用户信息
          await fetchUserInfo();
        }
        
        // 保存设备信息到本地存储
        if (response.device_id) {
          localStorage.setItem('user_current_device_info', JSON.stringify({
            ...deviceInfo,
            device_id: response.device_id
          }));
        }
        
        // 保存到本地存储（会保存到sessionStorage、localStorage和localforage）
        await saveUserToStorage();
        
        console.log(`用户登录成功，已保存token和信息到命名空间'${NAMESPACE}'`);
        
        // 返回包含设备信息的响应
        return { 
          success: true, 
          data: {
            ...response,
            device_id: response.device_id,
            is_new_device: response.is_new_device,
            risk_level: response.risk_level
          }
        };
      }
      
      return { success: false, message: '登录失败：无效的响应数据' };
    } catch (error: any) {
      console.error('用户登录失败:', error);
      return { success: false, message: error.message || '登录失败' };
    }
  }
  
  // 短信验证码登录
  async function userSmsLogin(smsParams: SmsLoginParams): Promise<{ success: boolean; data?: any; message?: string }> {
    try {
      // 生成设备信息
      const deviceInfo = generateDeviceInfo();
      
      // 将设备信息添加到登录参数中
      const smsParamsWithDevice = {
        ...smsParams,
        device_info: deviceInfo
      };
      
      // 使用明确的类型声明
      const response: LoginResponse = await loginBySms(smsParamsWithDevice);
      console.log('userSmsLogin response:', response);
      
      // 检查返回的token_info和access_token
      if (response?.token_info?.access_token) {
        // 设置token为access_token
        token.value = response.token_info.access_token;
        
        // 设置登录时间和过期时间
        loginTime.value = Date.now();
        
        // 如果API返回了expires_in，使用API的值（秒转毫秒）
        if (response.token_info.expires_in) {
          expiresIn.value = response.token_info.expires_in * 1000;
        } else {
          // 否则使用默认过期时间
          if (smsParams.rememberMe) {
            expiresIn.value = 30 * 24 * 60 * 60 * 1000; // 30天
          } else {
            expiresIn.value = 24 * 60 * 60 * 1000; // 1天
          }
        }
        
        // 如果API返回了refresh_token，存储它用于长期token刷新
        if (response.token_info.refresh_token) {
          await localforage.setItem('user_refresh_token', response.token_info.refresh_token);
          sessionStorage.setItem('user_refresh_token', response.token_info.refresh_token);
          // 标记为记住登录状态
          await localforage.setItem(`${NAMESPACE}_remember`, smsParams.rememberMe ? '1' : '0');
        }
        
        // 保存access_token到统一的存储位置
        await localforage.setItem('user_access_token', response.token_info.access_token);
        sessionStorage.setItem('user_access_token', response.token_info.access_token);
        
        // 如果API直接返回了用户信息，直接使用
        if (response.user) {
          userInfo.value = response.user;
        } else {
          // 否则尝试获取用户信息
          await fetchUserInfo();
        }
        
        // 保存设备信息到本地存储
        if (response.device_id) {
          localStorage.setItem('user_current_device_info', JSON.stringify({
            ...deviceInfo,
            device_id: response.device_id
          }));
        }
        
        // 保存到本地存储（会保存到sessionStorage、localStorage和localforage）
        await saveUserToStorage();
        
        console.log(`短信登录成功，已保存token和信息到命名空间'${NAMESPACE}'`);
        
        // 返回包含设备信息的响应
        return { 
          success: true, 
          data: {
            ...response,
            device_id: response.device_id,
            is_new_device: response.is_new_device,
            risk_level: response.risk_level
          }
        };
      }
      
      return { success: false, message: '登录失败：无效的响应数据' };
    } catch (error: any) {
      console.error('短信登录失败:', error);
      return { success: false, message: error.message || '登录失败' };
    }
  }
  
  /**
   * 获取用户信息
   * @returns 用户信息或null
   */
  async function fetchUserInfo(): Promise<UserInfo | null> {
    try {
      if (!token.value) {
        console.log('获取用户信息失败: 无效的token');
        return null;
      }
      
      const response = await getUserInfo();
      
      // 检查响应中的数据
      if (response?.data?.data) {
        // 更新内存中的用户信息
        userInfo.value = response.data.data;
        
        // 立即保存到所有存储中
        await saveUserToStorage();
        
        console.log(`获取用户信息成功，已保存到${NAMESPACE}命名空间`);
        return response.data.data;
      }
      
      console.log('获取用户信息失败: 服务器返回的数据无效');
      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }
  
  /**
   * 用户登出方法
   * 先获取必要信息，再调用API，最后清除本地数据
   * @returns Promise<boolean> 登出是否成功
   */
  async function userLogout(): Promise<boolean> {
    console.log('开始用户登出流程');
    try {
      // 先获取token和设备信息，避免清除数据后无法获取
      const lastToken = sessionStorage.getItem(`${NAMESPACE}_access_token`);
      const deviceInfo = JSON.parse(localStorage.getItem('user_current_device_info') || '{}');
      
      console.log('获取到的token:', lastToken ? '存在' : '不存在');
      console.log('获取到的设备信息:', deviceInfo);
      
      // 只有当前有token和设备ID时才调用后端登出接口
      if (lastToken && deviceInfo.device_id) {
        try {
          // 调用后端登出接口，传递设备ID
          await logout(deviceInfo.device_id);
          console.log('服务器端登出成功');
        } catch (e) {
          console.error('服务器端登出失败，但继续清除本地数据', e);
          // 即使服务器端登出失败，也要继续清除本地数据
        }
      } else {
        console.warn('缺少token或设备ID，跳过服务器端登出');
        console.warn('Token存在:', !!lastToken, 'DeviceId存在:', !!deviceInfo.device_id);
      }
      
      // 清除本地数据
      await clearUserData();
      
      // 清除设备信息
      localStorage.removeItem('user_current_device_info');
      
      console.log(`用户已登出，清除了${NAMESPACE}命名空间的所有数据`);
      return true;
    } catch (error) {
      console.error('登出过程发生错误:', error);
      
      // 即使发生错误，也要清除本地数据
      await clearUserData();
      localStorage.removeItem('user_current_device_info');
      
      return false;
    }
  }
  
  // 更新用户信息
  function updateUserInfo(newUserInfo: Partial<UserInfo>) {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...newUserInfo };
      saveUserToStorage();
    }
  }
  
/**
 * 恢复token状态
 * 在页面刷新或重新打开时调用，恢复登录状态
 * @returns 返回true表示恢复了token，false表示没有可用token
 */
const retoken = async (): Promise<boolean> => {
  // 先从SessionStorage获取token，这个最新鲜
  const sessionToken = sessionStorage.getItem('user_access_token');
  if (sessionToken) {
    token.value = sessionToken;
    return true;
  }
  
  // 如果会话token不存在，尝试从localforage获取
  const localToken = await localforage.getItem('user_access_token');
  if (localToken) {
    sessionStorage.setItem('user_access_token', String(localToken));
    token.value = String(localToken);
    return true;
  }
  
  return false;
};

/**
 * 使用长期token登录
 * @returns 登录是否成功
 */
const loginByLongTermTokenAction = async (): Promise<boolean> => {
  try {
    console.log('尝试使用长期token登录');
    
    // 先尝试恢复可用token
    const tokenExists = await retoken();
    if (tokenExists) {
      console.log('已恢复token状态，尝试获取用户信息');
    }
    
    // 获取长期token
    const longTermToken = await getLongTermToken();
    console.log('用户长期token:', longTermToken);
    
    if (!longTermToken) {
      console.log('未找到长期token，请重新登录');
      return false;
    }
    
    // 调用刷新token接口
     const response : any = await refreshToken({ refresh_token: longTermToken });
    console.log('用户刷新token响应:', response);
    
    if (response && response.access_token) {
      console.log('刷新token成功');
      
      // 保存新的token信息
      token.value = response.access_token;
      
      // 保存到localforage和sessionStorage
      await localforage.setItem('user_access_token', response.access_token);
      sessionStorage.setItem('user_access_token', response.access_token);
      
      if (response.refresh_token) {
        await localforage.setItem('user_refresh_token', response.refresh_token);
        sessionStorage.setItem('user_refresh_token', response.refresh_token);
      }
      
      // 获取用户信息
      await fetchUserInfo();
      
      console.log('使用长期token登录成功');
      return true;
    }
    
    console.log('使用长期token登录失败');
    return false;
  } catch (error) {
    console.error('使用长期token登录出错:', error);
    return false;
  }
};
  
  /**
   * 获取用户当前地理位置
   * 使用浏览器的navigator.geolocation API
   * @returns Promise<boolean> 是否成功获取位置
   */
  async function getCurrentLocation(): Promise<boolean> {
    if (isGettingLocation.value) {
      console.log('正在获取位置中，请稍候');
      return false;
    }
    
    if (!navigator.geolocation) {
      locationError.value = '浏览器不支持地理位置获取';
      console.error('浏览器不支持地理位置获取');
      return false;
    }
    
    isGettingLocation.value = true;
    locationError.value = null;
    
    return new Promise((resolve) => {
      const options = {
        enableHighAccuracy: true, // 启用高精度
        timeout: 10000, // 10秒超时
        maximumAge: 300000 // 5分钟内的缓存位置可用
      };
      
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const { longitude, latitude } = position.coords;
            
            // 保存位置信息
            userLocation.value = { longitude, latitude };
            
            // 持久化存储位置信息
            await saveLocationToStorage();
            
            console.log(`获取位置成功: 经度${longitude}, 纬度${latitude}`);
            isGettingLocation.value = false;
            resolve(true);
          } catch (error) {
            console.error('保存位置信息失败:', error);
            isGettingLocation.value = false;
            resolve(false);
          }
        },
        (error) => {
          let errorMessage = '获取位置失败';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = '用户拒绝了位置权限请求';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = '位置信息不可用';
              break;
            case error.TIMEOUT:
              errorMessage = '获取位置超时';
              break;
            default:
              errorMessage = '未知的位置获取错误';
              break;
          }
          
          locationError.value = errorMessage;
          console.error('获取位置失败:', errorMessage, error);
          isGettingLocation.value = false;
          resolve(false);
        },
        options
      );
    });
  }
  
  /**
   * 保存位置信息到本地存储
   */
  async function saveLocationToStorage() {
    if (userLocation.value) {
      // 创建纯净的数据对象，避免序列化问题
      const locationData = {
        longitude: userLocation.value.longitude,
        latitude: userLocation.value.latitude
      };
      const locationDataString = JSON.stringify(locationData);
      
      try {
        // 保存到各种存储中
        sessionStorage.setItem(`${NAMESPACE}_location`, locationDataString);
        localStorage.setItem(`${NAMESPACE}_location`, locationDataString);
        // 存储纯净的数据对象到localforage，避免DataCloneError
        await localforage.setItem(`${NAMESPACE}_location`, locationData);
        
        console.log('位置信息已保存到存储');
      } catch (error) {
        console.error('保存位置信息到存储失败:', error);
      }
    }
  }
  
  /**
   * 从本地存储加载位置信息
   */
  async function loadLocationFromStorage() {
    try {
      // 优先从localforage获取
      let locationData = await localforage.getItem<{ longitude: number; latitude: number }>(`${NAMESPACE}_location`);
      
      if (!locationData) {
        // 尝试从localStorage获取
        const storedLocation = localStorage.getItem(`${NAMESPACE}_location`) || sessionStorage.getItem(`${NAMESPACE}_location`);
        if (storedLocation) {
          locationData = JSON.parse(storedLocation);
        }
      }
      
      if (locationData && typeof locationData === 'object' && locationData.longitude && locationData.latitude) {
        userLocation.value = locationData;
        console.log('从存储中恢复位置信息:', locationData);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('从存储加载位置信息失败:', error);
      return false;
    }
  }
  
  /**
   * 清除位置信息
   */
  async function clearLocationData() {
    userLocation.value = null;
    locationError.value = null;
    
    // 清除存储中的位置信息
    sessionStorage.removeItem(`${NAMESPACE}_location`);
    localStorage.removeItem(`${NAMESPACE}_location`);
    await localforage.removeItem(`${NAMESPACE}_location`);
    
    console.log('位置信息已清除');
  }
  
  /**
   * 计算两点之间的距离（使用Haversine公式）
   * 适用于GCJ02坐标系
   * @param lat1 起点纬度
   * @param lon1 起点经度
   * @param lat2 终点纬度
   * @param lon2 终点经度
   * @returns 距离（公里）
   */
  function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Number(distance.toFixed(3)); // 保留三位小数，更精确
  }
  
  /**
   * 计算商家距离当前位置的距离
   * @param merchantLat 商家纬度
   * @param merchantLon 商家经度
   * @returns 距离（公里），如果没有当前位置则返回null
   */
  function calculateMerchantDistance(merchantLat: number, merchantLon: number): number | null {
    if (!userLocation.value) {
      return null;
    }
    
    return calculateDistance(
      userLocation.value.latitude,
      userLocation.value.longitude,
      merchantLat,
      merchantLon
    );
  }
  
  // 初始化加载用户数据和位置信息
  loadUserFromStorage();
  loadLocationFromStorage();
  
  return {
    token,
    userInfo,
    loginTime,
    isLoggedIn,
    isActive,
    isFrozen,
    username,
    userId,
    nickname,
    avatar,
    loadUserFromStorage,
    userLogin,
    userSmsLogin,
    userLogout,
    fetchUserInfo,
    updateUserInfo,
    clearUserData,
    retoken,
    loginByLongTermTokenAction,
    // 地理位置相关
    userLocation,
    locationError,
    isGettingLocation,
    hasLocation,
    currentLongitude,
    currentLatitude,
    getCurrentLocation,
    saveLocationToStorage,
    loadLocationFromStorage,
    clearLocationData,
    calculateDistance,
    calculateMerchantDistance
  };
});
