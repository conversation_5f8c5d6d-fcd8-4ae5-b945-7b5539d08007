/**
 * 消息状态管理
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'
import type {
  Message,
  // MessageType, // 未使用，注释掉
  // MessageStatus, // 未使用，注释掉
  // MessageSearchRequest, // 不存在，注释掉
  // MessageSearchResponse, // 不存在，注释掉
  // MessageStatistics, // 不存在，注释掉
  MessageDraft
} from '../types/message'
import { MessageService } from '../services/message'
// import { Logger } from '../utils/logger' // 未使用，注释掉

// 简化的统计类型定义
interface MessageStatistics {
  total_messages: number
  unread_messages: number
  sent_messages: number
  received_messages: number
}

/**
 * 消息状态接口
 */
export interface MessageState {
  // 消息数据
  messages: Map<string, Message[]> // sessionId -> messages
  messageCache: Map<string, Message> // messageId -> message
  drafts: Map<string, MessageDraft> // sessionId -> draft
  
  // 分页信息
  pagination: Map<string, {
    page: number
    per_page: number
    total: number
    has_more: boolean
  }> // sessionId -> pagination
  
  // 搜索相关
  searchResults: Message[]
  searchQuery: string
  searchLoading: boolean
  
  // 统计信息
  statistics: MessageStatistics | null
  
  // 输入状态
  typingUsers: Map<string, Set<string>> // sessionId -> Set<userId>
  typingTimeouts: Map<string, Map<string, number>> // sessionId -> userId -> timeoutId
  
  // 消息状态
  sendingMessages: Set<string> // 正在发送的消息ID
  failedMessages: Set<string> // 发送失败的消息ID
  
  // 未读消息
  unreadCounts: Map<string, number> // sessionId -> count
  lastReadTimes: Map<string, number> // sessionId -> timestamp
  
  // UI状态
  isLoading: boolean
  error: string | null
  
  // 消息操作
  selectedMessages: Set<string> // 选中的消息ID
  replyingTo: Message | null // 正在回复的消息
  editingMessage: Message | null // 正在编辑的消息
}

/**
 * 消息状态管理
 */
// 简化的消息 Store，使用 Composition API 风格
export const useMessageStore = defineStore('message', () => {
  const messages = ref(new Map<string, any[]>())
  const messageCache = ref(new Map<string, any>())
  const drafts = ref(new Map<string, any>())
  const searchResults = ref<any[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastReadTimes = ref(new Map<string, string>())
  const statistics = ref<MessageStatistics | null>(null)
  const editingMessage = ref<any | null>(null)
  const typingUsers = ref(new Map<string, any[]>()) // sessionId -> typing users
  const unreadCounts = ref(new Map<string, number>()) // sessionId -> unread count

  // 简化的方法
  const sendMessage = async (sessionId: string, content: string, type: any = 'TEXT') => {
    const messageService = new MessageService()
    const tempId = Date.now().toString()

    const tempMessage: any = {
      id: tempId,
      session_id: Number(sessionId),
      sender_id: 1,
      sender_type: 'USER' as any,
      receiver_id: 1,
      receiver_type: 'USER' as any,
      type,
      content,
      status: 'SENDING' as any,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_deleted: false
    }

    addMessage(tempMessage)

    try {
      const sentMessage = await messageService.sendMessage({
        session_id: Number(sessionId),
        type,
        content
      })

      removeMessage(tempId)
      addMessage(sentMessage as any)
      clearDraft(sessionId)
    } catch (error) {
      tempMessage.status = 'FAILED' as any
      updateMessage(tempMessage)
      throw error
    }
  }

  const addMessage = (message: any) => {
    messageCache.value.set(message.id.toString(), message)

    const sessionMessages = messages.value.get(message.session_id.toString()) || []
    sessionMessages.push(message)

    if (sessionMessages.length > 1) {
      sessionMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
    }

    messages.value.set(message.session_id.toString(), sessionMessages)
  }

  const removeMessage = (messageId: string) => {
    const message = messageCache.value.get(messageId)
    if (message) {
      messageCache.value.delete(messageId)

      const sessionMessages = messages.value.get(message.session_id.toString())
      if (sessionMessages) {
        const index = sessionMessages.findIndex(m => m.id.toString() === messageId)
        if (index !== -1) {
          sessionMessages.splice(index, 1)
        }
      }
    }
  }

  const updateMessage = (message: any) => {
    messageCache.value.set(message.id.toString(), message)

    const sessionMessages = messages.value.get(message.session_id.toString())
    if (sessionMessages) {
      const index = sessionMessages.findIndex(m => m.id.toString() === message.id.toString())
      if (index !== -1) {
        sessionMessages[index] = message
      }
    }
  }

  const clearDraft = (sessionId: string) => {
    drafts.value.delete(sessionId)
  }

  const loadMessages = async (sessionId: string) => {
    const messageService = new MessageService()
    const response = await messageService.getMessages({
      session_id: Number(sessionId),
      page_size: 50
    })

    const messageList = response.data?.messages || []
    messageList.forEach((message: any) => {
      messageCache.value.set(message.id.toString(), message)
    })

    messages.value.set(sessionId, messageList)
    return {
      messages: messageList,
      total: response.data?.total || 0,
      has_more: response.data?.has_more || false
    }
  }

  return {
    messages,
    messageCache,
    drafts,
    searchResults,
    isLoading,
    error,
    lastReadTimes,
    statistics,
    editingMessage,
    typingUsers,
    unreadCounts,
    sendMessage,
    addMessage,
    removeMessage,
    updateMessage,
    clearDraft,
    loadMessages
  }
})

export default useMessageStore
