/**
 * 通知 Store 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useNotificationStore } from '../../../stores/notification'
import type { Notification, NotificationType } from '../../../types'

// Mock Notification API
Object.defineProperty(window, 'Notification', {
  value: vi.fn().mockImplementation((title, options) => ({
    title,
    ...options,
    close: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
  })),
  configurable: true
})

Object.defineProperty(window.Notification, 'permission', {
  value: 'default',
  writable: true
})

Object.defineProperty(window.Notification, 'requestPermission', {
  value: vi.fn().mockResolvedValue('granted')
})

// Mock Audio
global.Audio = vi.fn().mockImplementation(() => ({
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
}))

describe('Notification Store', () => {
  let store: ReturnType<typeof useNotificationStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useNotificationStore()
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.notifications).toEqual([])
      expect(store.settings.enabled).toBe(true)
      expect(store.settings.sound).toBe(true)
      expect(store.settings.desktop).toBe(true)
      expect(store.settings.vibration).toBe(true)
      expect(store.settings.autoClose).toBe(true)
      expect(store.settings.autoCloseDelay).toBe(5000)
      expect(store.settings.maxNotifications).toBe(10)
      expect(store.permission).toBe('default')
    })
  })

  describe('权限管理', () => {
    it('应该能够请求通知权限', async () => {
      const result = await store.requestPermission()
      
      expect(window.Notification.requestPermission).toHaveBeenCalled()
      expect(result).toBe('granted')
      expect(store.permission).toBe('granted')
    })

    it('应该正确检查权限状态', () => {
      window.Notification.permission = 'granted'
      expect(store.hasPermission).toBe(true)
      
      window.Notification.permission = 'denied'
      expect(store.hasPermission).toBe(false)
      
      window.Notification.permission = 'default'
      expect(store.hasPermission).toBe(false)
    })
  })

  describe('通知创建', () => {
    const mockNotification: Omit<Notification, 'id' | 'createdAt'> = {
      type: 'message' as NotificationType,
      title: 'New Message',
      content: 'You have a new message',
      senderId: 'user1',
      sessionId: 'session1',
      isRead: false
    }

    it('应该能够添加通知', () => {
      const notification = store.addNotification(mockNotification)
      
      expect(store.notifications).toHaveLength(1)
      expect(store.notifications[0]).toEqual(notification)
      expect(notification.id).toBeDefined()
      expect(notification.createdAt).toBeInstanceOf(Date)
    })

    it('应该能够创建消息通知', () => {
      const notification = store.createMessageNotification({
        title: 'New Message',
        content: 'Hello world',
        senderId: 'user1',
        sessionId: 'session1',
        avatar: 'https://example.com/avatar.jpg'
      })
      
      expect(notification.type).toBe('message')
      expect(notification.title).toBe('New Message')
      expect(notification.avatar).toBe('https://example.com/avatar.jpg')
    })

    it('应该能够创建系统通知', () => {
      const notification = store.createSystemNotification({
        title: 'System Update',
        content: 'System will be updated'
      })
      
      expect(notification.type).toBe('system')
      expect(notification.title).toBe('System Update')
    })

    it('应该能够创建错误通知', () => {
      const notification = store.createErrorNotification({
        title: 'Error',
        content: 'Something went wrong'
      })
      
      expect(notification.type).toBe('error')
      expect(notification.title).toBe('Error')
    })

    it('应该能够创建警告通知', () => {
      const notification = store.createWarningNotification({
        title: 'Warning',
        content: 'Please check your settings'
      })
      
      expect(notification.type).toBe('warning')
      expect(notification.title).toBe('Warning')
    })

    it('应该能够创建成功通知', () => {
      const notification = store.createSuccessNotification({
        title: 'Success',
        content: 'Operation completed successfully'
      })
      
      expect(notification.type).toBe('success')
      expect(notification.title).toBe('Success')
    })

    it('应该能够创建信息通知', () => {
      const notification = store.createInfoNotification({
        title: 'Info',
        content: 'Here is some information'
      })
      
      expect(notification.type).toBe('info')
      expect(notification.title).toBe('Info')
    })
  })

  describe('通知显示', () => {
    beforeEach(() => {
      window.Notification.permission = 'granted'
      store.updateSettings({ enabled: true, desktop: true })
    })

    it('应该能够显示桌面通知', async () => {
      const notification = store.addNotification({
        type: 'message',
        title: 'Test',
        content: 'Test message'
      })
      
      await store.showDesktopNotification(notification.id)
      
      expect(window.Notification).toHaveBeenCalledWith('Test', {
        body: 'Test message',
        icon: undefined,
        tag: notification.id,
        requireInteraction: false
      })
    })

    it('权限被拒绝时不应该显示桌面通知', async () => {
      window.Notification.permission = 'denied'
      
      const notification = store.addNotification({
        type: 'message',
        title: 'Test',
        content: 'Test message'
      })
      
      await store.showDesktopNotification(notification.id)
      
      expect(window.Notification).not.toHaveBeenCalled()
    })

    it('桌面通知被禁用时不应该显示', async () => {
      store.updateSettings({ desktop: false })
      
      const notification = store.addNotification({
        type: 'message',
        title: 'Test',
        content: 'Test message'
      })
      
      await store.showDesktopNotification(notification.id)
      
      expect(window.Notification).not.toHaveBeenCalled()
    })
  })

  describe('声音通知', () => {
    beforeEach(() => {
      store.updateSettings({ enabled: true, sound: true })
    })

    it('应该能够播放通知声音', async () => {
      await store.playNotificationSound('message')
      
      expect(global.Audio).toHaveBeenCalled()
    })

    it('声音被禁用时不应该播放', async () => {
      store.updateSettings({ sound: false })
      
      await store.playNotificationSound('message')
      
      expect(global.Audio).not.toHaveBeenCalled()
    })

    it('应该能够播放不同类型的声音', async () => {
      await store.playNotificationSound('error')
      await store.playNotificationSound('warning')
      await store.playNotificationSound('success')
      
      expect(global.Audio).toHaveBeenCalledTimes(3)
    })
  })

  describe('振动通知', () => {
    beforeEach(() => {
      // Mock navigator.vibrate
      Object.defineProperty(navigator, 'vibrate', {
        value: vi.fn(),
        configurable: true
      })
      
      store.updateSettings({ enabled: true, vibration: true })
    })

    it('应该能够触发振动', () => {
      store.vibrate('message')
      
      expect(navigator.vibrate).toHaveBeenCalled()
    })

    it('振动被禁用时不应该触发', () => {
      store.updateSettings({ vibration: false })
      
      store.vibrate('message')
      
      expect(navigator.vibrate).not.toHaveBeenCalled()
    })

    it('不支持振动时不应该报错', () => {
      delete (navigator as any).vibrate
      
      expect(() => store.vibrate('message')).not.toThrow()
    })
  })

  describe('通知管理', () => {
    beforeEach(() => {
      // 添加一些测试通知
      store.addNotification({
        type: 'message',
        title: 'Message 1',
        content: 'Content 1',
        isRead: false
      })
      store.addNotification({
        type: 'system',
        title: 'System 1',
        content: 'Content 2',
        isRead: true
      })
      store.addNotification({
        type: 'error',
        title: 'Error 1',
        content: 'Content 3',
        isRead: false
      })
    })

    it('应该能够标记通知为已读', () => {
      const notification = store.notifications[0]
      store.markAsRead(notification.id)
      
      expect(notification.isRead).toBe(true)
      expect(notification.readAt).toBeInstanceOf(Date)
    })

    it('应该能够标记所有通知为已读', () => {
      store.markAllAsRead()
      
      expect(store.notifications.every(n => n.isRead)).toBe(true)
    })

    it('应该能够删除通知', () => {
      const notification = store.notifications[0]
      store.removeNotification(notification.id)
      
      expect(store.notifications.find(n => n.id === notification.id)).toBeUndefined()
    })

    it('应该能够清空所有通知', () => {
      store.clearAllNotifications()
      
      expect(store.notifications).toEqual([])
    })

    it('应该能够清空已读通知', () => {
      store.clearReadNotifications()
      
      expect(store.notifications.every(n => !n.isRead)).toBe(true)
      expect(store.notifications).toHaveLength(2) // 只剩未读的
    })

    it('应该限制通知数量', () => {
      store.updateSettings({ maxNotifications: 2 })

      // 添加新通知应该移除最老的
      store.addNotification({
        type: 'info',
        title: 'New notification',
        content: 'This should remove the oldest'
      })

      expect(store.notifications).toHaveLength(2)
    })
  })

  describe('自动关闭', () => {
    beforeEach(() => {
      vi.useFakeTimers()
      store.updateSettings({ autoClose: true, autoCloseDelay: 1000 })
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('应该能够自动关闭通知', () => {
      const notification = store.addNotification({
        type: 'info',
        title: 'Auto close test',
        content: 'This should auto close'
      })
      
      expect(store.notifications).toHaveLength(1)
      
      // 快进时间
      vi.advanceTimersByTime(1000)
      
      expect(store.notifications.find(n => n.id === notification.id)).toBeUndefined()
    })

    it('自动关闭被禁用时不应该关闭', () => {
      store.updateSettings({ autoClose: false })
      
      const notification = store.addNotification({
        type: 'info',
        title: 'No auto close test',
        content: 'This should not auto close'
      })
      
      vi.advanceTimersByTime(2000)
      
      expect(store.notifications.find(n => n.id === notification.id)).toBeDefined()
    })

    it('错误通知不应该自动关闭', () => {
      const notification = store.addNotification({
        type: 'error',
        title: 'Error test',
        content: 'Errors should not auto close'
      })
      
      vi.advanceTimersByTime(2000)
      
      expect(store.notifications.find(n => n.id === notification.id)).toBeDefined()
    })
  })

  describe('Getters', () => {
    beforeEach(() => {
      store.addNotification({
        type: 'message',
        title: 'Message 1',
        content: 'Content 1',
        isRead: false
      })
      store.addNotification({
        type: 'system',
        title: 'System 1',
        content: 'Content 2',
        isRead: true
      })
      store.addNotification({
        type: 'error',
        title: 'Error 1',
        content: 'Content 3',
        isRead: false
      })
    })

    it('应该正确获取未读通知', () => {
      const unread = store.unreadNotifications
      
      expect(unread).toHaveLength(2)
      expect(unread.every(n => !n.isRead)).toBe(true)
    })

    it('应该正确获取已读通知', () => {
      const read = store.readNotifications
      
      expect(read).toHaveLength(1)
      expect(read.every(n => n.isRead)).toBe(true)
    })

    it('应该正确按类型分组通知', () => {
      const grouped = store.notificationsByType
      
      expect(grouped.message).toHaveLength(1)
      expect(grouped.system).toHaveLength(1)
      expect(grouped.error).toHaveLength(1)
    })

    it('应该正确计算未读数量', () => {
      expect(store.unreadCount).toBe(2)
    })

    it('应该正确检查是否有未读通知', () => {
      expect(store.hasUnreadNotifications).toBe(true)
      
      store.markAllAsRead()
      expect(store.hasUnreadNotifications).toBe(false)
    })

    it('应该正确获取最新通知', () => {
      const latest = store.latestNotification
      
      expect(latest?.title).toBe('Error 1') // 最后添加的
    })
  })

  describe('设置管理', () => {
    it('应该能够更新设置', () => {
      store.updateSettings({
        enabled: false,
        sound: false,
        autoCloseDelay: 3000
      })
      
      expect(store.settings.enabled).toBe(false)
      expect(store.settings.sound).toBe(false)
      expect(store.settings.autoCloseDelay).toBe(3000)
      expect(store.settings.desktop).toBe(true) // 未更新的保持原值
    })

    it('应该能够重置设置', () => {
      store.updateSettings({ enabled: false, sound: false })
      store.resetSettings()
      
      expect(store.settings.enabled).toBe(true)
      expect(store.settings.sound).toBe(true)
    })
  })

  describe('通知过滤', () => {
    beforeEach(() => {
      const now = new Date()
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      
      store.addNotification({
        type: 'message',
        title: 'Recent',
        content: 'Recent message'
      })
      
      // 手动设置创建时间
      store.notifications[0].createdAt = now
      
      store.addNotification({
        type: 'system',
        title: 'Hour ago',
        content: 'Hour ago message'
      })
      store.notifications[1].createdAt = oneHourAgo
      
      store.addNotification({
        type: 'error',
        title: 'Day ago',
        content: 'Day ago message'
      })
      store.notifications[2].createdAt = oneDayAgo
    })

    it('应该能够获取今天的通知', () => {
      const today = store.getTodayNotifications()
      
      expect(today).toHaveLength(2) // 最近的和一小时前的
    })

    it('应该能够按日期范围过滤通知', () => {
      const now = new Date()
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000)
      
      const filtered = store.getNotificationsByDateRange(startOfDay, endOfDay)
      
      expect(filtered).toHaveLength(2)
    })

    it('应该能够按类型过滤通知', () => {
      const messages = store.getNotificationsByType('message')
      const systems = store.getNotificationsByType('system')
      
      expect(messages).toHaveLength(1)
      expect(systems).toHaveLength(1)
    })
  })

  describe('状态重置', () => {
    it('应该能够重置状态', () => {
      // 设置一些状态
      store.addNotification({
        type: 'message',
        title: 'Test',
        content: 'Test message'
      })
      store.updateSettings({ enabled: false })
      
      // 重置状态
      store.resetState()
      
      // 验证状态已重置
      expect(store.notifications).toEqual([])
      expect(store.settings.enabled).toBe(true)
    })
  })
})