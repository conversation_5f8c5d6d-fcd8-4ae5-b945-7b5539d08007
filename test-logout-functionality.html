<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登出功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .storage-info {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>登出功能测试页面</h1>
        <p>此页面用于测试用户、商户、管理员模块的登出功能是否正确调用API</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. 设备信息管理</h3>
            <button onclick="generateDeviceInfo()">生成设备信息</button>
            <button onclick="showStoredDeviceInfo()">查看存储的设备信息</button>
            <button onclick="clearAllDeviceInfo()">清除所有设备信息</button>
            <div id="deviceInfoResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 用户模块登出测试</h3>
            <button onclick="simulateUserLogin()">模拟用户登录</button>
            <button onclick="testUserLogout()" class="danger">测试用户登出</button>
            <div id="userLogoutResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 商户模块登出测试</h3>
            <button onclick="simulateMerchantLogin()">模拟商户登录</button>
            <button onclick="testMerchantLogout()" class="danger">测试商户登出</button>
            <div id="merchantLogoutResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 管理员模块登出测试</h3>
            <button onclick="simulateAdminLogin()">模拟管理员登录</button>
            <button onclick="testAdminLogout()" class="danger">测试管理员登出</button>
            <div id="adminLogoutResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. API调用监控</h3>
            <button onclick="startApiMonitoring()">开始监控API调用</button>
            <button onclick="stopApiMonitoring()">停止监控</button>
            <button onclick="clearApiLog()">清除日志</button>
            <div id="apiMonitorResult" class="result"></div>
        </div>
    </div>

    <script>
        // 设备信息生成工具（与项目中的实现保持一致）
        function generateDeviceInfo() {
            const deviceId = getOrCreateDeviceId();
            const deviceInfo = {
                device_id: deviceId,
                device_name: getDeviceName(),
                device_type: getDeviceType(),
                platform: getPlatform(),
                browser: getBrowserInfo(),
                app_version: '1.0.0',
                os_version: navigator.userAgent,
                user_agent: navigator.userAgent
            };
            
            document.getElementById('deviceInfoResult').innerHTML = 
                `<div class="success">设备信息生成成功:\n${JSON.stringify(deviceInfo, null, 2)}</div>`;
            
            return deviceInfo;
        }

        function getOrCreateDeviceId() {
            let deviceId = localStorage.getItem('device_id');
            if (!deviceId) {
                deviceId = generateUUID();
                localStorage.setItem('device_id', deviceId);
            }
            return deviceId;
        }

        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function getDeviceType() {
            const userAgent = navigator.userAgent;
            if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
                return /iPad/.test(userAgent) ? 'tablet' : 'mobile';
            }
            return 'desktop';
        }

        function getPlatform() {
            const userAgent = navigator.userAgent;
            if (/iPhone|iPad/.test(userAgent)) return 'ios';
            if (/Android/.test(userAgent)) return 'android';
            if (/Windows/.test(userAgent)) return 'windows';
            if (/Mac/.test(userAgent)) return 'macos';
            if (/Linux/.test(userAgent)) return 'linux';
            return 'web';
        }

        function getBrowserInfo() {
            const userAgent = navigator.userAgent;
            if (/Chrome/.test(userAgent)) return 'Chrome';
            if (/Firefox/.test(userAgent)) return 'Firefox';
            if (/Safari/.test(userAgent)) return 'Safari';
            if (/Edge/.test(userAgent)) return 'Edge';
            return 'Unknown';
        }

        function getDeviceName() {
            const platform = getPlatform();
            const browser = getBrowserInfo();
            return `${platform} ${browser}`;
        }

        function showStoredDeviceInfo() {
            const userInfo = localStorage.getItem('user_current_device_info');
            const merchantInfo = localStorage.getItem('merchant_current_device_info');
            const adminInfo = localStorage.getItem('admin_current_device_info');
            
            let result = '存储的设备信息:\n\n';
            result += `用户模块: ${userInfo || '无'}\n\n`;
            result += `商户模块: ${merchantInfo || '无'}\n\n`;
            result += `管理员模块: ${adminInfo || '无'}`;
            
            document.getElementById('deviceInfoResult').innerHTML = 
                `<div class="info">${result}</div>`;
        }

        function clearAllDeviceInfo() {
            localStorage.removeItem('device_id');
            localStorage.removeItem('user_current_device_info');
            localStorage.removeItem('merchant_current_device_info');
            localStorage.removeItem('merchant_device_id');
            localStorage.removeItem('admin_current_device_info');
            
            document.getElementById('deviceInfoResult').innerHTML = 
                `<div class="success">所有设备信息已清除</div>`;
        }

        // 模拟登录功能
        function simulateUserLogin() {
            const deviceInfo = generateDeviceInfo();
            localStorage.setItem('user_current_device_info', JSON.stringify(deviceInfo));
            sessionStorage.setItem('user_access_token', 'mock_user_token_' + Date.now());
            
            document.getElementById('userLogoutResult').innerHTML = 
                `<div class="success">用户登录模拟成功\n设备信息已存储到 user_current_device_info\nToken已存储到 sessionStorage</div>`;
        }

        function simulateMerchantLogin() {
            const deviceInfo = generateDeviceInfo();
            localStorage.setItem('merchant_current_device_info', JSON.stringify(deviceInfo));
            localStorage.setItem('merchant_device_id', deviceInfo.device_id);
            sessionStorage.setItem('merchant_access_token', 'mock_merchant_token_' + Date.now());
            
            document.getElementById('merchantLogoutResult').innerHTML = 
                `<div class="success">商户登录模拟成功\n设备信息已存储\nToken已存储到 sessionStorage</div>`;
        }

        function simulateAdminLogin() {
            const deviceInfo = generateDeviceInfo();
            localStorage.setItem('admin_current_device_info', JSON.stringify(deviceInfo));
            sessionStorage.setItem('admin_access_token', 'mock_admin_token_' + Date.now());
            
            document.getElementById('adminLogoutResult').innerHTML = 
                `<div class="success">管理员登录模拟成功\n设备信息已存储\nToken已存储到 sessionStorage</div>`;
        }

        // API调用监控
        let originalFetch = window.fetch;
        let isMonitoring = false;
        let apiCalls = [];

        function startApiMonitoring() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            apiCalls = [];
            
            window.fetch = function(...args) {
                const url = args[0];
                const options = args[1] || {};
                
                // 记录API调用
                const callInfo = {
                    timestamp: new Date().toISOString(),
                    url: url,
                    method: options.method || 'GET',
                    headers: options.headers || {},
                    body: options.body
                };
                
                apiCalls.push(callInfo);
                updateApiMonitorDisplay();
                
                // 调用原始fetch
                return originalFetch.apply(this, args)
                    .then(response => {
                        callInfo.status = response.status;
                        callInfo.statusText = response.statusText;
                        updateApiMonitorDisplay();
                        return response;
                    })
                    .catch(error => {
                        callInfo.error = error.message;
                        updateApiMonitorDisplay();
                        throw error;
                    });
            };
            
            document.getElementById('apiMonitorResult').innerHTML = 
                `<div class="success">API监控已开始</div>`;
        }

        function stopApiMonitoring() {
            if (!isMonitoring) return;
            
            window.fetch = originalFetch;
            isMonitoring = false;
            
            document.getElementById('apiMonitorResult').innerHTML = 
                `<div class="info">API监控已停止\n\n捕获的API调用:\n${JSON.stringify(apiCalls, null, 2)}</div>`;
        }

        function clearApiLog() {
            apiCalls = [];
            document.getElementById('apiMonitorResult').innerHTML = 
                `<div class="info">API调用日志已清除</div>`;
        }

        function updateApiMonitorDisplay() {
            if (!isMonitoring) return;
            
            const logoutCalls = apiCalls.filter(call => call.url.includes('logout'));
            
            let display = `<div class="info">监控中... 已捕获 ${apiCalls.length} 个API调用`;
            
            if (logoutCalls.length > 0) {
                display += `\n\n登出相关API调用:\n${JSON.stringify(logoutCalls, null, 2)}`;
            }
            
            display += '</div>';
            
            document.getElementById('apiMonitorResult').innerHTML = display;
        }

        // 测试登出功能
        function testUserLogout() {
            const deviceInfo = localStorage.getItem('user_current_device_info');
            
            if (!deviceInfo) {
                document.getElementById('userLogoutResult').innerHTML = 
                    `<div class="error">错误: 未找到用户设备信息，请先模拟登录</div>`;
                return;
            }
            
            const parsedDeviceInfo = JSON.parse(deviceInfo);
            const deviceId = parsedDeviceInfo.device_id;
            
            if (!deviceId) {
                document.getElementById('userLogoutResult').innerHTML = 
                    `<div class="error">错误: 设备信息中缺少device_id</div>`;
                return;
            }
            
            // 模拟调用登出API
            const apiUrl = `/v1/user/devices/${deviceId}/logout`;
            
            document.getElementById('userLogoutResult').innerHTML = 
                `<div class="info">正在测试用户登出...\n\nAPI调用: POST ${apiUrl}\n设备ID: ${deviceId}\n\n注意: 这是模拟调用，实际项目中会发送真实请求</div>`;
            
            // 清除本地存储（模拟登出成功）
            setTimeout(() => {
                localStorage.removeItem('user_current_device_info');
                sessionStorage.removeItem('user_access_token');
                
                document.getElementById('userLogoutResult').innerHTML = 
                    `<div class="success">用户登出测试完成\n\nAPI调用: POST ${apiUrl}\n设备ID: ${deviceId}\n本地存储已清除</div>`;
            }, 1000);
        }

        function testMerchantLogout() {
            const deviceInfo = localStorage.getItem('merchant_current_device_info');
            
            if (!deviceInfo) {
                document.getElementById('merchantLogoutResult').innerHTML = 
                    `<div class="error">错误: 未找到商户设备信息，请先模拟登录</div>`;
                return;
            }
            
            const parsedDeviceInfo = JSON.parse(deviceInfo);
            const deviceId = parsedDeviceInfo.device_id;
            
            if (!deviceId) {
                document.getElementById('merchantLogoutResult').innerHTML = 
                    `<div class="error">错误: 设备信息中缺少device_id</div>`;
                return;
            }
            
            const apiUrl = `/v1/merchant/devices/${deviceId}/logout`;
            
            document.getElementById('merchantLogoutResult').innerHTML = 
                `<div class="info">正在测试商户登出...\n\nAPI调用: POST ${apiUrl}\n设备ID: ${deviceId}</div>`;
            
            setTimeout(() => {
                localStorage.removeItem('merchant_current_device_info');
                localStorage.removeItem('merchant_device_id');
                sessionStorage.removeItem('merchant_access_token');
                
                document.getElementById('merchantLogoutResult').innerHTML = 
                    `<div class="success">商户登出测试完成\n\nAPI调用: POST ${apiUrl}\n设备ID: ${deviceId}\n本地存储已清除</div>`;
            }, 1000);
        }

        function testAdminLogout() {
            const deviceInfo = localStorage.getItem('admin_current_device_info');
            
            if (!deviceInfo) {
                document.getElementById('adminLogoutResult').innerHTML = 
                    `<div class="error">错误: 未找到管理员设备信息，请先模拟登录</div>`;
                return;
            }
            
            const parsedDeviceInfo = JSON.parse(deviceInfo);
            const deviceId = parsedDeviceInfo.device_id;
            
            if (!deviceId) {
                document.getElementById('adminLogoutResult').innerHTML = 
                    `<div class="error">错误: 设备信息中缺少device_id</div>`;
                return;
            }
            
            const apiUrl = `/v1/admin/devices/${deviceId}/logout`;
            
            document.getElementById('adminLogoutResult').innerHTML = 
                `<div class="info">正在测试管理员登出...\n\nAPI调用: POST ${apiUrl}\n设备ID: ${deviceId}</div>`;
            
            setTimeout(() => {
                localStorage.removeItem('admin_current_device_info');
                sessionStorage.removeItem('admin_access_token');
                
                document.getElementById('adminLogoutResult').innerHTML = 
                    `<div class="success">管理员登出测试完成\n\nAPI调用: POST ${apiUrl}\n设备ID: ${deviceId}\n本地存储已清除</div>`;
            }, 1000);
        }

        // 页面加载时显示当前存储的设备信息
        window.onload = function() {
            showStoredDeviceInfo();
        };
    </script>
</body>
</html>