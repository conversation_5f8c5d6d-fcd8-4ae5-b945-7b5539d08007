/**
 * SessionList 组件单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import SessionList from '../../../components/SessionList.vue'
import { useSessionStore } from '../../../stores/session'
import { useChatStore } from '../../../stores/chat'
import type { Session } from '../../../types'

// Mock 图标组件
vi.mock('@/components/ui/Icon.vue', () => ({
  default: {
    name: 'Icon',
    template: '<span class="icon" :class="name">{{ name }}</span>',
    props: ['name']
  }
}))

// Mock 头像组件
vi.mock('@/components/ui/Avatar.vue', () => ({
  default: {
    name: 'Avatar',
    template: '<div class="avatar" :class="{ online: isOnline }">{{ name?.[0] }}</div>',
    props: ['src', 'name', 'size', 'isOnline']
  }
}))

// Mock 徽章组件
vi.mock('@/components/ui/Badge.vue', () => ({
  default: {
    name: 'Badge',
    template: '<span class="badge" :class="type">{{ count }}</span>',
    props: ['count', 'type', 'max']
  }
}))

describe('SessionList', () => {
  let wrapper: any
  let sessionStore: any
  let chatStore: any

  const mockSessions: Session[] = [
    {
      id: 'session1',
      name: 'John Doe',
      type: 'private',
      avatar: 'https://example.com/avatar1.jpg',
      lastMessage: {
        id: 'msg1',
        content: 'Hello there!',
        senderId: 'user1',
        senderName: 'John',
        timestamp: new Date('2024-01-15T10:00:00Z'),
        type: 'text'
      },
      unreadCount: 2,
      isOnline: true,
      isPinned: false,
      isMuted: false,
      isArchived: false,
      participants: [
        { id: 'user1', name: 'John Doe', avatar: 'avatar1.jpg', isOnline: true }
      ],
      createdAt: new Date('2024-01-10T10:00:00Z'),
      updatedAt: new Date('2024-01-15T10:00:00Z')
    },
    {
      id: 'session2',
      name: 'Team Chat',
      type: 'group',
      avatar: 'https://example.com/avatar2.jpg',
      lastMessage: {
        id: 'msg2',
        content: 'Meeting at 3 PM',
        senderId: 'user2',
        senderName: 'Alice',
        timestamp: new Date('2024-01-15T09:00:00Z'),
        type: 'text'
      },
      unreadCount: 0,
      isOnline: false,
      isPinned: true,
      isMuted: false,
      isArchived: false,
      participants: [
        { id: 'user2', name: 'Alice', avatar: 'avatar2.jpg', isOnline: true },
        { id: 'user3', name: 'Bob', avatar: 'avatar3.jpg', isOnline: false }
      ],
      createdAt: new Date('2024-01-08T10:00:00Z'),
      updatedAt: new Date('2024-01-15T09:00:00Z')
    },
    {
      id: 'session3',
      name: 'Support Channel',
      type: 'channel',
      avatar: 'https://example.com/avatar3.jpg',
      lastMessage: {
        id: 'msg3',
        content: 'Issue resolved',
        senderId: 'user4',
        senderName: 'Support',
        timestamp: new Date('2024-01-14T15:00:00Z'),
        type: 'text'
      },
      unreadCount: 5,
      isOnline: false,
      isPinned: false,
      isMuted: true,
      isArchived: false,
      participants: [
        { id: 'user4', name: 'Support', avatar: 'avatar4.jpg', isOnline: true }
      ],
      createdAt: new Date('2024-01-05T10:00:00Z'),
      updatedAt: new Date('2024-01-14T15:00:00Z')
    }
  ]

  beforeEach(() => {
    setActivePinia(createPinia())
    
    sessionStore = useSessionStore()
    chatStore = useChatStore()
    
    // 设置 mock 数据
    sessionStore.sessions = mockSessions
    sessionStore.currentSessionId = null
    sessionStore.searchQuery = ''
    sessionStore.filterType = 'all'
    
    // Mock store 方法
    vi.spyOn(sessionStore, 'selectSession').mockResolvedValue(undefined)
    vi.spyOn(sessionStore, 'loadSessions').mockResolvedValue(undefined)
    vi.spyOn(sessionStore, 'createSession').mockResolvedValue(mockSessions[0])
    vi.spyOn(sessionStore, 'deleteSession').mockResolvedValue(undefined)
    vi.spyOn(sessionStore, 'togglePin').mockResolvedValue(undefined)
    vi.spyOn(sessionStore, 'toggleMute').mockResolvedValue(undefined)
    vi.spyOn(sessionStore, 'toggleArchive').mockResolvedValue(undefined)
    vi.spyOn(sessionStore, 'clearUnreadCount').mockImplementation(() => {})
    
    wrapper = mount(SessionList, {
      global: {
        plugins: [createPinia()]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染会话列表结构', () => {
      expect(wrapper.find('.session-list').exists()).toBe(true)
      expect(wrapper.find('.session-list-header').exists()).toBe(true)
      expect(wrapper.find('.session-list-body').exists()).toBe(true)
    })

    it('应该渲染搜索框', () => {
      const searchInput = wrapper.find('.search-input')
      expect(searchInput.exists()).toBe(true)
      expect(searchInput.attributes('placeholder')).toContain('搜索会话')
    })

    it('应该渲染过滤按钮', () => {
      expect(wrapper.find('.filter-all').exists()).toBe(true)
      expect(wrapper.find('.filter-unread').exists()).toBe(true)
      expect(wrapper.find('.filter-pinned').exists()).toBe(true)
      expect(wrapper.find('.filter-muted').exists()).toBe(true)
      expect(wrapper.find('.filter-archived').exists()).toBe(true)
    })

    it('应该渲染创建会话按钮', () => {
      const createBtn = wrapper.find('.create-session-btn')
      expect(createBtn.exists()).toBe(true)
    })

    it('应该渲染所有会话项', () => {
      const sessionItems = wrapper.findAll('.session-item')
      expect(sessionItems).toHaveLength(mockSessions.length)
    })
  })

  describe('会话项渲染', () => {
    it('应该正确显示会话基本信息', () => {
      const firstSession = wrapper.find('.session-item')
      
      expect(firstSession.find('.session-name').text()).toBe(mockSessions[0].name)
      expect(firstSession.find('.last-message').text()).toBe(mockSessions[0].lastMessage?.content)
    })

    it('应该显示未读消息数量', () => {
      const sessionWithUnread = wrapper.findAll('.session-item')[0]
      const badge = sessionWithUnread.find('.badge')
      
      expect(badge.exists()).toBe(true)
      expect(badge.text()).toBe('2')
    })

    it('应该显示置顶标识', () => {
      const pinnedSession = wrapper.findAll('.session-item')[1]
      expect(pinnedSession.classes()).toContain('pinned')
    })

    it('应该显示静音标识', () => {
      const mutedSession = wrapper.findAll('.session-item')[2]
      expect(mutedSession.find('.muted-icon').exists()).toBe(true)
    })

    it('应该显示在线状态', () => {
      const onlineSession = wrapper.findAll('.session-item')[0]
      const avatar = onlineSession.find('.avatar')
      expect(avatar.classes()).toContain('online')
    })

    it('应该显示会话类型图标', () => {
      const groupSession = wrapper.findAll('.session-item')[1]
      const typeIcon = groupSession.find('.session-type-icon')
      expect(typeIcon.exists()).toBe(true)
    })

    it('应该显示最后消息时间', () => {
      const sessionItem = wrapper.find('.session-item')
      const timestamp = sessionItem.find('.timestamp')
      expect(timestamp.exists()).toBe(true)
    })
  })

  describe('会话选择', () => {
    it('应该能够选择会话', async () => {
      const sessionItem = wrapper.find('.session-item')
      await sessionItem.trigger('click')
      
      expect(sessionStore.selectSession).toHaveBeenCalledWith(mockSessions[0].id)
      expect(wrapper.emitted('session-select')).toBeTruthy()
      expect(wrapper.emitted('session-select')[0]).toEqual([mockSessions[0].id])
    })

    it('应该高亮当前选中的会话', async () => {
      sessionStore.currentSessionId = mockSessions[0].id
      await wrapper.vm.$nextTick()
      
      const selectedSession = wrapper.find('.session-item')
      expect(selectedSession.classes()).toContain('active')
    })

    it('应该清除未读数量当选择会话时', async () => {
      const sessionItem = wrapper.find('.session-item')
      await sessionItem.trigger('click')
      
      expect(sessionStore.clearUnreadCount).toHaveBeenCalledWith(mockSessions[0].id)
    })
  })

  describe('搜索功能', () => {
    it('应该能够搜索会话', async () => {
      const searchInput = wrapper.find('.search-input')
      await searchInput.setValue('John')
      
      expect(sessionStore.searchQuery).toBe('John')
    })

    it('应该显示搜索结果', async () => {
      sessionStore.searchQuery = 'Team'
      await wrapper.vm.$nextTick()
      
      const sessionItems = wrapper.findAll('.session-item')
      // 应该只显示匹配的会话
      expect(sessionItems.length).toBeLessThanOrEqual(mockSessions.length)
    })

    it('应该能够清除搜索', async () => {
      const searchInput = wrapper.find('.search-input')
      await searchInput.setValue('test')
      
      const clearBtn = wrapper.find('.clear-search-btn')
      await clearBtn.trigger('click')
      
      expect(sessionStore.searchQuery).toBe('')
    })

    it('应该显示无搜索结果提示', async () => {
      sessionStore.searchQuery = 'nonexistent'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.no-results').exists()).toBe(true)
    })
  })

  describe('过滤功能', () => {
    it('应该能够过滤所有会话', async () => {
      const filterBtn = wrapper.find('.filter-all')
      await filterBtn.trigger('click')
      
      expect(sessionStore.filterType).toBe('all')
      expect(filterBtn.classes()).toContain('active')
    })

    it('应该能够过滤未读会话', async () => {
      const filterBtn = wrapper.find('.filter-unread')
      await filterBtn.trigger('click')
      
      expect(sessionStore.filterType).toBe('unread')
      expect(filterBtn.classes()).toContain('active')
    })

    it('应该能够过滤置顶会话', async () => {
      const filterBtn = wrapper.find('.filter-pinned')
      await filterBtn.trigger('click')
      
      expect(sessionStore.filterType).toBe('pinned')
      expect(filterBtn.classes()).toContain('active')
    })

    it('应该能够过滤静音会话', async () => {
      const filterBtn = wrapper.find('.filter-muted')
      await filterBtn.trigger('click')
      
      expect(sessionStore.filterType).toBe('muted')
      expect(filterBtn.classes()).toContain('active')
    })

    it('应该能够过滤归档会话', async () => {
      const filterBtn = wrapper.find('.filter-archived')
      await filterBtn.trigger('click')
      
      expect(sessionStore.filterType).toBe('archived')
      expect(filterBtn.classes()).toContain('active')
    })
  })

  describe('会话操作', () => {
    it('应该显示右键菜单', async () => {
      const sessionItem = wrapper.find('.session-item')
      await sessionItem.trigger('contextmenu')
      
      expect(wrapper.find('.context-menu').exists()).toBe(true)
    })

    it('应该能够置顶/取消置顶会话', async () => {
      const sessionItem = wrapper.find('.session-item')
      await sessionItem.trigger('contextmenu')
      
      const pinBtn = wrapper.find('.pin-session-btn')
      await pinBtn.trigger('click')
      
      expect(sessionStore.togglePin).toHaveBeenCalledWith(mockSessions[0].id)
    })

    it('应该能够静音/取消静音会话', async () => {
      const sessionItem = wrapper.find('.session-item')
      await sessionItem.trigger('contextmenu')
      
      const muteBtn = wrapper.find('.mute-session-btn')
      await muteBtn.trigger('click')
      
      expect(sessionStore.toggleMute).toHaveBeenCalledWith(mockSessions[0].id)
    })

    it('应该能够归档/取消归档会话', async () => {
      const sessionItem = wrapper.find('.session-item')
      await sessionItem.trigger('contextmenu')
      
      const archiveBtn = wrapper.find('.archive-session-btn')
      await archiveBtn.trigger('click')
      
      expect(sessionStore.toggleArchive).toHaveBeenCalledWith(mockSessions[0].id)
    })

    it('应该能够删除会话', async () => {
      const sessionItem = wrapper.find('.session-item')
      await sessionItem.trigger('contextmenu')
      
      const deleteBtn = wrapper.find('.delete-session-btn')
      await deleteBtn.trigger('click')
      
      // 应该显示确认对话框
      expect(wrapper.find('.delete-confirm-dialog').exists()).toBe(true)
      
      // 确认删除
      const confirmBtn = wrapper.find('.confirm-delete-btn')
      await confirmBtn.trigger('click')
      
      expect(sessionStore.deleteSession).toHaveBeenCalledWith(mockSessions[0].id)
    })
  })

  describe('会话创建', () => {
    it('应该显示创建会话对话框', async () => {
      const createBtn = wrapper.find('.create-session-btn')
      await createBtn.trigger('click')
      
      expect(wrapper.find('.create-session-dialog').exists()).toBe(true)
    })

    it('应该能够创建私聊会话', async () => {
      const createBtn = wrapper.find('.create-session-btn')
      await createBtn.trigger('click')
      
      const privateBtn = wrapper.find('.create-private-btn')
      await privateBtn.trigger('click')
      
      expect(wrapper.emitted('create-session')).toBeTruthy()
      expect(wrapper.emitted('create-session')[0]).toEqual([{ type: 'private' }])
    })

    it('应该能够创建群聊会话', async () => {
      const createBtn = wrapper.find('.create-session-btn')
      await createBtn.trigger('click')
      
      const groupBtn = wrapper.find('.create-group-btn')
      await groupBtn.trigger('click')
      
      expect(wrapper.emitted('create-session')).toBeTruthy()
      expect(wrapper.emitted('create-session')[0]).toEqual([{ type: 'group' }])
    })

    it('应该能够创建频道会话', async () => {
      const createBtn = wrapper.find('.create-session-btn')
      await createBtn.trigger('click')
      
      const channelBtn = wrapper.find('.create-channel-btn')
      await channelBtn.trigger('click')
      
      expect(wrapper.emitted('create-session')).toBeTruthy()
      expect(wrapper.emitted('create-session')[0]).toEqual([{ type: 'channel' }])
    })
  })

  describe('拖拽排序', () => {
    it('应该支持拖拽开始', async () => {
      const sessionItem = wrapper.find('.session-item')
      const dragEvent = new DragEvent('dragstart')
      
      await sessionItem.trigger('dragstart', dragEvent)
      
      expect(wrapper.vm.draggedSessionId).toBe(mockSessions[0].id)
    })

    it('应该支持拖拽放置', async () => {
      const sessionItems = wrapper.findAll('.session-item')
      
      // 开始拖拽第一个会话
      wrapper.vm.draggedSessionId = mockSessions[0].id
      
      // 放置到第二个会话位置
      const dropEvent = new DragEvent('drop')
      await sessionItems[1].trigger('drop', dropEvent)
      
      expect(wrapper.emitted('session-reorder')).toBeTruthy()
    })
  })

  describe('键盘导航', () => {
    it('应该支持上下箭头键导航', async () => {
      const sessionList = wrapper.find('.session-list-body')
      
      // 按下箭头键
      await sessionList.trigger('keydown', { key: 'ArrowDown' })
      
      expect(wrapper.vm.focusedIndex).toBe(0)
      
      await sessionList.trigger('keydown', { key: 'ArrowDown' })
      expect(wrapper.vm.focusedIndex).toBe(1)
      
      await sessionList.trigger('keydown', { key: 'ArrowUp' })
      expect(wrapper.vm.focusedIndex).toBe(0)
    })

    it('应该支持回车键选择会话', async () => {
      const sessionList = wrapper.find('.session-list-body')
      wrapper.vm.focusedIndex = 0
      
      await sessionList.trigger('keydown', { key: 'Enter' })
      
      expect(sessionStore.selectSession).toHaveBeenCalledWith(mockSessions[0].id)
    })

    it('应该支持删除键删除会话', async () => {
      const sessionList = wrapper.find('.session-list-body')
      wrapper.vm.focusedIndex = 0
      
      await sessionList.trigger('keydown', { key: 'Delete' })
      
      expect(wrapper.find('.delete-confirm-dialog').exists()).toBe(true)
    })
  })

  describe('加载状态', () => {
    it('应该显示加载指示器', async () => {
      sessionStore.loading = true
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.loading-indicator').exists()).toBe(true)
    })

    it('应该显示空状态', async () => {
      sessionStore.sessions = []
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.empty-state').exists()).toBe(true)
    })

    it('应该显示错误状态', async () => {
      sessionStore.error = 'Failed to load sessions'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.error-state').exists()).toBe(true)
      expect(wrapper.find('.error-message').text()).toBe('Failed to load sessions')
    })
  })

  describe('无限滚动', () => {
    it('应该在滚动到底部时加载更多会话', async () => {
      vi.spyOn(sessionStore, 'loadMoreSessions').mockResolvedValue(undefined)
      
      const scrollContainer = wrapper.find('.session-list-body')
      
      // 模拟滚动到底部
      Object.defineProperty(scrollContainer.element, 'scrollTop', {
        value: 1000,
        writable: true
      })
      Object.defineProperty(scrollContainer.element, 'scrollHeight', {
        value: 1100,
        writable: true
      })
      Object.defineProperty(scrollContainer.element, 'clientHeight', {
        value: 500,
        writable: true
      })
      
      await scrollContainer.trigger('scroll')
      
      expect(sessionStore.loadMoreSessions).toHaveBeenCalled()
    })
  })

  describe('响应式设计', () => {
    it('应该在移动端隐藏某些操作按钮', async () => {
      // 模拟移动端
      Object.defineProperty(window, 'innerWidth', {
        value: 768,
        writable: true
      })
      
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.isMobile).toBe(true)
    })
  })
})