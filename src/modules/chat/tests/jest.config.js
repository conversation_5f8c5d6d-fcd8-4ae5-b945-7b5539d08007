/**
 * Jest 配置文件
 * 用于单元测试和集成测试
 */

module.exports = {
  // 测试环境
  testEnvironment: 'jsdom',
  
  // 根目录
  rootDir: '../',
  
  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/tests/unit/**/*.test.{js,ts}',
    '<rootDir>/tests/integration/**/*.test.{js,ts}'
  ],
  
  // 忽略的测试文件
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/',
    '/e2e/'
  ],
  
  // 模块文件扩展名
  moduleFileExtensions: [
    'js',
    'ts',
    'json',
    'vue'
  ],
  
  // 模块名映射
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^~/(.*)$': '<rootDir>/$1',
    '^@chat/(.*)$': '<rootDir>/src/modules/chat/$1'
  },
  
  // 转换配置
  transform: {
    '^.+\.vue$': '@vue/vue3-jest',
    '^.+\.(ts|tsx)$': 'ts-jest',
    '^.+\.(js|jsx)$': 'babel-jest'
  },
  
  // 转换忽略模式
  transformIgnorePatterns: [
    'node_modules/(?!(.*\.mjs$|@vue/.*|vue-.*))'
  ],
  
  // 设置文件
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup/test-setup.ts'
  ],
  
  // 全局设置
  globals: {
    'ts-jest': {
      useESM: true,
      tsconfig: {
        compilerOptions: {
          target: 'esnext',
          module: 'esnext',
          moduleResolution: 'node',
          allowSyntheticDefaultImports: true,
          esModuleInterop: true,
          skipLibCheck: true,
          strict: true
        }
      }
    },
    '__VUE_OPTIONS_API__': true,
    '__VUE_PROD_DEVTOOLS__': false
  },
  
  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{js,ts,vue}',
    '!src/**/*.d.ts',
    '!src/main.ts',
    '!src/App.vue',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/coverage/**',
    '!**/tests/**'
  ],
  
  // 覆盖率报告
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json'
  ],
  
  // 覆盖率输出目录
  coverageDirectory: '<rootDir>/tests/coverage',
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/modules/chat/components/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    },
    './src/modules/chat/stores/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  
  // 测试超时
  testTimeout: 10000,
  
  // 并发运行
  maxConcurrency: 5,
  
  // 详细输出
  verbose: true,
  
  // 静默模式
  silent: false,
  
  // 错误时停止
  bail: false,
  
  // 缓存
  cache: true,
  cacheDirectory: '<rootDir>/tests/.jest-cache',
  
  // 清除模拟
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
  
  // 监听模式
  watchman: true,
  
  // 报告器
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: '<rootDir>/tests/reports',
        filename: 'jest-report.html',
        expand: true,
        hideIcon: false,
        pageTitle: 'Chat Module Test Report'
      }
    ],
    [
      'jest-junit',
      {
        outputDirectory: '<rootDir>/tests/reports',
        outputName: 'junit.xml',
        ancestorSeparator: ' › ',
        uniqueOutputName: 'false',
        suiteNameTemplate: '{filepath}',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}'
      }
    ]
  ],
  
  // 错误处理
  errorOnDeprecated: true,
  
  // 模拟配置
  modulePathIgnorePatterns: [
    '<rootDir>/dist/'
  ],
  
  // 预设
  preset: 'ts-jest/presets/default-esm',
  
  // 扩展名解析
  extensionsToTreatAsEsm: ['.ts', '.vue'],
  
  // 测试环境选项
  testEnvironmentOptions: {
    customExportConditions: ['node', 'node-addons']
  },
  
  // 快照序列化器
  snapshotSerializers: [
    '<rootDir>/node_modules/jest-serializer-vue'
  ],
  
  // 自定义匹配器
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup/test-setup.ts',
    '<rootDir>/tests/setup/custom-matchers.ts'
  ],
  
  // 项目配置（多项目支持）
  projects: [
    {
      displayName: 'unit',
      testMatch: ['<rootDir>/tests/unit/**/*.test.{js,ts}'],
      testEnvironment: 'jsdom'
    },
    {
      displayName: 'integration',
      testMatch: ['<rootDir>/tests/integration/**/*.test.{js,ts}'],
      testEnvironment: 'jsdom'
    }
  ],
  
  // 全局变量
  globals: {
    'ts-jest': {
      useESM: true
    },
    '__DEV__': true,
    '__TEST__': true,
    '__VERSION__': '1.0.0',
    '__VUE_OPTIONS_API__': true,
    '__VUE_PROD_DEVTOOLS__': false
  },
  
  // 依赖提取
  dependencyExtractor: '<rootDir>/tests/utils/dependency-extractor.js',
  
  // 自定义解析器
  resolver: '<rootDir>/tests/utils/custom-resolver.js',
  
  // 运行器
  runner: 'jest-runner',
  
  // 测试结果处理器
  testResultsProcessor: '<rootDir>/tests/utils/test-results-processor.js',
  
  // 监听插件
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ],
  
  // 强制退出
  forceExit: false,
  
  // 检测打开句柄
  detectOpenHandles: true,
  
  // 检测泄漏
  detectLeaks: false,
  
  // 最大工作进程
  maxWorkers: '50%',
  
  // 通知
  notify: false,
  notifyMode: 'failure-change',
  
  // 只有失败的测试
  onlyFailures: false,
  
  // 通过时退出
  passWithNoTests: true,
  
  // 随机化
  randomize: false,
  
  // 运行串行
  runInBand: false,
  
  // 种子
  seed: undefined,
  
  // 更新快照
  updateSnapshot: false,
  
  // 使用stderr
  useStderr: false,
  
  // 监听所有
  watchAll: false
}