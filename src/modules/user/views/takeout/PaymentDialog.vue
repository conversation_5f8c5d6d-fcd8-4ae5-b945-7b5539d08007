<template>
  <el-dialog
    v-model="visible"
    title="订单支付"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <!-- 订单信息 -->
    <div class="payment-order-info" v-if="orderInfoList && orderInfoList.length > 0">
      <h3>订单信息</h3>
      
      <!-- 单个订单显示 -->
      <template v-if="orderInfoList.length === 1">
        <div class="order-details">
          <div class="order-row">
            <span class="label">订单号:</span>
            <span class="value">{{ orderInfoList[0].orderNumber }}</span>
          </div>
          <div class="order-row" v-if="orderInfoList[0].merchantName">
            <span class="label">商家:</span>
            <span class="value">{{ orderInfoList[0].merchantName }}</span>
          </div>
          <div class="order-row">
            <span class="label">下单时间:</span>
            <span class="value">{{ formatDate(orderInfoList[0].createdAt) }}</span>
          </div>
          <div class="order-row">
            <span class="label">支付金额:</span>
            <span class="value amount">¥{{ (orderInfoList[0].payAmount || orderInfoList[0].totalAmount).toFixed(2) }}</span>
          </div>
        </div>
      </template>
      
      <!-- 多个订单显示 -->
      <template v-else>
        <div class="multi-order-summary">
          <div class="order-row">
            <span class="label">订单数量:</span>
            <span class="value">{{ orderInfoList.length }} 个订单</span>
          </div>
          <div class="order-row">
            <span class="label">下单时间:</span>
            <span class="value">{{ formatDate(orderInfoList[0].createdAt) }}</span>
          </div>
          <div class="order-row">
            <span class="label">支付总金额:</span>
            <span class="value amount">¥{{ totalPaymentAmount.toFixed(2) }}</span>
          </div>
        </div>
        
        <!-- 订单详情列表 -->
        <div class="order-list">
          <div 
            v-for="(order, index) in orderInfoList" 
            :key="order.id"
            class="order-item"
          >
            <div class="order-item-header">
              <span class="order-index">订单{{ index + 1 }}</span>
              <span class="order-number">{{ order.orderNumber }}</span>
            </div>
            <div class="order-item-details">
              <div class="order-item-row" v-if="order.merchantName">
                <span class="label">商家:</span>
                <span class="value">{{ order.merchantName }}</span>
              </div>
              <div class="order-item-row">
                <span class="label">金额:</span>
                <span class="value">¥{{ (order.payAmount || order.totalAmount).toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 支付方式选择 -->
    <div class="payment-methods" v-if="!showPaymentInterface">
      <h3>选择支付方式</h3>
      <el-radio-group v-model="selectedPaymentMethod" class="payment-method-group">
        <el-radio
          v-for="method in paymentMethods"
          :key="method.value"
          :value="method.value"
          class="payment-method-item"
        >
          <div class="method-content">
            <el-icon class="method-icon">
              <component :is="method.icon" />
            </el-icon>
            <span class="method-text">{{ method.label }}</span>
          </div>
        </el-radio>
      </el-radio-group>
    </div>

    <!-- 支付界面 -->
    <div class="payment-interface" v-if="showPaymentInterface && paymentInfo">
      <div class="payment-header">
        <h3>{{ getPaymentMethodText(paymentInfo.paymentMethod) }}</h3>
        <div class="payment-amount">支付金额: ¥{{ totalPaymentAmount.toFixed(2) }}</div>
      </div>

      <!-- 二维码支付 -->
      <div class="qr-payment" v-if="paymentInfo.qrCode || paymentInfo.codeUrl">
        <div class="qr-container">
          <div class="qr-code">
            <img v-if="paymentInfo.codeUrl" :src="paymentInfo.codeUrl" alt="支付二维码" />
            <div v-else class="qr-placeholder">
              <el-icon size="48"><QrCode /></el-icon>
              <p>二维码生成中...</p>
            </div>
          </div>
        </div>
        
        <div class="payment-tips">
          <p>请使用{{ getPaymentMethodText(paymentInfo.paymentMethod) }}扫描上方二维码完成支付</p>
          <div class="countdown" v-if="countdown > 0">
            支付剩余时间: {{ formatCountdown(countdown) }}
          </div>
        </div>
      </div>

      <!-- 支付说明 -->
      <div class="payment-notes">
        <ul>
          <li>请在15分钟内完成支付</li>
          <li>支付成功后将自动跳转</li>
          <li>如遇问题请联系客服</li>
        </ul>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <template v-if="!showPaymentInterface">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            :loading="loading"
            @click="handleCreatePayment"
          >
            立即支付 ¥{{ totalPaymentAmount.toFixed(2) }}
          </el-button>
        </template>
        <template v-else>
          <el-button @click="handleRefreshQR" :loading="loading">
            刷新二维码
          </el-button>
          <el-button @click="handleBackToMethodSelect">
            选择其他支付方式
          </el-button>
          <el-button @click="handleClose">取消支付</el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { CreditCard, Wallet, Money, Grid } from '@element-plus/icons-vue';
import {
  PaymentMethod,
  PaymentStatus,
  type PaymentInfo,
  createOrderPayment,
  createTakeoutPayment,
  getOrderPaymentStatus,
  getTakeoutPaymentStatus,
  getPaymentMethodText,
  type CreatePaymentRequest
} from '@/modules/user/api/payment';

// 订单信息接口
interface OrderInfo {
  id: number;
  orderNumber: string;
  totalAmount: number;
  payAmount: number; // 添加可选的支付金额字段
  createdAt: string;
  merchantName?: string;
  orderType: 'general' | 'takeout';
}

// Props
interface Props {
  visible: boolean;
  orderInfo: OrderInfo | OrderInfo[] | null;
}

// Emits
interface Emits {
  'update:visible': [value: boolean];
  'payment-success': [paymentData: any];
  'payment-failed': [error: any];
  'payment-timeout': [];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const showPaymentInterface = ref(false);
const selectedPaymentMethod = ref<PaymentMethod>(PaymentMethod.WECHAT);
const paymentInfo = ref<PaymentInfo | null>(null);
const countdown = ref(0);
const pollingTimer = ref<ReturnType<typeof setInterval> | null>(null);
const countdownTimer = ref<ReturnType<typeof setInterval> | null>(null);

// 支付方式配置
const paymentMethods = [
  {
    value: PaymentMethod.WECHAT,
    label: '微信支付',
    icon: Wallet
  },
  {
    value: PaymentMethod.ALIPAY,
    label: '支付宝',
    icon: Wallet
  },
  {
    value: PaymentMethod.CREDITCARD,
    label: '信用卡',
    icon: CreditCard
  },
  {
    value: PaymentMethod.BANK_TRANSFER,
    label: '银行转账',
    icon: Money
  },
  {
    value: PaymentMethod.BALANCE,
    label: '余额支付',
    icon: Wallet
  },
  {
    value: PaymentMethod.COMBINATION,
    label: '组合支付',
    icon: Grid
  }
];

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 将订单信息统一转换为数组格式
const orderInfoList = computed(() => {
  if (!props.orderInfo) return [];
  return Array.isArray(props.orderInfo) ? props.orderInfo : [props.orderInfo];
});

// 计算总支付金额
const totalPaymentAmount = computed(() => {
  return orderInfoList.value.reduce((sum, order) => sum + order.payAmount, 0);
});

// 获取主订单（用于支付创建）
const primaryOrder = computed(() => {
  return orderInfoList.value.length > 0 ? orderInfoList.value[0] : null;
});

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetPaymentState();
  } else {
    clearTimers();
  }
});

/**
 * 重置支付状态
 */
function resetPaymentState() {
  showPaymentInterface.value = false;
  selectedPaymentMethod.value = PaymentMethod.WECHAT;
  paymentInfo.value = null;
  countdown.value = 0;
  clearTimers();
}

/**
 * 清除定时器
 */
function clearTimers() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
}

/**
 * 创建支付
 */
async function handleCreatePayment() {
  if (!primaryOrder.value) return;
  
  try {
    loading.value = true;
    
    let paymentResult;
    
    if (orderInfoList.value.length === 1) {
      // 单订单支付
      const order = primaryOrder.value;
      if (order.orderType === 'takeout') {
        // 外卖订单支付
        const params: CreatePaymentRequest = {
          payment_method: selectedPaymentMethod.value,
          payment_amount: order.totalAmount
        };
        paymentResult = await createTakeoutPayment(order.id, params);
      } else {
        // 普通订单支付
        paymentResult = await createOrderPayment(order.id, selectedPaymentMethod.value);
      }
    } else {
      // 多订单支付 - 使用第一个订单创建支付，但支付金额为总金额
      const order = primaryOrder.value;
      if (order.orderType === 'takeout') {
        const params: CreatePaymentRequest = {
          payment_method: selectedPaymentMethod.value,
          payment_amount: totalPaymentAmount.value,
          order_ids: orderInfoList.value.map(o => o.id) // 传递所有订单ID
        };
        paymentResult = await createTakeoutPayment(order.id, params);
      } else {
        // 普通订单暂不支持多订单支付
        ElMessage.error('普通订单暂不支持多订单支付');
        return;
      }
    }
    
    // 检查支付结果（注意：响应拦截器已经返回了data部分）
    if (!paymentResult) {
      ElMessage.error('支付创建失败：服务器响应异常');
      return;
    }

    // 保存支付信息
    paymentInfo.value = {
      paymentId: paymentResult.paymentID,
      orderId: primaryOrder.value.id,
      paymentMethod: selectedPaymentMethod.value,
      paymentAmount: totalPaymentAmount.value,
      status: PaymentStatus.PENDING,
      qrCode: paymentResult.qrCodeURL,
      codeUrl: paymentResult.paymentURL,
      expiresAt: paymentResult.expireTime.toString(),
      orderIds: orderInfoList.value.map(o => o.id) // 保存所有订单ID
    };
    
    // 检查是否为余额支付且已成功
    const appPayParams = paymentResult.appPayParams;
    const webPayParams = paymentResult.webPayParams;
    
    if (appPayParams || webPayParams) {
      try {
        const payParamsStr = appPayParams || webPayParams;
        if (payParamsStr) {
          const payParams = JSON.parse(payParamsStr);
        if (payParams.status === 'success') {
          // 余额支付成功，直接处理成功逻辑
          handlePaymentSuccess({
            paymentAmount: totalPaymentAmount.value,
            paymentMethod: selectedPaymentMethod.value,
            transactionNo: paymentResult.transactionNo
          });
          return;
        }
        }
      } catch (e) {
        console.warn('解析支付参数失败:', e);
      }
    }
    
    // 显示支付界面
    showPaymentInterface.value = true;
    
    // 开始倒计时和轮询
    startCountdown();
    startPaymentPolling();
    
  } catch (error) {
    console.error('创建支付失败:', error);
    // 发生API调用异常时，也调用handlePaymentFailed来显示错误弹窗
    handlePaymentFailed(error);
  } finally {
    loading.value = false;
  }
}

/**
 * 开始倒计时
 */
function startCountdown() {
  if (!paymentInfo.value?.expiresAt) return;
  
  const expiresTime = new Date(paymentInfo.value.expiresAt).getTime();
  
  countdownTimer.value = setInterval(() => {
    const now = Date.now();
    const remaining = Math.max(0, expiresTime - now);
    countdown.value = Math.floor(remaining / 1000);
    
    if (remaining <= 0) {
      handlePaymentTimeout();
    }
  }, 1000);
}

/**
 * 开始轮询支付状态
 */
function startPaymentPolling() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }
  
  pollingTimer.value = setInterval(async () => {
    await checkPaymentStatus();
  }, 2000); // 每2秒查询一次
}

/**
 * 检查支付状态
 */
async function checkPaymentStatus() {
  if (!paymentInfo.value || !primaryOrder.value) return;
  
  try {
    let statusResult;
    
    if (primaryOrder.value.orderType === 'takeout') {
      statusResult = await getTakeoutPaymentStatus(paymentInfo.value.paymentId);
    } else {
      statusResult = await getOrderPaymentStatus(primaryOrder.value.id);
    }
    
    const status = statusResult.status;
    paymentInfo.value.status = status;
    
    if (status === PaymentStatus.PAID) {
      handlePaymentSuccess(statusResult);
    } else if (status === PaymentStatus.FAILED) {
      handlePaymentFailed(statusResult);
    }
    
  } catch (error) {
    console.error('查询支付状态失败:', error);
  }
}

/**
 * 处理支付成功
 */
function handlePaymentSuccess(paymentData: any) {
  clearTimers();
  visible.value = false;
  
  let successMessage = '支付成功！\n';
  
  if (orderInfoList.value.length === 1) {
    // 单订单支付成功
    successMessage += `订单号：${orderInfoList.value[0].orderNumber}\n`;
  } else {
    // 多订单支付成功
    successMessage += `共支付 ${orderInfoList.value.length} 个订单\n`;
    orderInfoList.value.forEach((order, index) => {
      successMessage += `订单${index + 1}：${order.orderNumber}\n`;
    });
  }
  
  successMessage += `支付金额：¥${paymentData.paymentAmount?.toFixed(2)}\n`;
  successMessage += `支付方式：${getPaymentMethodText(paymentData.paymentMethod)}\n`;
  successMessage += `交易流水：${paymentData.transactionNo}`;
  
  ElMessageBox.alert(
    successMessage,
    '支付成功',
    {
      confirmButtonText: '确定',
      type: 'success'
    }
  );
  
  emit('payment-success', paymentData);
}

/**
 * 处理支付失败
 */
function handlePaymentFailed(paymentData: any) {
  clearTimers();
  
  // 提取错误信息
  let errorMessage = '未知错误';
  if (typeof paymentData === 'string') {
    errorMessage = paymentData;
  } else if (paymentData) {
    errorMessage = paymentData.error_message || paymentData.errorMessage || paymentData.message || 
                  (paymentData.data?.message) || (paymentData.response?.data?.message) || '未知错误';
  }
  
  ElMessageBox.confirm(
    `支付失败\n失败原因：${errorMessage}\n是否重新支付？`,
    '支付失败',
    {
      confirmButtonText: '重新支付',
      cancelButtonText: '取消',
      type: 'error'
    }
  ).then(() => {
    // 重新支付
    showPaymentInterface.value = false;
    handleCreatePayment();
  }).catch(() => {
    // 取消支付
    visible.value = false;
    emit('payment-failed', paymentData);
  });
}

/**
 * 处理支付超时
 */
function handlePaymentTimeout() {
  clearTimers();
  
  let timeoutMessage = '支付超时\n';
  
  if (orderInfoList.value.length === 1) {
    // 单订单支付超时
    timeoutMessage += `订单号：${orderInfoList.value[0].orderNumber}\n`;
  } else {
    // 多订单支付超时
    timeoutMessage += `共 ${orderInfoList.value.length} 个订单\n`;
    orderInfoList.value.forEach((order, index) => {
      timeoutMessage += `订单${index + 1}：${order.orderNumber}\n`;
    });
  }
  
  timeoutMessage += `支付金额：¥${paymentInfo.value?.paymentAmount?.toFixed(2)}\n\n是否重新生成支付二维码？`;
  
  ElMessageBox.confirm(
    timeoutMessage,
    '支付超时',
    {
      confirmButtonText: '重新生成',
      cancelButtonText: '取消支付',
      type: 'warning'
    }
  ).then(() => {
    handleRefreshQR();
  }).catch(() => {
    handleClose();
  });
}

/**
 * 刷新二维码
 */
function handleRefreshQR() {
  if (props.orderInfo) {
    handleCreatePayment();
  }
}

/**
 * 返回支付方式选择
 */
function handleBackToMethodSelect() {
  clearTimers();
  showPaymentInterface.value = false;
  paymentInfo.value = null;
}

/**
 * 关闭弹窗
 */
function handleClose() {
  clearTimers();
  visible.value = false;
}

/**
 * 格式化日期
 */
function formatDate(dateString: string): string {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 格式化倒计时
 */
function formatCountdown(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 组件卸载时清除定时器
onUnmounted(() => {
  clearTimers();
});
</script>

<style scoped>
.payment-order-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.payment-order-info h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-row .label {
  color: #666;
  font-size: 14px;
}

.order-row .value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.order-row .value.amount {
  color: #e74c3c;
  font-size: 16px;
  font-weight: 600;
}

.payment-methods {
  margin-bottom: 24px;
}

.payment-methods h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.payment-method-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-method-item {
  margin: 0;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.payment-method-item:hover {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.payment-method-item.is-checked {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.method-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.method-icon {
  font-size: 20px;
  color: #409eff;
}

.method-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.payment-interface {
  text-align: center;
}

.payment-header {
  margin-bottom: 24px;
}

.payment-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.payment-amount {
  font-size: 16px;
  color: #e74c3c;
  font-weight: 600;
}

.qr-payment {
  margin-bottom: 24px;
}

.qr-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.qr-code {
  width: 200px;
  height: 200px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.qr-code img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #999;
}

.qr-placeholder p {
  margin: 0;
  font-size: 14px;
}

.payment-tips {
  color: #666;
  font-size: 14px;
}

.payment-tips p {
  margin: 0 0 8px 0;
}

.countdown {
  color: #e74c3c;
  font-weight: 600;
  font-size: 16px;
}

.payment-notes {
  text-align: left;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 16px;
}

.payment-notes ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
  font-size: 14px;
}

.payment-notes li {
  margin-bottom: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>