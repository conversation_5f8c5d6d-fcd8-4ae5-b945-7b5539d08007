/**
 * 聊天模块类型定义统一导出
 */

export * from './message'
export * from './session'
export * from './chat'
export * from './websocket'
export * from './notification'

// 添加简化的通用类型
export interface User {
  id: number
  name: string
  email?: string
  avatar?: string
  role?: string
  status?: 'online' | 'offline' | 'away' | 'busy'
  created_at: string
  updated_at: string
}

export interface FileUpload {
  id: string
  name: string
  size: number
  type: string
  status: 'pending' | 'uploading' | 'completed' | 'failed' | 'cancelled'
  progress: number
  url?: string
  error?: string
  created_at: string
}

export interface SessionStats {
  total_messages: number
  total_participants: number
  average_response_time: number
  satisfaction_score?: number
  created_at: string
  updated_at: string
}