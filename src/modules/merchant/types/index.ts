/**
 * 商家模块类型定义
 */

// Token信息接口
export interface TokenInfo {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

// 商家审核状态枚举
export enum MerchantAuditStatus {
  PENDING = 0,    // 待审核
  APPROVED = 1,   // 已审核通过
  REJECTED = 2,   // 已拒绝
  SUSPENDED = 3,  // 已禁用
  LOCKED = 4      // 已锁定
}

// 商家活动状态枚举
export enum MerchantActiveStatus {
  DISABLED = 0,   // 禁用
  NORMAL = 1,     // 正常
  LOCKED = 2      // 锁定
}

// 兼容旧版的商家入驻状态枚举（将逐步淘汰）
export enum MerchantStatus {
  PENDING = 0,    // 待审核
  APPROVED = 1,   // 已审核通过
  REJECTED = 2,   // 已拒绝
  SUSPENDED = 3   // 已禁用/暂停
}

// 商家入驻申请类型
export interface MerchantApplication {
  id?: number;
  name: string;                // 商家名称
  logo?: string;               // 商家Logo
  description: string;         // 商家描述
  username: string;            // 登录用户名
  password: string;            // 登录密码
  contact_name: string;        // 联系人姓名
  contact_mobile: string;      // 联系人手机号
  contact_email: string;       // 联系人电子邮箱
  business_license: string;    // 营业执照URL
  address: string;             // 商家地址
  category_id?: number;         // 商家分类ID
  
  // 前端存储字段，不会提交给后端
  businessLicense?: string;    // 兼容旧字段名
  idCardFront?: string;        // 身份证正面图片URL
  idCardBack?: string;         // 身份证背面图片URL
  contactName?: string;        // 兼容旧字段名
  contactPhone?: string;       // 兼容旧字段名
  contactEmail?: string;       // 兼容旧字段名
  bankName?: string;           // 银行名称
  bankAccount?: string;        // 银行账号
  bankAccountName?: string;    // 开户名
  businessScope?: string;      // 经营范围
  
  // 后端返回字段
  applyTime?: Date;            // 申请时间
  status?: MerchantStatus;     // 申请状态（旧版，兼容用）
  audit_status?: MerchantAuditStatus; // 审核状态
  activeStatus?: MerchantActiveStatus; // 活动状态
  rejectReason?: string;       // 拒绝原因
}

// 经营时段项目类型
export interface BusinessHoursItem {
  weekday: number;       // 星期几：0-周日，1-周一, ..., 6-周六
  startTime: string;     // 开始时间：格式如 "09:00"
  endTime: string;       // 结束时间：格式如 "22:00"
}

// 完整商家信息类型
export interface Merchant extends Omit<MerchantApplication, 'status' | 'audit_status' | 'activeStatus'> {
  userId: number;          // 关联的用户ID
  createTime: Date;        // 创建时间
  updateTime: Date;        // 更新时间
  score: number;           // 店铺评分
  serviceScore: number;    // 服务评分
  deliveryScore: number;   // 配送评分
  productScore: number;    // 商品评分
  totalOrders: number;     // 总订单数
  totalSales: number;      // 总销售额
  logo: string;            // 店铺logo
  banner: string;          // 店铺banner
  openingHours: string;    // 营业时间
  isOpen: boolean;         // 是否营业中
  operationStatus?: number; // 经营状态：1-营业中，0-暂停营业
  businessHours?: BusinessHoursItem[]; // 经营时段
  status: MerchantActiveStatus;   // 商家状态：0-禁用,1-正常,2-锁定
  audit_status: MerchantAuditStatus; // 商家审核状态：0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定
  longitude?: number;       // 经度
  latitude?: number;        // 纬度
}

// 商家登录参数
export interface MerchantLoginParams {
  username: string;
  password: string;
  device_info?: {
    device_type: string;    // 设备类型：web/mobile/app
    device_id: string;      // 设备唯一标识
    device_name: string;    // 设备名称
    os: string;             // 操作系统
    browser: string;        // 浏览器信息
    ip?: string;            // IP地址
    user_agent?: string;    // User Agent
  };
}

// 短信验证码登录参数
export interface SmsLoginParams {
  phone: string;
  code: string;
  device_info?: {
    device_type: string;    // 设备类型：web/mobile/app
    device_id: string;      // 设备唯一标识
    device_name: string;    // 设备名称
    os: string;             // 操作系统
    browser: string;        // 浏览器信息
    ip?: string;            // IP地址
    user_agent?: string;    // User Agent
  };
}

// 商家统计数据
export interface MerchantStatistics {
  todaySales: number;      // 今日销售额
  todayOrders: number;     // 今日订单数
  weekSales: number;       // 本周销售额
  weekOrders: number;      // 本周订单数
  monthSales: number;      // 本月销售额
  monthOrders: number;     // 本月订单数
  totalSales: number;      // 总销售额
  totalOrders: number;     // 总订单数
  pendingOrders: number;   // 待处理订单数
  pendingShipment: number; // 待发货订单数
  pendingRefund: number;   // 待退款订单数
  lowStockProducts: number;// 库存不足商品数
}

// 商家结算记录
export interface SettlementRecord {
  id: number;
  merchantId: number;     // 商家ID
  amount: number;         // 结算金额
  period: string;         // 结算周期，如"2023-05"
  status: string;         // 结算状态：处理中、已完成、已拒绝
  createTime: Date;       // 创建时间
  completeTime?: Date;    // 完成时间
  remark?: string;        // 备注
}

// 商家商品类型
export interface MerchantProduct {
  id: number;
  merchantId: number;     // 商家ID
  name: string;           // 商品名称
  categoryId: number;     // 分类ID
  price: number;          // 价格
  originalPrice: number;  // 原价
  stock: number;          // 库存
  sales: number;          // 销量
  mainImage: string;      // 主图
  description: string;    // 描述
  images: string[];       // 图片列表
  status: string;         // 状态：上架、下架、审核中
  createTime: Date;       // 创建时间
  updateTime: Date;       // 更新时间
  isRecommend: boolean;   // 是否推荐
  isNew: boolean;         // 是否新品
  isHot: boolean;         // 是否热销
  attributes: ProductAttribute[]; // 商品属性
  specs: ProductSpec[];   // 商品规格
}

// 商品属性
export interface ProductAttribute {
  id?: number;
  name: string;           // 属性名
  value: string;          // 属性值
}

// 商品规格
export interface ProductSpec {
  id?: number;
  name: string;           // 规格名，如"颜色"、"尺寸"
  options: SpecOption[];  // 规格选项
}

// 规格选项
export interface SpecOption {
  id?: number;
  value: string;          // 选项值，如"红色"、"XL"
  image?: string;         // 选项图片
}

// 商品SKU
export interface ProductSKU {
  id?: number;
  productId: number;      // 商品ID
  specs: {                // 规格组合，如 {"颜色": "红色", "尺寸": "XL"}
    [key: string]: string;
  };
  price: number;          // 该规格价格
  originalPrice: number;  // 该规格原价
  stock: number;          // 该规格库存
  sku: string;            // SKU编码
}

// 商家订单类型
export interface MerchantOrder {
  id: number;
  orderNo: string;        // 订单编号
  userId: number;         // 用户ID
  userName: string;       // 用户名
  merchantId: number;     // 商家ID
  status: string;         // 订单状态
  totalAmount: number;    // 订单总金额
  payAmount: number;      // 实付金额
  createTime: Date;       // 创建时间
  payTime?: Date;         // 支付时间
  shipTime?: Date;        // 发货时间
  receiveTime?: Date;     // 收货时间
  cancelTime?: Date;      // 取消时间
  closeTime?: Date;       // 关闭时间
  refundStatus?: string;  // 退款状态
  refundAmount?: number;  // 退款金额
  refundTime?: Date;      // 退款时间
  remark?: string;        // 订单备注
  address: {              // 收货地址
    name: string;
    phone: string;
    province: string;
    city: string;
    district: string;
    address: string;
    postalCode?: string;
  };
  items: MerchantOrderItem[]; // 订单项
  logistics?: {           // 物流信息
    company: string;      // 物流公司
    trackingNo: string;   // 物流单号
    trackingUrl?: string; // 物流查询URL
  };
}

// 订单项
export interface MerchantOrderItem {
  id: number;
  orderId: number;        // 订单ID
  productId: number;      // 商品ID
  productName: string;    // 商品名称
  productImage: string;   // 商品图片
  skuId?: number;         // SKUID
  skuSpecs?: string;      // SKU规格描述
  price: number;          // 单价
  quantity: number;       // 数量
  subtotal: number;       // 小计
}

/**
 * 商户模块类型定义
 */

// 商户登录参数
export interface MerchantLoginParams {
  username: string;
  password: string;
}

// 商户信息
export interface MerchantInfo {
  id: number;
  username: string;
  name: string;
  logo?: string;
  description?: string;
  phone?: string;
  email?: string;
  address?: string;
  status: string;
  createTime: string;
  updateTime: string;
}

// 商品信息
export interface ProductInfo {
  id: number;
  merchantId: number;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  stock: number;
  sales: number;
  categoryId: number;
  images: string[];
  status: string;
  createTime: string;
  updateTime: string;
}

// 订单状态枚举
export enum OrderStatus {
  PENDING = 10,     // 待支付
  PAID = 20,        // 已支付
  PROCESSING = 30,  // 处理中
  DELIVERING = 40,  // 配送中
  COMPLETED = 50,   // 已完成
  CANCELLED = 60,   // 已取消
  REFUNDING = 70,   // 退款中
  REFUNDED = 80     // 已退款
}

/**
 * 订单状态文本映射 (中文)
 */
export const OrderStatusText: { [key in OrderStatus]?: string } = {
  [OrderStatus.PENDING]: '待支付',
  [OrderStatus.PAID]: '已支付',
  [OrderStatus.PROCESSING]: '处理中',
  [OrderStatus.DELIVERING]: '配送中',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.REFUNDING]: '退款中',
  [OrderStatus.REFUNDED]: '已退款',
};

// 外卖订单信息
export interface OrderInfo {
  id: number;
  order_number: string;        // 订单号
  user_id: number;             // 用户ID
  userID: number;              // 用户ID (驼峰命名)
  user_name: string;           // 用户名
  user_phone: string;          // 用户电话
  merchant_id: number;         // 商家ID
  merchantID: number;          // 商家ID (驼峰命名)
  merchant_name: string;       // 商家名称
  status: number;              // 订单状态
  status_text: string;         // 订单状态文本
  total_amount: number;        // 总金额
  items_count: number;         // 商品项数量
  delivery_fee: number;        // 配送费
  packaging_fee: number;       // 包装费
  discount_amount: number;     // 优惠金额
  order_items: Array<OrderItem>; // 订单商品项
  address: {                  // 配送地址
    id: number;
    receiver: string;          // 收货人
    phone: string;             // 联系电话
    province: string;          // 省份
    city: string;              // 城市
    district: string;          // 区/县
    detail: string;            // 详细地址
    is_default: boolean;       // 是否默认地址
  };
  remark: string;              // 订单备注
  delivery_info: DeliveryInfo; // 配送信息
  created_at: string;          // 创建时间
  paid_at: string;             // 支付时间
  accepted_at: string | null;  // 接单时间
  completed_at: string | null; // 完成时间
  cancelled_at: string | null; // 取消时间
  refunding_at: string | null; // 退款开始时间
  refunded_at: string | null;  // 退款完成时间
  updated_at: string;          // 更新时间
}

// 订单商品项
export interface OrderItem {
  id: number;
  food_id: number;           // 商品ID
  food_name: string;         // 商品名称
  food_image: string;        // 商品图片
  variant_name: string;      // 规格名称
  quantity: number;          // 数量
  price: number;             // 价格
  subtotal: number;          // 小计
  combo_selections: Array<{  // 套餐选择
    combo_name: string;      // 套餐名称
    options: Array<{         // 选项
      option_name: string;   // 选项名称
      quantity: number;      // 数量
      extra_price: number;   // 额外价格
    }>
  }>;
}

// 配送信息
/**
 * 配送状态枚举 - 已迁移到 order.ts 文件
 * @deprecated 请使用 @/modules/merchant/types/order 中的 DeliveryStatus
 */
// export enum DeliveryStatus {
//   PENDING_ASSIGNMENT = 10, // 待分配
//   PENDING_PICKUP = 20,     // 待取货
//   DELIVERING = 30,         // 配送中
//   DELIVERED = 40,          // 已送达
//   DELIVERY_EXCEPTION = 50, // 配送异常
// }

/**
 * 配送状态文本映射 (中文) - 已迁移到 order.ts 文件
 * @deprecated 请使用 @/modules/merchant/types/order 中的 DeliveryStatusText
 */
// export const DeliveryStatusText: { [key in DeliveryStatus]?: string } = {
//   [DeliveryStatus.PENDING_ASSIGNMENT]: '待分配',
//   [DeliveryStatus.PENDING_PICKUP]: '待取货',
//   [DeliveryStatus.DELIVERING]: '配送中',
//   [DeliveryStatus.DELIVERED]: '已送达',
//   [DeliveryStatus.DELIVERY_EXCEPTION]: '配送异常',
// };

// 配送信息
/**
 * @deprecated 请使用 @/modules/merchant/types/order 中的 DeliveryInfo
 */
export interface DeliveryInfo {
  delivery_staff_id: number;          // 配送员ID
  delivery_staff_name: string;        // 配送员姓名
  delivery_staff_phone: string;       // 配送员电话
  delivery_status: number;            // 配送状态（使用数字类型避免类型冲突）
  estimated_arrival_time: string | null; // 预计到达时间
  delivery_started_at: string | null;    // 开始配送时间
  delivery_completed_at: string | null;  // 配送完成时间
}

// 订单统计数据
export interface OrderStatistics {
  total_count: number;       // 总订单数
  completed_count: number;   // 已完成订单数
  processing_count: number;  // 处理中订单数
  cancelled_count: number;   // 已取消订单数
  today_orders: number;      // 今日订单数
  today_amount: number;      // 今日订单金额
  month_orders: number;      // 本月订单数
  month_amount: number;      // 本月订单金额
} 

/**
 * 外卖商品相关类型定义
 */

// 外卖商品状态枚举
export enum TakeoutFoodStatus {
  DRAFT = 0,          // 草稿状态
  ON_SALE = 1,        // 上架销售中
  OFF_SALE = 2,       // 已下架
  SOLD_OUT = 3        // 已售罄
}

// 外卖商品审核状态枚举
export enum TakeoutFoodAuditStatus {
  PENDING = 0,        // 未审核/待审核
  APPROVED = 1,       // 审核通过
  REJECTED = 2        // 审核拒绝
}

// 外卖商品基础信息
/**
 * 外卖商品基础信息
 */
export interface TakeoutFood {
  id: number;
  merchant_id: number;
  name: string;
  category_id: number;
  global_category_id?: number;
  description: string;
  brief?: string;              // 商品简介
  price: number;
  original_price?: number;     // 原价
  packaging_fee?: number;      // 包装费
  preparation_time?: number;   // 备餐时间(分钟)
  is_combination?: boolean;    // 是否为套餐组合
  is_spicy?: boolean;          // 是否辣味
  has_variants?: boolean;      // 是否有规格变体
  daily_limit?: number;        // 每日限量
  tags?: string[];             // 标签
  keywords?: string;           // 关键词，逗号分隔
  is_recommend?: boolean;      // 是否推荐
  sort_order: number;          // 排序值
  status: TakeoutFoodStatus;   // 状态
  audit_status?: TakeoutFoodAuditStatus; // 审核状态
  created_at: string;
  updated_at: string;
}

// 外卖商品规格组
export interface TakeoutFoodSpec {
  id: number;
  name: string;
  options: TakeoutFoodSpecOption[];
}

// 外卖商品规格选项
export interface TakeoutFoodSpecOption {
  id: number;
  name: string;
  price_adjustment: number;
  is_default: boolean;
}

// 外卖商品图片信息
export interface TakeoutFoodImages {
  food_id: number;
  main_image: string;
  detail_images: string[];
}

// 外卖商品分类
export interface TakeoutFoodCategory {
  id: number;
  name: string;
  image?: string;
  sort_order: number;
  children?: TakeoutFoodCategory[];
}

// 分类信息
export interface CategoryItem {
  id?: number;
  name: string;
  code: string;
  parent_id: number | null;
  description?: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  level?: number;
  children?: CategoryItem[];
}