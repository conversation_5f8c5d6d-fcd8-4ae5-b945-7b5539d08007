/**
 * 文件 Store 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useFileStore } from '../../../stores/file'
import type { FileUpload, FileUploadStatus } from '../../../types'

// Mock API
vi.mock('../../../api', () => ({
  fileApi: {
    uploadFile: vi.fn().mockImplementation((file: File, onProgress?: (progress: number) => void) => {
      // 模拟上传进度
      if (onProgress) {
        setTimeout(() => onProgress(50), 100)
        setTimeout(() => onProgress(100), 200)
      }
      return Promise.resolve({
        id: 'file1',
        name: file.name,
        size: file.size,
        type: file.type,
        url: 'https://example.com/file1.txt'
      })
    }),
    deleteFile: vi.fn().mockResolvedValue(undefined),
    getFileInfo: vi.fn().mockResolvedValue({
      id: 'file1',
      name: 'test.txt',
      size: 1024,
      type: 'text/plain',
      url: 'https://example.com/file1.txt'
    })
  }
}))

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn().mockReturnValue('blob:mock-url')
global.URL.revokeObjectURL = vi.fn()

describe('File Store', () => {
  let store: ReturnType<typeof useFileStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useFileStore()
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.uploadQueue).toEqual([])
      expect(store.uploadHistory).toEqual([])
      expect(store.isUploading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.settings.maxFileSize).toBe(10 * 1024 * 1024) // 10MB
      expect(store.settings.allowedTypes).toEqual([
        'image/*',
        'video/*',
        'audio/*',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/*'
      ])
      expect(store.settings.maxConcurrentUploads).toBe(3)
    })
  })

  describe('文件验证', () => {
    it('应该验证文件大小', () => {
      const smallFile = new File(['test'], 'small.txt', { type: 'text/plain' })
      const largeFile = new File([new ArrayBuffer(20 * 1024 * 1024)], 'large.txt', { type: 'text/plain' })
      
      expect(store.validateFile(smallFile)).toBe(true)
      expect(store.validateFile(largeFile)).toBe(false)
    })

    it('应该验证文件类型', () => {
      const textFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      const execFile = new File(['test'], 'test.exe', { type: 'application/x-msdownload' })
      
      expect(store.validateFile(textFile)).toBe(true)
      expect(store.validateFile(execFile)).toBe(false)
    })

    it('应该验证图片文件', () => {
      const imageFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      expect(store.validateFile(imageFile)).toBe(true)
      expect(store.isImageFile(imageFile)).toBe(true)
    })

    it('应该验证视频文件', () => {
      const videoFile = new File(['test'], 'test.mp4', { type: 'video/mp4' })
      
      expect(store.validateFile(videoFile)).toBe(true)
      expect(store.isVideoFile(videoFile)).toBe(true)
    })

    it('应该验证音频文件', () => {
      const audioFile = new File(['test'], 'test.mp3', { type: 'audio/mpeg' })
      
      expect(store.validateFile(audioFile)).toBe(true)
      expect(store.isAudioFile(audioFile)).toBe(true)
    })
  })

  describe('文件上传', () => {
    const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' })

    it('应该能够添加文件到上传队列', () => {
      store.addToUploadQueue(mockFile)
      
      expect(store.uploadQueue).toHaveLength(1)
      expect(store.uploadQueue[0].file).toBe(mockFile)
      expect(store.uploadQueue[0].status).toBe('pending')
    })

    it('应该能够批量添加文件到上传队列', () => {
      const files = [
        new File(['test1'], 'test1.txt', { type: 'text/plain' }),
        new File(['test2'], 'test2.txt', { type: 'text/plain' })
      ]
      
      store.addFilesToUploadQueue(files)
      
      expect(store.uploadQueue).toHaveLength(2)
    })

    it('应该能够开始上传文件', async () => {
      store.addToUploadQueue(mockFile)
      const uploadId = store.uploadQueue[0].id
      
      await store.startUpload(uploadId)
      
      const upload = store.uploadQueue.find(u => u.id === uploadId)
      expect(upload?.status).toBe('completed')
      expect(upload?.result).toBeDefined()
    })

    it('应该能够暂停上传', () => {
      store.addToUploadQueue(mockFile)
      const uploadId = store.uploadQueue[0].id
      
      store.pauseUpload(uploadId)
      
      const upload = store.uploadQueue.find(u => u.id === uploadId)
      expect(upload?.status).toBe('paused')
    })

    it('应该能够恢复上传', async () => {
      store.addToUploadQueue(mockFile)
      const uploadId = store.uploadQueue[0].id
      
      store.pauseUpload(uploadId)
      await store.resumeUpload(uploadId)
      
      const upload = store.uploadQueue.find(u => u.id === uploadId)
      expect(upload?.status).toBe('completed')
    })

    it('应该能够取消上传', () => {
      store.addToUploadQueue(mockFile)
      const uploadId = store.uploadQueue[0].id
      
      store.cancelUpload(uploadId)
      
      const upload = store.uploadQueue.find(u => u.id === uploadId)
      expect(upload?.status).toBe('cancelled')
    })

    it('应该能够重试失败的上传', async () => {
      store.addToUploadQueue(mockFile)
      const uploadId = store.uploadQueue[0].id
      
      // 模拟失败
      store.updateUploadStatus(uploadId, 'failed', 'Upload failed')
      
      await store.retryUpload(uploadId)
      
      const upload = store.uploadQueue.find(u => u.id === uploadId)
      expect(upload?.status).toBe('completed')
    })

    it('应该能够从队列中移除上传', () => {
      store.addToUploadQueue(mockFile)
      const uploadId = store.uploadQueue[0].id
      
      store.removeFromQueue(uploadId)
      
      expect(store.uploadQueue.find(u => u.id === uploadId)).toBeUndefined()
    })
  })

  describe('批量操作', () => {
    beforeEach(() => {
      const files = [
        new File(['test1'], 'test1.txt', { type: 'text/plain' }),
        new File(['test2'], 'test2.txt', { type: 'text/plain' }),
        new File(['test3'], 'test3.txt', { type: 'text/plain' })
      ]
      store.addFilesToUploadQueue(files)
    })

    it('应该能够开始所有上传', async () => {
      await store.startAllUploads()
      
      expect(store.uploadQueue.every(u => u.status === 'completed')).toBe(true)
    })

    it('应该能够暂停所有上传', () => {
      store.pauseAllUploads()
      
      expect(store.uploadQueue.every(u => u.status === 'paused')).toBe(true)
    })

    it('应该能够恢复所有上传', async () => {
      store.pauseAllUploads()
      await store.resumeAllUploads()
      
      expect(store.uploadQueue.every(u => u.status === 'completed')).toBe(true)
    })

    it('应该能够取消所有上传', () => {
      store.cancelAllUploads()
      
      expect(store.uploadQueue.every(u => u.status === 'cancelled')).toBe(true)
    })

    it('应该能够清空上传队列', () => {
      store.clearUploadQueue()
      
      expect(store.uploadQueue).toEqual([])
    })

    it('应该能够移除已完成的上传', async () => {
      await store.startAllUploads()
      store.removeCompletedUploads()
      
      expect(store.uploadQueue).toEqual([])
    })
  })

  describe('文件预览', () => {
    it('应该能够生成图片预览URL', () => {
      const imageFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const previewUrl = store.getFilePreviewUrl(imageFile)
      
      expect(previewUrl).toBe('blob:mock-url')
    })

    it('应该能够释放预览URL', () => {
      const imageFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const previewUrl = store.getFilePreviewUrl(imageFile)
      
      store.revokeFilePreviewUrl(previewUrl)
      
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith(previewUrl)
    })

    it('应该能够获取文件图标', () => {
      const textFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      const imageFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const pdfFile = new File(['test'], 'test.pdf', { type: 'application/pdf' })
      
      expect(store.getFileIcon(textFile)).toBe('📄')
      expect(store.getFileIcon(imageFile)).toBe('🖼️')
      expect(store.getFileIcon(pdfFile)).toBe('📕')
    })
  })

  describe('文件格式化', () => {
    it('应该能够格式化文件大小', () => {
      expect(store.formatFileSize(0)).toBe('0 B')
      expect(store.formatFileSize(1024)).toBe('1.0 KB')
      expect(store.formatFileSize(1024 * 1024)).toBe('1.0 MB')
      expect(store.formatFileSize(1024 * 1024 * 1024)).toBe('1.0 GB')
    })

    it('应该能够格式化上传速度', () => {
      expect(store.formatUploadSpeed(0)).toBe('0 B/s')
      expect(store.formatUploadSpeed(1024)).toBe('1.0 KB/s')
      expect(store.formatUploadSpeed(1024 * 1024)).toBe('1.0 MB/s')
    })

    it('应该能够计算剩余时间', () => {
      expect(store.calculateRemainingTime(1024, 1024)).toBe('1s')
      expect(store.calculateRemainingTime(1024 * 60, 1024)).toBe('1m')
      expect(store.calculateRemainingTime(1024 * 3600, 1024)).toBe('1h')
    })
  })

  describe('Getters', () => {
    beforeEach(() => {
      const files = [
        new File(['test1'], 'test1.txt', { type: 'text/plain' }),
        new File(['test2'], 'test2.txt', { type: 'text/plain' }),
        new File(['test3'], 'test3.txt', { type: 'text/plain' })
      ]
      store.addFilesToUploadQueue(files)
      
      // 设置不同状态
      store.updateUploadStatus(store.uploadQueue[0].id, 'uploading')
      store.updateUploadStatus(store.uploadQueue[1].id, 'completed')
      store.updateUploadStatus(store.uploadQueue[2].id, 'failed')
    })

    it('应该正确获取待上传文件', () => {
      const pending = store.pendingUploads
      expect(pending).toHaveLength(0) // 没有pending状态的
    })

    it('应该正确获取正在上传文件', () => {
      const uploading = store.uploadingFiles
      expect(uploading).toHaveLength(1)
      expect(uploading[0].status).toBe('uploading')
    })

    it('应该正确获取已完成上传', () => {
      const completed = store.completedUploads
      expect(completed).toHaveLength(1)
      expect(completed[0].status).toBe('completed')
    })

    it('应该正确获取失败上传', () => {
      const failed = store.failedUploads
      expect(failed).toHaveLength(1)
      expect(failed[0].status).toBe('failed')
    })

    it('应该正确计算总上传进度', () => {
      // 设置进度
      store.updateUploadProgress(store.uploadQueue[0].id, 50)
      store.updateUploadProgress(store.uploadQueue[1].id, 100)
      store.updateUploadProgress(store.uploadQueue[2].id, 0)
      
      const totalProgress = store.totalUploadProgress
      expect(totalProgress).toBe(50) // (50 + 100 + 0) / 3
    })

    it('应该正确检查是否有活跃上传', () => {
      expect(store.hasActiveUploads).toBe(true) // 有uploading状态的
    })

    it('应该正确检查是否有失败上传', () => {
      expect(store.hasFailedUploads).toBe(true)
    })

    it('应该正确获取上传统计', () => {
      const stats = store.uploadStats
      
      expect(stats.total).toBe(3)
      expect(stats.pending).toBe(0)
      expect(stats.uploading).toBe(1)
      expect(stats.completed).toBe(1)
      expect(stats.failed).toBe(1)
      expect(stats.cancelled).toBe(0)
    })
  })

  describe('设置管理', () => {
    it('应该能够更新最大文件大小', () => {
      store.updateSettings({ maxFileSize: 20 * 1024 * 1024 })
      
      expect(store.settings.maxFileSize).toBe(20 * 1024 * 1024)
    })

    it('应该能够更新允许的文件类型', () => {
      const newTypes = ['image/*', 'text/*']
      store.updateSettings({ allowedTypes: newTypes })
      
      expect(store.settings.allowedTypes).toEqual(newTypes)
    })

    it('应该能够更新最大并发上传数', () => {
      store.updateSettings({ maxConcurrentUploads: 5 })
      
      expect(store.settings.maxConcurrentUploads).toBe(5)
    })

    it('应该能够重置设置', () => {
      store.updateSettings({ maxFileSize: 20 * 1024 * 1024 })
      store.resetSettings()
      
      expect(store.settings.maxFileSize).toBe(10 * 1024 * 1024)
    })
  })

  describe('错误处理', () => {
    it('应该能够设置错误', () => {
      const error = new Error('Upload failed')
      store.setError(error)
      
      expect(store.error).toBe(error)
    })

    it('应该能够清除错误', () => {
      const error = new Error('Upload failed')
      store.setError(error)
      store.clearError()
      
      expect(store.error).toBeNull()
    })

    it('上传失败时应该设置错误状态', async () => {
      // Mock API 返回错误
      const { fileApi } = await import('../../../api')
      vi.mocked(fileApi.uploadFile).mockRejectedValueOnce(new Error('Network error'))
      
      const file = new File(['test'], 'test.txt', { type: 'text/plain' })
      store.addToUploadQueue(file)
      const uploadId = store.uploadQueue[0].id
      
      await store.startUpload(uploadId)
      
      const upload = store.uploadQueue.find(u => u.id === uploadId)
      expect(upload?.status).toBe('failed')
      expect(upload?.error).toBe('Network error')
    })
  })

  describe('状态重置', () => {
    it('应该能够重置状态', () => {
      // 设置一些状态
      const file = new File(['test'], 'test.txt', { type: 'text/plain' })
      store.addToUploadQueue(file)
      store.setError(new Error('Test error'))
      
      // 重置状态
      store.resetState()
      
      // 验证状态已重置
      expect(store.uploadQueue).toEqual([])
      expect(store.uploadHistory).toEqual([])
      expect(store.error).toBeNull()
      expect(store.isUploading).toBe(false)
    })
  })

  describe('并发控制', () => {
    it('应该限制并发上传数量', async () => {
      // 设置最大并发数为2
      store.updateSettings({ maxConcurrentUploads: 2 })
      
      // 添加5个文件
      const files = Array.from({ length: 5 }, (_, i) => 
        new File([`test${i}`], `test${i}.txt`, { type: 'text/plain' })
      )
      store.addFilesToUploadQueue(files)
      
      // 开始所有上传
      await store.startAllUploads()
      
      // 验证所有文件都已完成（虽然有并发限制）
      expect(store.uploadQueue.every(u => u.status === 'completed')).toBe(true)
    })
  })
})