/**
 * 消息 Store 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useMessageStore } from '../../../stores/message'
import type { Message, MessageType, MessageStatus } from '../../../types'

// Mock API
vi.mock('../../../api', () => ({
  messageApi: {
    getMessages: vi.fn().mockResolvedValue({
      data: [],
      total: 0,
      page: 1,
      size: 20
    }),
    sendMessage: vi.fn().mockResolvedValue({
      id: 'msg1',
      content: 'test message',
      type: 'text',
      status: 'sent',
      senderId: 'user1',
      sessionId: 'session1',
      createdAt: new Date()
    }),
    updateMessage: vi.fn().mockResolvedValue(undefined),
    deleteMessage: vi.fn().mockResolvedValue(undefined),
    markAsRead: vi.fn().mockResolvedValue(undefined),
    searchMessages: vi.fn().mockResolvedValue({
      data: [],
      total: 0
    })
  }
}))

// Mock ChatClient
vi.mock('../../../services/client', () => ({
  ChatClient: {
    getInstance: vi.fn().mockReturnValue({
      sendMessage: vi.fn().mockResolvedValue({ id: 'msg1' }),
      on: vi.fn(),
      off: vi.fn()
    })
  }
}))

describe('Message Store', () => {
  let store: ReturnType<typeof useMessageStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useMessageStore()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.messages).toEqual([])
      expect(store.messageCache).toEqual(new Map())
      expect(store.currentSessionId).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.hasMore).toBe(true)
      expect(store.searchResults).toEqual([])
      expect(store.selectedMessages).toEqual([])
      expect(store.replyingTo).toBeNull()
      expect(store.editingMessage).toBeNull()
    })
  })

  describe('消息管理', () => {
    const mockMessage: Message = {
      id: 'msg1',
      content: 'Test message',
      type: 'text' as MessageType,
      status: 'sent' as MessageStatus,
      senderId: 'user1',
      sessionId: 'session1',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    it('应该能够添加消息', () => {
      store.addMessage(mockMessage)
      expect(store.messages).toContain(mockMessage)
      expect(store.messageCache.get('msg1')).toBe(mockMessage)
    })

    it('应该能够更新消息', () => {
      store.addMessage(mockMessage)
      const updatedMessage = { ...mockMessage, content: 'Updated message' }
      store.updateMessage('msg1', updatedMessage)
      
      const message = store.messages.find(m => m.id === 'msg1')
      expect(message?.content).toBe('Updated message')
    })

    it('应该能够删除消息', () => {
      store.addMessage(mockMessage)
      store.removeMessage('msg1')
      
      expect(store.messages.find(m => m.id === 'msg1')).toBeUndefined()
      expect(store.messageCache.has('msg1')).toBe(false)
    })

    it('应该能够批量添加消息', () => {
      const messages = [
        { ...mockMessage, id: 'msg1' },
        { ...mockMessage, id: 'msg2' },
        { ...mockMessage, id: 'msg3' }
      ]
      
      store.addMessages(messages)
      expect(store.messages).toHaveLength(3)
      expect(store.messageCache.size).toBe(3)
    })

    it('应该能够清空消息', () => {
      store.addMessage(mockMessage)
      store.clearMessages()
      
      expect(store.messages).toEqual([])
      expect(store.messageCache.size).toBe(0)
    })
  })

  describe('消息发送', () => {
    it('应该能够发送文本消息', async () => {
      const result = await store.sendMessage('Hello', 'session1')
      
      expect(result).toBeDefined()
      expect(result.content).toBe('Hello')
      expect(result.sessionId).toBe('session1')
    })

    it('应该能够发送带文件的消息', async () => {
      const files = [new File(['test'], 'test.txt', { type: 'text/plain' })]
      const result = await store.sendMessage('Message with file', 'session1', { files })
      
      expect(result).toBeDefined()
      expect(result.content).toBe('Message with file')
    })

    it('应该能够回复消息', async () => {
      const originalMessage: Message = {
        id: 'original',
        content: 'Original message',
        type: 'text',
        status: 'sent',
        senderId: 'user2',
        sessionId: 'session1',
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      store.setReplyingTo(originalMessage)
      const result = await store.sendMessage('Reply message', 'session1')
      
      expect(result.replyTo).toBe('original')
      expect(store.replyingTo).toBeNull() // 应该清除回复状态
    })
  })

  describe('消息加载', () => {
    it('应该能够加载消息', async () => {
      await store.loadMessages('session1')
      
      expect(store.isLoading).toBe(false)
      expect(store.currentSessionId).toBe('session1')
    })

    it('应该能够加载更多消息', async () => {
      store.currentSessionId = 'session1'
      store.pagination.page = 1
      
      await store.loadMoreMessages()
      
      expect(store.pagination.page).toBe(2)
    })

    it('应该能够刷新消息', async () => {
      store.currentSessionId = 'session1'
      store.pagination.page = 2
      
      await store.refreshMessages()
      
      expect(store.pagination.page).toBe(1)
      expect(store.messages).toEqual([])
    })
  })

  describe('消息搜索', () => {
    it('应该能够搜索消息', async () => {
      await store.searchMessages('test query')
      
      expect(store.searchQuery).toBe('test query')
      expect(store.isSearching).toBe(false)
    })

    it('应该能够清除搜索结果', () => {
      store.searchQuery = 'test'
      store.searchResults = [{ id: 'msg1' } as Message]
      
      store.clearSearch()
      
      expect(store.searchQuery).toBe('')
      expect(store.searchResults).toEqual([])
    })
  })

  describe('消息选择', () => {
    const message1: Message = { id: 'msg1' } as Message
    const message2: Message = { id: 'msg2' } as Message

    beforeEach(() => {
      store.addMessage(message1)
      store.addMessage(message2)
    })

    it('应该能够选择消息', () => {
      store.selectMessage('msg1')
      expect(store.selectedMessages).toContain('msg1')
    })

    it('应该能够取消选择消息', () => {
      store.selectMessage('msg1')
      store.unselectMessage('msg1')
      expect(store.selectedMessages).not.toContain('msg1')
    })

    it('应该能够切换消息选择状态', () => {
      store.toggleMessageSelection('msg1')
      expect(store.selectedMessages).toContain('msg1')
      
      store.toggleMessageSelection('msg1')
      expect(store.selectedMessages).not.toContain('msg1')
    })

    it('应该能够全选消息', () => {
      store.selectAllMessages()
      expect(store.selectedMessages).toHaveLength(2)
      expect(store.selectedMessages).toContain('msg1')
      expect(store.selectedMessages).toContain('msg2')
    })

    it('应该能够清除所有选择', () => {
      store.selectMessage('msg1')
      store.selectMessage('msg2')
      store.clearSelection()
      
      expect(store.selectedMessages).toEqual([])
    })
  })

  describe('消息操作', () => {
    const mockMessage: Message = {
      id: 'msg1',
      content: 'Test message',
      type: 'text',
      status: 'sent',
      senderId: 'user1',
      sessionId: 'session1',
      createdAt: new Date(),
      updatedAt: new Date()
    }

    beforeEach(() => {
      store.addMessage(mockMessage)
    })

    it('应该能够编辑消息', async () => {
      await store.editMessage('msg1', 'Updated content')
      
      const message = store.messages.find(m => m.id === 'msg1')
      expect(message?.content).toBe('Updated content')
    })

    it('应该能够删除消息', async () => {
      await store.deleteMessage('msg1')
      
      expect(store.messages.find(m => m.id === 'msg1')).toBeUndefined()
    })

    it('应该能够标记消息为已读', async () => {
      await store.markAsRead(['msg1'])
      
      const message = store.messages.find(m => m.id === 'msg1')
      expect(message?.readAt).toBeDefined()
    })

    it('应该能够转发消息', async () => {
      const result = await store.forwardMessage('msg1', 'session2')
      
      expect(result).toBeDefined()
      expect(result.sessionId).toBe('session2')
      expect(result.forwardedFrom).toBe('msg1')
    })
  })

  describe('消息状态管理', () => {
    it('应该能够设置回复状态', () => {
      const message: Message = { id: 'msg1' } as Message
      store.setReplyingTo(message)
      
      expect(store.replyingTo).toBe(message)
    })

    it('应该能够清除回复状态', () => {
      const message: Message = { id: 'msg1' } as Message
      store.setReplyingTo(message)
      store.clearReplyingTo()
      
      expect(store.replyingTo).toBeNull()
    })

    it('应该能够设置编辑状态', () => {
      const message: Message = { id: 'msg1' } as Message
      store.setEditingMessage(message)
      
      expect(store.editingMessage).toBe(message)
    })

    it('应该能够清除编辑状态', () => {
      const message: Message = { id: 'msg1' } as Message
      store.setEditingMessage(message)
      store.clearEditingMessage()
      
      expect(store.editingMessage).toBeNull()
    })
  })

  describe('Getters', () => {
    const messages: Message[] = [
      {
        id: 'msg1',
        content: 'Message 1',
        type: 'text',
        status: 'sent',
        senderId: 'user1',
        sessionId: 'session1',
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-01')
      },
      {
        id: 'msg2',
        content: 'Message 2',
        type: 'image',
        status: 'delivered',
        senderId: 'user2',
        sessionId: 'session1',
        createdAt: new Date('2023-01-02'),
        updatedAt: new Date('2023-01-02')
      }
    ]

    beforeEach(() => {
      store.addMessages(messages)
    })

    it('应该正确获取当前会话的消息', () => {
      store.currentSessionId = 'session1'
      const sessionMessages = store.currentSessionMessages
      
      expect(sessionMessages).toHaveLength(2)
      expect(sessionMessages.every(m => m.sessionId === 'session1')).toBe(true)
    })

    it('应该正确按日期分组消息', () => {
      store.currentSessionId = 'session1'
      const grouped = store.messagesByDate
      
      expect(Object.keys(grouped)).toHaveLength(2)
      expect(grouped['2023-01-01']).toHaveLength(1)
      expect(grouped['2023-01-02']).toHaveLength(1)
    })

    it('应该正确按类型分组消息', () => {
      const grouped = store.messagesByType
      
      expect(grouped.text).toHaveLength(1)
      expect(grouped.image).toHaveLength(1)
    })

    it('应该正确获取未读消息', () => {
      const unreadMessages = store.unreadMessages
      
      expect(unreadMessages).toHaveLength(2) // 没有readAt的消息都是未读
    })

    it('应该正确获取选中的消息对象', () => {
      store.selectMessage('msg1')
      const selected = store.selectedMessageObjects
      
      expect(selected).toHaveLength(1)
      expect(selected[0].id).toBe('msg1')
    })

    it('应该正确检查是否有选中的消息', () => {
      expect(store.hasSelectedMessages).toBe(false)
      
      store.selectMessage('msg1')
      expect(store.hasSelectedMessages).toBe(true)
    })

    it('应该正确获取最新消息', () => {
      const latest = store.latestMessage
      
      expect(latest?.id).toBe('msg2') // 按创建时间排序，msg2更新
    })
  })

  describe('错误处理', () => {
    it('应该能够设置错误', () => {
      const error = new Error('Test error')
      store.setError(error)
      
      expect(store.error).toBe(error)
    })

    it('应该能够清除错误', () => {
      const error = new Error('Test error')
      store.setError(error)
      store.clearError()
      
      expect(store.error).toBeNull()
    })
  })

  describe('状态重置', () => {
    it('应该能够重置状态', () => {
      // 设置一些状态
      store.addMessage({ id: 'msg1' } as Message)
      store.currentSessionId = 'session1'
      store.selectMessage('msg1')
      store.setError(new Error('Test'))
      
      // 重置状态
      store.resetState()
      
      // 验证状态已重置
      expect(store.messages).toEqual([])
      expect(store.messageCache.size).toBe(0)
      expect(store.currentSessionId).toBeNull()
      expect(store.selectedMessages).toEqual([])
      expect(store.error).toBeNull()
      expect(store.replyingTo).toBeNull()
      expect(store.editingMessage).toBeNull()
    })
  })
})