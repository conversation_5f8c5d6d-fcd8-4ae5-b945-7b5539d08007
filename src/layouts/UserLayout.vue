<!--
  用户中心布局组件
  为用户模块提供统一的页面布局和导航结构
-->
<template>
  <div class="user-layout">
    <!-- 头部导航栏 -->
    <header class="header">
      <div class="header-container">
        <div class="logo-container">
          <router-link to="/">
            <img v-if="systemInfo?.siteLogo" :src="adjustLinkProtocol(systemInfo?.siteLogo)" alt="siteLogo" class="logo" />
          </router-link>
          <h1 class="site-title">用户中心</h1>
        </div>
        
        <div class="header-actions">
          <el-tooltip content="返回商城首页" placement="bottom">
            <el-button link @click="navigateTo('/')">
              <el-icon><House /></el-icon>
              首页
            </el-button>
          </el-tooltip>
          
          <el-dropdown trigger="click" @command="handleCommand">
            <div class="user-dropdown-link">
              <el-avatar :size="32" :src="userStore.avatar" class="user-avatar" />
              <span class="user-name">{{ userStore.nickname }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  账户设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>
    
    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="main-container">
        <!-- 侧边栏导航 -->
        <div class="sidebar">
          <el-menu
            router
            :default-active="activeMenu"
            class="menu"
          >
            <el-menu-item index="/user/home">
              <el-icon><House /></el-icon>
              <span>用户首页</span>
            </el-menu-item>
            
            <el-sub-menu index="/user/takeout">
              <template #title>
                <el-icon><Goods /></el-icon>
                <span>外卖服务</span>
              </template>
              <el-menu-item index="/user/takeout">
                <el-icon><Shop /></el-icon>
                <span>外卖商家</span>
              </el-menu-item>
              <el-menu-item index="/user/takeout/cart">
                <el-icon><ShoppingCart /></el-icon>
                <span>购物车</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item index="/user/orders">
              <el-icon><List /></el-icon>
              <span>我的订单</span>
            </el-menu-item>
            
            <el-menu-item index="/user/addresses">
              <el-icon><Location /></el-icon>
              <span>收货地址</span>
            </el-menu-item>
            
            <el-menu-item index="/user/coupons">
              <el-icon><Ticket /></el-icon>
              <span>优惠券中心</span>
            </el-menu-item>
            
            <el-menu-item index="/user/profile">
              <el-icon><User /></el-icon>
              <span>个人资料</span>
            </el-menu-item>
            
            <el-menu-item index="/user/settings">
              <el-icon><Setting /></el-icon>
              <span>账户设置</span>
            </el-menu-item>
            
            <el-menu-item index="/user/referral/invite">
              <el-icon><Share /></el-icon>
              <span>邀请好友</span>
            </el-menu-item>

            <!-- Runner Menu Start -->
            <el-sub-menu v-if="runnerMenuItems.length > 0" index="user-runner-submenu"> <!-- Unique index for the submenu -->
              <template #title>
                <el-icon><component :is="runnerMenus[0].icon || 'Menu'" /></el-icon>
                <span>{{ runnerMenus[0].title }}</span>
              </template>
              <el-menu-item
                v-for="item in runnerMenuItems"
                :key="item.path"
                :index="item.path"
              >
                <el-icon>
                  <component :is="item.icon || 'Document'" />
                </el-icon>
                <span>{{ item.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            <!-- Runner Menu End -->
            
            <!-- 动态菜单项 -->
            <!-- <template v-for="menuItem in dynamicMenuItems" :key="menuItem.path">
              <el-menu-item :index="menuItem.path">
                <el-icon>
                  <component :is="menuItem.icon || 'Document'" />
                </el-icon>
                <span>{{ menuItem.title }}</span>
              </el-menu-item>
            </template> -->
          </el-menu>
        </div>
        
        <!-- 内容区域 -->
        <div class="content">
          <!-- 页面头部 -->
          <div class="page-header">
            <h2 class="page-title">{{ pageTitle }}</h2>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/user/home' }">用户中心</el-breadcrumb-item>
              <el-breadcrumb-item>{{ pageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <!-- 路由视图 -->
          <div class="page-content">
            <router-view v-slot="{ Component }">
              <transition name="fade" mode="out-in">
                <keep-alive :include="cachedViews">
                  <component :is="Component" />
                </keep-alive>
              </transition>
            </router-view>
          </div>
        </div>
      </div>
    </main>
    
    <!-- 底部版权信息 -->
    <footer class="footer">
      <div class="footer-container">
        <p>&copy; 2025 O_Mall 购物中心. 保留所有权利.</p>
      </div>
    </footer>
    
    <!-- 登出确认对话框 -->
    <el-dialog
      v-model="logoutDialogVisible"
      title="退出确认"
      width="380px"
    >
      <span>确定要退出登录吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="logoutDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmLogout" :loading="loggingOut">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/modules/user/stores/userStore';
import { ElMessage } from 'element-plus';
import { Ticket } from '@element-plus/icons-vue';
import { runnerMenus } from '@/modules/user/router';
import { adjustLinkProtocol } from '@/utils/format';

import { useSystemStore } from '@/stores/systemStore';
const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 登出对话框
const logoutDialogVisible = ref(false);
const loggingOut = ref(false);



// 动态菜单项
const dynamicMenuItems = ref([]);

// 缓存的视图组件
const cachedViews = ref(['UserHome', 'UserProfile', 'UserOrders', 'UserAddresses', 'UserSettings']);

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  return route.path;
});

// 计算当前页面标题
const pageTitle = computed(() => {
  return route.meta.title || '用户中心';
});

// Computed property for runner menu items
const runnerMenuItems = computed(() => {
  if (runnerMenus && runnerMenus.length > 0 && runnerMenus[0].children) {
    // Here you could also filter items based on userStore.isRunner and item.requiresRunner if needed
    return runnerMenus[0].children;
  }
  return [];
});

/**
 * 处理下拉菜单命令
 * @param command 命令
 */
function handleCommand(command: string) {
  switch (command) {
    case 'profile':
      navigateTo('/user/profile');
      break;
    case 'settings':
      navigateTo('/user/settings');
      break;
    case 'logout':
      showLogoutConfirm();
      break;
    default:
      break;
  }
}

/**
 * 导航到指定路径
 * @param path 路径
 */
function navigateTo(path: string) {
  router.push(path);
}

/**
 * 显示登出确认对话框
 */
function showLogoutConfirm() {
  logoutDialogVisible.value = true;
}

/**
 * 确认登出
 */
async function confirmLogout() {
  try {
    loggingOut.value = true;
    await userStore.userLogout();
    ElMessage.success('已安全退出登录');
    router.push('/user/login');
  } catch (error) {
    console.error('登出失败:', error);
    ElMessage.error('登出失败，请稍后重试');
  } finally {
    loggingOut.value = false;
    logoutDialogVisible.value = false;
  }
}

/**
 * 获取动态菜单项
 */
async function fetchDynamicMenuItems() {
  try {
    // 这里应该从API获取动态菜单数据
    // 实际项目中，可能需要从后端获取用户可访问的模块和页面
    // 以下为模拟数据
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 这只是示例数据，实际项目中应该从API获取
    dynamicMenuItems.value = [
      // 以下是动态菜单示例，实际项目中应该从API获取
      // {
      //   path: '/user/favorites',
      //   title: '我的收藏',
      //   icon: 'Star'
      // }
    ];
  } catch (error) {
    console.error('获取动态菜单失败:', error);
  }
}

// 检查用户登录状态
function checkLoginStatus() {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录');
    router.push('/user/login');
  }
}

onMounted(() => {
  console.log('UserLayout mounted', systemInfo.value);
  // 检查登录状态
  checkLoginStatus();
  
  // 获取动态菜单
  fetchDynamicMenuItems();
});
</script>

<style scoped>
.user-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  background-color: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: 60px;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.site-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-dropdown-link {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown-link:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  margin-right: 8px;
}

.user-name {
  margin-right: 5px;
  font-size: 14px;
}

.main-content {
  flex: 1;
  margin-top: 60px;
}

.main-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  gap: 20px;
}

.sidebar {
  width: 220px;
  position: sticky;
  top: 80px;
  height: fit-content;
}

.menu {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.content {
  flex: 1;
  overflow: hidden;
}

.page-header {
  background-color: #ffffff;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.page-content {
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  min-height: 500px;
}

.footer {
  background-color: #ffffff;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);
  margin-top: 30px;
  padding: 20px 0;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  color: #909399;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
