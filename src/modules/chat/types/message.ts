/**
 * 消息相关类型定义
 */

// 消息类型枚举
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  VOICE = 'voice',
  VIDEO = 'video',
  SYSTEM = 'system',
  PRODUCT_CARD = 'product_card',
  ORDER_CARD = 'order_card'
}

// 消息状态枚举
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed'
}

// 发送者类型枚举
export enum SenderType {
  USER = 'user',
  MERCHANT = 'merchant',
  ADMIN = 'admin',
  SYSTEM = 'system'
}

// 基础消息接口
export interface BaseMessage {
  id: number
  session_id: number
  sender_id: number
  sender_type: SenderType
  receiver_id: number
  receiver_type: SenderType
  type: MessageType
  content: string
  status: MessageStatus
  created_at: string
  updated_at: string
  is_deleted: boolean
}

// 文本消息
export interface TextMessage extends BaseMessage {
  type: MessageType.TEXT
  content: string
}

// 图片消息
export interface ImageMessage extends BaseMessage {
  type: MessageType.IMAGE
  resource_id: string
  image_url: string
  thumbnail_url?: string
  width?: number
  height?: number
  file_size?: number
}

// 文件消息
export interface FileMessage extends BaseMessage {
  type: MessageType.FILE
  resource_id: string
  file_name: string
  file_url: string
  file_size: number
  file_ext: string
  mime_type: string
}

// 语音消息
export interface VoiceMessage extends BaseMessage {
  type: MessageType.VOICE
  resource_id: string
  voice_url: string
  duration: number // 秒
  file_size: number
}

// 视频消息
export interface VideoMessage extends BaseMessage {
  type: MessageType.VIDEO
  resource_id: string
  video_url: string
  thumbnail_url?: string
  duration: number // 秒
  width?: number
  height?: number
  file_size: number
}

// 系统消息
export interface SystemMessage extends BaseMessage {
  type: MessageType.SYSTEM
  system_type: 'notification' | 'warning' | 'info' | 'session_created' | 'session_closed'
  content: string
}

// 商品卡片消息
export interface ProductCardMessage extends BaseMessage {
  type: MessageType.PRODUCT_CARD
  product_id: number
  product_name: string
  product_image: string
  product_price: number
  product_url: string
  merchant_id: number
  merchant_name: string
}

// 订单卡片消息
export interface OrderCardMessage extends BaseMessage {
  type: MessageType.ORDER_CARD
  order_id: number
  order_number: string
  order_status: string
  order_amount: number
  order_time: string
  product_count: number
  order_url: string
}

// 联合消息类型
export type Message = 
  | TextMessage 
  | ImageMessage 
  | FileMessage 
  | VoiceMessage 
  | VideoMessage 
  | SystemMessage 
  | ProductCardMessage 
  | OrderCardMessage

// 消息发送请求
export interface SendMessageRequest {
  session_id: number
  type: MessageType
  content: string
  resource_id?: string
  extra_data?: Record<string, any>
}

// 消息发送响应
export interface SendMessageResponse {
  success: boolean
  message: Message
  error?: string
}

// 消息列表请求
export interface GetMessagesRequest {
  session_id: number
  page?: number
  page_size?: number
  before_message_id?: number
  after_message_id?: number
}

// 消息列表响应
export interface GetMessagesResponse {
  success: boolean
  data: {
    messages: Message[]
    total: number
    page: number
    page_size: number
    has_more: boolean
  }
  error?: string
}

// 标记消息已读请求
export interface MarkMessageReadRequest {
  message_ids: number[]
  session_id?: number
}

// 标记消息已读响应
export interface MarkMessageReadResponse {
  success: boolean
  updated_count: number
  error?: string
}

// 消息搜索请求
export interface SearchMessagesRequest {
  keyword: string
  session_id?: number
  message_type?: MessageType
  sender_type?: SenderType
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 消息搜索响应
export interface SearchMessagesResponse {
  success: boolean
  data: {
    messages: Message[]
    total: number
    page: number
    page_size: number
    has_more: boolean
  }
  error?: string
}

// 消息统计信息
export interface MessageStats {
  total_messages: number
  unread_messages: number
  today_messages: number
  message_types: Record<MessageType, number>
}

// 消息预览信息
export interface MessagePreview {
  type: MessageType
  content: string
  preview_text: string
  created_at: string
}

// 正在输入状态
export interface TypingStatus {
  session_id: number
  user_id: number
  user_type: SenderType
  is_typing: boolean
  timestamp: number
}

// 消息回执
export interface MessageReceipt {
  message_id: number
  session_id: number
  user_id: number
  user_type: SenderType
  status: MessageStatus
  timestamp: number
}

// 文件上传进度
export interface FileUploadProgress {
  file_id: string
  file_name: string
  total_size: number
  uploaded_size: number
  progress: number // 0-100
  status: 'uploading' | 'completed' | 'failed' | 'cancelled'
  error?: string
}

// 消息草稿
export interface MessageDraft {
  session_id: number
  content: string
  type: MessageType
  created_at: string
  updated_at: string
}