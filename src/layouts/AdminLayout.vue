<template>
  <div class="admin-layout">
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
        <div class="logo">
          <img v-if="systemInfo?.siteLogo" :src="adjustLinkProtocol(systemInfo?.siteLogo)" alt="Logo" class="logo-img" />
          <h2 class="logo-text" v-if="!isCollapse">{{ systemInfo?.siteName || 'O_Mall 管理后台' }}</h2>
        </div>
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical"
            :router="true"
            :collapse="isCollapse"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <template v-for="item in menuItems" :key="item.key">
              <!-- 有子菜单的情况 -->
              <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path">
                <template #title>
                  <el-icon v-if="item.icon">
                    <component :is="item.icon" />
                  </el-icon>
                  <span>{{ item.label }}</span>
                </template>
                <el-menu-item 
                  v-for="child in item.children" 
                  :key="child.key" 
                  :index="child.path"
                >
                  <span>{{ child.label }}</span>
                </el-menu-item>
              </el-sub-menu>
              
              <!-- 没有子菜单的情况 -->
              <el-menu-item v-else :index="item.path">
                <el-icon v-if="item.icon">
                  <component :is="item.icon" />
                </el-icon>
                <template #title>{{ item.label }}</template>
              </el-menu-item>
            </template>
          </el-menu>
        </el-scrollbar>
      </el-aside>
      
      <el-container class="main-container">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-icon class="toggle-icon" @click="toggleSidebar">
              <Fold v-if="!isCollapse" />
              <Expand v-else />
            </el-icon>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/admin/dashboard' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="currentRoute.meta.title">
                {{ currentRoute.meta.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-dropdown trigger="click">
              <div class="avatar-container">
                <el-avatar :size="32" :src="adminInfo.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ adminInfo.nickname }}</span>
                <el-icon><CaretBottom /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="navigateToProfile">个人信息</el-dropdown-item>
                  
                  <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import { Fold, Expand, User, CaretBottom } from '@element-plus/icons-vue';
import { ADMIN_MENU_ITEMS } from '../modules/admin/constants';
import { useAdminStore } from '../modules/admin/stores/adminStore';
import { useSystemStore } from '@/stores/systemStore';
import { adjustLinkProtocol } from '@/utils/format';

// 定义菜单项和路径数据的接口
interface MenuItem {
  key: string;
  label: string;
  icon?: string;
  path: string;
  children?: MenuItem[];
  [key: string]: any; // 允许添加额外属性如dynamic
}

interface PathItem {
  icon?: string;
  path: string;
  title: string;
  count: number;
  [key: string]: any;
}

interface ModuleData {
  module: string;
  paths: PathItem[];
}

const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);
const route = useRoute();
const router = useRouter();
const adminStore = useAdminStore();

const isCollapse = ref(false);
const staticMenuItems = ref<MenuItem[]>(ADMIN_MENU_ITEMS);
const menuItems = ref<MenuItem[]>([...ADMIN_MENU_ITEMS]);

// 处理前端路径数据，生成动态菜单项
const generateDynamicMenuItems = (frontendPaths: ModuleData[]) => {
  if (!frontendPaths || !Array.isArray(frontendPaths) || frontendPaths.length === 0) {
    console.log('前端路径数据为空，使用静态菜单');
    return staticMenuItems.value;
  }

  const newMenuItems: MenuItem[] = [...staticMenuItems.value];
  
  // 处理前端路径数据
  frontendPaths.forEach(moduleData => {
    if (!moduleData.module || !moduleData.paths || !Array.isArray(moduleData.paths)) {
      return;
    }
    
    // 根据模块生成子菜单
    const moduleName = moduleData.module;
    const modulePaths = moduleData.paths;
    
    // 跳过空路径
    if (modulePaths.length === 0) return;
    
    // 根据模块名称创建不同的菜单结构
    switch(moduleName) {
      case 'admin':
        // admin模块的路径直接添加到顶级菜单
        modulePaths.forEach((pathItem: PathItem) => {
          console.log('处理admin模块路径:', pathItem);
          // 检查是否已存在相同路径的菜单项（避免重复）
          const existingItem = newMenuItems.find(item => item.path === `/admin/${pathItem.path}`);
          if (!existingItem && pathItem.path && pathItem.title) {
            newMenuItems.push({
              key: pathItem.path,
              label: pathItem.title,
              icon: pathItem.icon || 'Menu', // 使用路径中的图标或默认图标
              path: `/admin/${pathItem.path}`,
              dynamic: true // 标记为动态生成的
            });
          }
        });
        break;
        
      case 'ui_config':
        // UI配置模块添加到系统设置子菜单下
        const systemMenu = newMenuItems.find(item => item.key === 'system');
        if (systemMenu && systemMenu.children) {
          modulePaths.forEach((pathItem: PathItem) => {
            // 检查是否已存在相同路径的菜单项
            const existingItem = systemMenu.children?.find(child => 
              child.path === `/admin/system/${pathItem.path}`
            );
            
            if (!existingItem && pathItem.path && pathItem.title) {
              systemMenu.children?.push({
                key: pathItem.path,
                label: pathItem.title,
                icon: pathItem.icon || undefined, // 使用路径中的图标或不设置（继承父级）
                path: `/admin/system/${pathItem.path}`,
                dynamic: true // 标记为动态生成的
              });
            }
          });
        }
        break;
        
      default:
        // 其他模块创建新的顶级菜单
        const moduleMenu: MenuItem = {
          key: moduleName,
          label: moduleName.charAt(0).toUpperCase() + moduleName.slice(1), // 首字母大写
          icon: 'Menu',
          path: `/admin/${moduleName}`,
          children: modulePaths.map((pathItem: PathItem) => ({
            key: pathItem.path,
            label: pathItem.title,
            icon: pathItem.icon || undefined, // 使用路径中的图标或不设置（继承父级）
            path: `/admin/${moduleName}/${pathItem.path}`,
            dynamic: true
          }))
        };
        
        // 检查是否已存在相同模块名的菜单
        const existingModule = newMenuItems.find(item => item.key === moduleName);
        if (!existingModule) {
          newMenuItems.push(moduleMenu);
        }
        break;
    }
  });
  
  console.log('生成的动态菜单：', newMenuItems);
  return newMenuItems;
};

// 监听前端路径数据变化，更新菜单
watch(() => adminStore.frontendPaths, (newPaths) => {
  console.log('监听到frontendPaths变化:', newPaths);
  if (newPaths && newPaths.length > 0) {
    console.log('前端路径数据已更新，重新生成菜单', newPaths);
    menuItems.value = generateDynamicMenuItems(newPaths);
  }
}, { deep: true, immediate: true });

// 监听路由变化，确保菜单状态同步
watch(() => route.path, (newPath, oldPath) => {
  console.log(`🔄 AdminLayout 路由变化: ${oldPath} -> ${newPath}`);
  
  // 如果是跨模块路由切换，重新获取菜单数据
  const oldModule = oldPath?.split('/')[2];
  const newModule = newPath?.split('/')[2];
  
  if (oldModule !== newModule) {
    console.log(`🔄 跨模块路由切换: ${oldModule} -> ${newModule}`);
    
    // 延迟一点时间确保路由完全切换
    setTimeout(async () => {
      try {
        // 重新获取前端路径数据
        const paths = await adminStore.fetchFrontendPaths(false);
        if (paths && paths.length > 0) {
          menuItems.value = generateDynamicMenuItems(paths);
          console.log('✅ 菜单数据已同步');
        }
      } catch (error) {
        console.error('❌ 同步菜单数据失败:', error);
      }
    }, 100);
  }
}, { immediate: false });

// 当前路由信息
const currentRoute = computed(() => route);

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path;
});

// 管理员信息
const adminInfo = computed(() => {
  return adminStore.currentAdmin || {
    nickname: '管理员',
    avatar: '',
  };
});

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value;
};

// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      adminStore.logout();
      router.push('/admin/login');
    })
    .catch(() => {});
};

// 跳转到个人信息页面
const navigateToProfile = () => {
  router.push('/admin/profile');
};

// 刷新前端路径数据
// const refreshMenuData = async () => {
//   try {
//     // 清除缓存并重新获取
//     await adminStore.clearFrontendPathsCache();
//     const paths = await adminStore.fetchFrontendPaths();
//     console.log('刷新获取的前端路径数据:', paths);
//     if (paths && paths.length > 0) {
//       menuItems.value = generateDynamicMenuItems(paths);
//     } else {
//       console.warn('获取的前端路径数据为空');
//     }
//   } catch (error) {
//     console.error('刷新菜单数据失败:', error);
//   }
// };

onMounted(async () => {
  // 如果有token但没有管理员信息，则获取管理员信息
  if (adminStore.token && !adminStore.currentAdmin) {
    await adminStore.fetchCurrentAdmin();
  }
  
  // 获取前端路径并生成菜单
  try {
    // 直接强制重新获取前端路径数据，避免使用缓存导致的问题
    console.log('组件挂载时重新获取前端路径数据');
    // 先清除缓存，确保获取最新数据
    await adminStore.clearFrontendPathsCache();
    const paths = await adminStore.fetchFrontendPaths();
    console.log('获取到最新前端路径数据:', paths);
    
    if (paths && paths.length > 0) {
      menuItems.value = generateDynamicMenuItems(paths);
    } else {
      console.warn('获取的前端路径数据为空，尝试重新获取...');
      // 延迟再试一次
      setTimeout(async () => {
        const retryPaths = await adminStore.fetchFrontendPaths();
        if (retryPaths && retryPaths.length > 0) {
          menuItems.value = generateDynamicMenuItems(retryPaths);
        }
      }, 1000);
    }
  } catch (error) {
    console.error('获取前端路径失败', error);
  }
});
</script>

<style scoped>
html, body {
  overflow: hidden; /* 禁止滚动条 */
}
.admin-layout {
  height: 100vh;
  width: 100%;
  margin: 0; /* 移除外边距 */
  padding: 0; /* 移除外边距 */
}

.layout-container {
  height: 100%;
  margin: 0; /* 移除外边距 */
  padding: 0; /* 移除外边距 */
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  background-color: #2b3649;
}

.logo-img {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.logo-text {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
}

.el-menu-vertical {
  border-right: none;
}

.header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-icon {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.avatar-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 5px;
  color: #606266;
}

.main {
  background-color: #f0f2f5;
  padding: 20px; /* 根据需要调整内边距 */
}

/* 路由切换动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
</style>