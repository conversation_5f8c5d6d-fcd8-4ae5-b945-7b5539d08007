/**
 * 模拟数据工厂
 * 用于生成测试所需的模拟数据
 */

import type { User, Session, Message, FileInfo } from '../../types'

/**
 * 生成随机 ID
 */
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * 生成随机字符串
 */
function generateRandomString(length: number = 10): string {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成随机数字
 */
function generateRandomNumber(min: number = 0, max: number = 100): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成随机日期
 */
function generateRandomDate(daysAgo: number = 30): Date {
  const now = new Date()
  const pastDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000)
  const randomTime = pastDate.getTime() + Math.random() * (now.getTime() - pastDate.getTime())
  return new Date(randomTime)
}

/**
 * 用户工厂
 */
export class UserFactory {
  /**
   * 创建单个用户
   */
  static create(overrides: Partial<User> = {}): User {
    const id = generateId()
    return {
      id,
      name: `User_${generateRandomString(5)}`,
      email: `user_${id}@example.com`,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${id}`,
      status: Math.random() > 0.3 ? 'online' : 'offline',
      lastSeen: generateRandomDate(7),
      createdAt: generateRandomDate(365),
      updatedAt: generateRandomDate(1),
      ...overrides
    }
  }
  
  /**
   * 创建多个用户
   */
  static createMany(count: number, overrides: Partial<User> = {}): User[] {
    return Array.from({ length: count }, () => this.create(overrides))
  }
  
  /**
   * 创建在线用户
   */
  static createOnline(overrides: Partial<User> = {}): User {
    return this.create({
      status: 'online',
      lastSeen: new Date(),
      ...overrides
    })
  }
  
  /**
   * 创建离线用户
   */
  static createOffline(overrides: Partial<User> = {}): User {
    return this.create({
      status: 'offline',
      lastSeen: generateRandomDate(7),
      ...overrides
    })
  }
  
  /**
   * 创建管理员用户
   */
  static createAdmin(overrides: Partial<User> = {}): User {
    return this.create({
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      ...overrides
    })
  }
}

/**
 * 会话工厂
 */
export class SessionFactory {
  /**
   * 创建单个会话
   */
  static create(overrides: Partial<Session> = {}): Session {
    const id = generateId()
    const type = overrides.type || (Math.random() > 0.5 ? 'private' : 'group')
    
    return {
      id,
      name: type === 'private' ? '' : `Group_${generateRandomString(6)}`,
      type,
      avatar: type === 'group' ? `https://api.dicebear.com/7.x/shapes/svg?seed=${id}` : undefined,
      description: type === 'group' ? `This is a group chat about ${generateRandomString(10)}` : undefined,
      participants: UserFactory.createMany(type === 'private' ? 2 : generateRandomNumber(3, 10)),
      lastMessage: null,
      lastMessageTime: generateRandomDate(1),
      unreadCount: generateRandomNumber(0, 5),
      isPinned: Math.random() > 0.8,
      isMuted: Math.random() > 0.9,
      isArchived: Math.random() > 0.95,
      createdAt: generateRandomDate(30),
      updatedAt: generateRandomDate(1),
      ...overrides
    }
  }
  
  /**
   * 创建多个会话
   */
  static createMany(count: number, overrides: Partial<Session> = {}): Session[] {
    return Array.from({ length: count }, () => this.create(overrides))
  }
  
  /**
   * 创建私聊会话
   */
  static createPrivate(overrides: Partial<Session> = {}): Session {
    return this.create({
      type: 'private',
      name: '',
      participants: UserFactory.createMany(2),
      ...overrides
    })
  }
  
  /**
   * 创建群聊会话
   */
  static createGroup(overrides: Partial<Session> = {}): Session {
    return this.create({
      type: 'group',
      name: `Group_${generateRandomString(8)}`,
      participants: UserFactory.createMany(generateRandomNumber(3, 10)),
      ...overrides
    })
  }
  
  /**
   * 创建频道会话
   */
  static createChannel(overrides: Partial<Session> = {}): Session {
    return this.create({
      type: 'channel',
      name: `Channel_${generateRandomString(8)}`,
      participants: UserFactory.createMany(generateRandomNumber(10, 100)),
      ...overrides
    })
  }
  
  /**
   * 创建有未读消息的会话
   */
  static createWithUnread(overrides: Partial<Session> = {}): Session {
    return this.create({
      unreadCount: generateRandomNumber(1, 10),
      ...overrides
    })
  }
  
  /**
   * 创建置顶会话
   */
  static createPinned(overrides: Partial<Session> = {}): Session {
    return this.create({
      isPinned: true,
      ...overrides
    })
  }
  
  /**
   * 创建静音会话
   */
  static createMuted(overrides: Partial<Session> = {}): Session {
    return this.create({
      isMuted: true,
      ...overrides
    })
  }
}

/**
 * 消息工厂
 */
export class MessageFactory {
  /**
   * 创建单个消息
   */
  static create(overrides: Partial<Message> = {}): Message {
    const id = generateId()
    const type = overrides.type || 'text'
    
    let content = ''
    switch (type) {
      case 'text':
        content = `Message content ${generateRandomString(20)}`
        break
      case 'image':
        content = `https://picsum.photos/400/300?random=${id}`
        break
      case 'file':
        content = `document_${generateRandomString(8)}.pdf`
        break
      case 'system':
        content = `System message: ${generateRandomString(15)}`
        break
    }
    
    return {
      id,
      content,
      type,
      senderId: generateId(),
      sessionId: generateId(),
      timestamp: generateRandomDate(7),
      status: Math.random() > 0.1 ? 'sent' : 'sending',
      replyTo: Math.random() > 0.8 ? generateId() : undefined,
      mentions: Math.random() > 0.9 ? [generateId()] : [],
      reactions: Math.random() > 0.7 ? {
        '👍': [generateId()],
        '❤️': [generateId(), generateId()]
      } : {},
      isEdited: Math.random() > 0.95,
      editedAt: Math.random() > 0.95 ? generateRandomDate(1) : undefined,
      ...overrides
    }
  }
  
  /**
   * 创建多个消息
   */
  static createMany(count: number, overrides: Partial<Message> = {}): Message[] {
    return Array.from({ length: count }, () => this.create(overrides))
  }
  
  /**
   * 创建文本消息
   */
  static createText(overrides: Partial<Message> = {}): Message {
    return this.create({
      type: 'text',
      content: `Text message: ${generateRandomString(30)}`,
      ...overrides
    })
  }
  
  /**
   * 创建图片消息
   */
  static createImage(overrides: Partial<Message> = {}): Message {
    const id = generateId()
    return this.create({
      type: 'image',
      content: `https://picsum.photos/800/600?random=${id}`,
      metadata: {
        width: 800,
        height: 600,
        size: generateRandomNumber(100000, 2000000),
        filename: `image_${id}.jpg`
      },
      ...overrides
    })
  }
  
  /**
   * 创建文件消息
   */
  static createFile(overrides: Partial<Message> = {}): Message {
    const id = generateId()
    const extensions = ['pdf', 'doc', 'xlsx', 'txt', 'zip']
    const extension = extensions[Math.floor(Math.random() * extensions.length)]
    
    return this.create({
      type: 'file',
      content: `document_${id}.${extension}`,
      metadata: {
        size: generateRandomNumber(10000, 10000000),
        filename: `document_${id}.${extension}`,
        mimeType: `application/${extension}`
      },
      ...overrides
    })
  }
  
  /**
   * 创建系统消息
   */
  static createSystem(overrides: Partial<Message> = {}): Message {
    const systemMessages = [
      'User joined the chat',
      'User left the chat',
      'Chat name was changed',
      'User was added to the chat',
      'User was removed from the chat'
    ]
    
    return this.create({
      type: 'system',
      content: systemMessages[Math.floor(Math.random() * systemMessages.length)],
      ...overrides
    })
  }
  
  /**
   * 创建回复消息
   */
  static createReply(replyToId: string, overrides: Partial<Message> = {}): Message {
    return this.create({
      type: 'text',
      content: `Reply message: ${generateRandomString(25)}`,
      replyTo: replyToId,
      ...overrides
    })
  }
  
  /**
   * 创建带提及的消息
   */
  static createWithMentions(mentionIds: string[], overrides: Partial<Message> = {}): Message {
    return this.create({
      type: 'text',
      content: `Message with mentions: ${generateRandomString(20)}`,
      mentions: mentionIds,
      ...overrides
    })
  }
  
  /**
   * 创建会话中的消息序列
   */
  static createConversation(sessionId: string, userIds: string[], messageCount: number = 10): Message[] {
    const messages: Message[] = []
    
    for (let i = 0; i < messageCount; i++) {
      const senderId = userIds[Math.floor(Math.random() * userIds.length)]
      const timestamp = new Date(Date.now() - (messageCount - i) * 60000) // 每分钟一条消息
      
      let message: Message
      
      // 10% 概率创建系统消息
      if (Math.random() < 0.1) {
        message = this.createSystem({
          sessionId,
          senderId,
          timestamp
        })
      }
      // 15% 概率创建图片消息
      else if (Math.random() < 0.15) {
        message = this.createImage({
          sessionId,
          senderId,
          timestamp
        })
      }
      // 10% 概率创建文件消息
      else if (Math.random() < 0.1) {
        message = this.createFile({
          sessionId,
          senderId,
          timestamp
        })
      }
      // 20% 概率创建回复消息（如果有之前的消息）
      else if (messages.length > 0 && Math.random() < 0.2) {
        const replyToMessage = messages[Math.floor(Math.random() * messages.length)]
        message = this.createReply(replyToMessage.id, {
          sessionId,
          senderId,
          timestamp
        })
      }
      // 其余创建普通文本消息
      else {
        message = this.createText({
          sessionId,
          senderId,
          timestamp
        })
      }
      
      messages.push(message)
    }
    
    return messages
  }
}

/**
 * 文件工厂
 */
export class FileFactory {
  /**
   * 创建文件信息
   */
  static create(overrides: Partial<FileInfo> = {}): FileInfo {
    const id = generateId()
    const extensions = ['jpg', 'png', 'pdf', 'doc', 'xlsx', 'txt', 'zip', 'mp4', 'mp3']
    const extension = extensions[Math.floor(Math.random() * extensions.length)]
    const filename = `file_${id}.${extension}`
    
    return {
      id,
      name: filename,
      size: generateRandomNumber(1000, 10000000),
      type: this.getMimeType(extension),
      url: `https://example.com/files/${filename}`,
      uploadedAt: generateRandomDate(30),
      uploadedBy: generateId(),
      ...overrides
    }
  }
  
  /**
   * 创建多个文件
   */
  static createMany(count: number, overrides: Partial<FileInfo> = {}): FileInfo[] {
    return Array.from({ length: count }, () => this.create(overrides))
  }
  
  /**
   * 创建图片文件
   */
  static createImage(overrides: Partial<FileInfo> = {}): FileInfo {
    const id = generateId()
    return this.create({
      name: `image_${id}.jpg`,
      type: 'image/jpeg',
      size: generateRandomNumber(100000, 5000000),
      ...overrides
    })
  }
  
  /**
   * 创建文档文件
   */
  static createDocument(overrides: Partial<FileInfo> = {}): FileInfo {
    const id = generateId()
    return this.create({
      name: `document_${id}.pdf`,
      type: 'application/pdf',
      size: generateRandomNumber(50000, 2000000),
      ...overrides
    })
  }
  
  /**
   * 创建视频文件
   */
  static createVideo(overrides: Partial<FileInfo> = {}): FileInfo {
    const id = generateId()
    return this.create({
      name: `video_${id}.mp4`,
      type: 'video/mp4',
      size: generateRandomNumber(5000000, 100000000),
      ...overrides
    })
  }
  
  /**
   * 创建音频文件
   */
  static createAudio(overrides: Partial<FileInfo> = {}): FileInfo {
    const id = generateId()
    return this.create({
      name: `audio_${id}.mp3`,
      type: 'audio/mpeg',
      size: generateRandomNumber(1000000, 10000000),
      ...overrides
    })
  }
  
  /**
   * 根据扩展名获取 MIME 类型
   */
  private static getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      txt: 'text/plain',
      zip: 'application/zip',
      mp4: 'video/mp4',
      mp3: 'audio/mpeg'
    }
    
    return mimeTypes[extension] || 'application/octet-stream'
  }
}

/**
 * API 响应工厂
 */
export class ApiResponseFactory {
  /**
   * 创建成功响应
   */
  static createSuccess<T>(data: T): { success: true; data: T } {
    return {
      success: true,
      data
    }
  }
  
  /**
   * 创建错误响应
   */
  static createError(message: string, code?: string): { success: false; error: { message: string; code?: string } } {
    return {
      success: false,
      error: {
        message,
        code
      }
    }
  }
  
  /**
   * 创建分页响应
   */
  static createPaginated<T>(
    items: T[],
    page: number = 1,
    pageSize: number = 20,
    total?: number
  ): {
    success: true
    data: {
      items: T[]
      pagination: {
        page: number
        pageSize: number
        total: number
        totalPages: number
        hasNext: boolean
        hasPrev: boolean
      }
    }
  } {
    const actualTotal = total || items.length
    const totalPages = Math.ceil(actualTotal / pageSize)
    
    return {
      success: true,
      data: {
        items,
        pagination: {
          page,
          pageSize,
          total: actualTotal,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    }
  }
}

/**
 * WebSocket 事件工厂
 */
export class WebSocketEventFactory {
  /**
   * 创建消息事件
   */
  static createMessageEvent(message: Message): any {
    return {
      type: 'message',
      data: message,
      timestamp: new Date().toISOString()
    }
  }
  
  /**
   * 创建用户状态事件
   */
  static createUserStatusEvent(userId: string, status: 'online' | 'offline'): any {
    return {
      type: 'user_status',
      data: {
        userId,
        status,
        timestamp: new Date().toISOString()
      }
    }
  }
  
  /**
   * 创建输入状态事件
   */
  static createTypingEvent(userId: string, sessionId: string, isTyping: boolean): any {
    return {
      type: 'typing',
      data: {
        userId,
        sessionId,
        isTyping,
        timestamp: new Date().toISOString()
      }
    }
  }
  
  /**
   * 创建会话更新事件
   */
  static createSessionUpdateEvent(session: Session): any {
    return {
      type: 'session_update',
      data: session,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * 综合数据工厂
 * 用于创建完整的测试场景数据
 */
export class ScenarioFactory {
  /**
   * 创建完整的聊天场景
   */
  static createChatScenario(): {
    currentUser: User
    users: User[]
    sessions: Session[]
    messages: Message[]
  } {
    // 创建当前用户
    const currentUser = UserFactory.createOnline({
      name: 'Current User',
      email: '<EMAIL>'
    })
    
    // 创建其他用户
    const users = UserFactory.createMany(10)
    users.push(currentUser)
    
    // 创建会话
    const sessions = SessionFactory.createMany(5)
    
    // 为每个会话分配参与者
    sessions.forEach(session => {
      session.participants = [currentUser, ...users.slice(0, session.type === 'private' ? 1 : generateRandomNumber(2, 5))]
    })
    
    // 为每个会话创建消息
    const messages: Message[] = []
    sessions.forEach(session => {
      const sessionMessages = MessageFactory.createConversation(
        session.id,
        session.participants.map(p => p.id),
        generateRandomNumber(5, 20)
      )
      messages.push(...sessionMessages)
      
      // 设置会话的最后消息
      if (sessionMessages.length > 0) {
        const lastMessage = sessionMessages[sessionMessages.length - 1]
        session.lastMessage = lastMessage
        session.lastMessageTime = lastMessage.timestamp
      }
    })
    
    return {
      currentUser,
      users,
      sessions,
      messages
    }
  }
  
  /**
   * 创建文件上传场景
   */
  static createFileUploadScenario(): {
    files: FileInfo[]
    uploadProgress: Record<string, number>
    uploadErrors: Record<string, string>
  } {
    const files = [
      FileFactory.createImage(),
      FileFactory.createDocument(),
      FileFactory.createVideo(),
      FileFactory.createAudio()
    ]
    
    const uploadProgress: Record<string, number> = {}
    const uploadErrors: Record<string, string> = {}
    
    files.forEach(file => {
      if (Math.random() > 0.8) {
        uploadErrors[file.id] = 'Upload failed: File too large'
      } else {
        uploadProgress[file.id] = generateRandomNumber(0, 100)
      }
    })
    
    return {
      files,
      uploadProgress,
      uploadErrors
    }
  }
}