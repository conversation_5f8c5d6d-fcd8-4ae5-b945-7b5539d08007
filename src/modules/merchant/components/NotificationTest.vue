<template>
  <div class="notification-test">
    <h3>通知管理测试</h3>
    <el-button @click="testRefundNotification" type="warning">
      测试退款通知
    </el-button>
    <el-button @click="testGeneralNotification" type="info">
      测试一般通知
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ElNotification } from 'element-plus'

// 测试退款通知
const testRefundNotification = () => {
  const mockNotification = {
    type: 'notification',
    event: 'merchant_refund_request',
    session_id: 0,
    data: {
      content: "订单 202507251442251419 收到退款申请，退款金额 8 元，退款原因：不想要了/拍错了",
      data: {
        action_type: 'refund_process',
        action_url: '/merchant/refund/detail/10',
        apply_time: 1753426322,
        merchant_id: 1,
        message: '用户申请退款，订单号：202507251442251419，退款金额：8.00元，退款原因：不想要了/拍错了'
      },
      expire_time: 1753685522,
      order_id: 0,
      order_no: "",
      persistent: true,
      priority: 2,
      title: "收到退款申请",
      type: "merchant_refund_request"
    },
    timestamp: 1753426322
  }

  // 模拟处理退款通知
  ElNotification({
    title: mockNotification.data.title || '收到退款申请',
    message: mockNotification.data.content || '有新的退款申请需要处理',
    type: 'warning',
    position: 'top-right',
    duration: 8000,
    showClose: true,
    onClick: () => {
      if (mockNotification.data.data?.action_url) {
        window.open(mockNotification.data.data.action_url, '_blank')
      }
    }
  })
}

// 测试一般通知
const testGeneralNotification = () => {
  ElNotification({
    title: '新通知',
    message: '这是一个测试通知',
    type: 'info',
    position: 'top-right',
    duration: 5000,
    showClose: true
  })
}
</script>

<style scoped>
.notification-test {
  padding: 20px;
  
  h3 {
    margin-bottom: 16px;
  }
  
  .el-button {
    margin-right: 12px;
  }
}
</style>
