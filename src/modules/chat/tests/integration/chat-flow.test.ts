/**
 * 聊天模块集成测试 - 完整聊天流程
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ChatWindow from '../../components/ChatWindow.vue'
import { useChatStore } from '../../stores/chat'
import { useSessionStore } from '../../stores/session'
import { useMessageStore } from '../../stores/message'
import { useFileStore } from '../../stores/file'
import { useNotificationStore } from '../../stores/notification'
import { ChatClient } from '../../services/client'
import type { Session, Message, User } from '../../types'

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readyState = MockWebSocket.CONNECTING
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(public url: string) {
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN
      this.onopen?.(new Event('open'))
    }, 100)
  }

  send(data: string) {
    // 模拟服务器响应
    setTimeout(() => {
      const message = JSON.parse(data)
      this.simulateServerResponse(message)
    }, 50)
  }

  close() {
    this.readyState = MockWebSocket.CLOSED
    this.onclose?.(new CloseEvent('close'))
  }

  private simulateServerResponse(message: any) {
    switch (message.type) {
      case 'send_message':
        this.onmessage?.(new MessageEvent('message', {
          data: JSON.stringify({
            type: 'message_sent',
            data: {
              id: `msg_${Date.now()}`,
              ...message.data,
              timestamp: new Date().toISOString(),
              status: 'sent'
            }
          })
        }))
        break
      case 'join_session':
        this.onmessage?.(new MessageEvent('message', {
          data: JSON.stringify({
            type: 'session_joined',
            data: { sessionId: message.data.sessionId }
          })
        }))
        break
    }
  }
}

// Mock APIs
const mockSessionApi = {
  getSessions: vi.fn().mockResolvedValue({
    data: [
      {
        id: 'session1',
        name: 'Test Chat',
        type: 'private',
        participants: [
          { id: 'user1', name: 'Alice', avatar: 'avatar1.jpg', isOnline: true },
          { id: 'user2', name: 'Bob', avatar: 'avatar2.jpg', isOnline: false }
        ],
        lastMessage: null,
        unreadCount: 0,
        isPinned: false,
        isMuted: false,
        isArchived: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    total: 1
  }),
  getSession: vi.fn().mockResolvedValue({
    data: {
      id: 'session1',
      name: 'Test Chat',
      type: 'private',
      participants: [
        { id: 'user1', name: 'Alice', avatar: 'avatar1.jpg', isOnline: true },
        { id: 'user2', name: 'Bob', avatar: 'avatar2.jpg', isOnline: false }
      ]
    }
  }),
  createSession: vi.fn().mockResolvedValue({
    data: {
      id: 'new_session',
      name: 'New Chat',
      type: 'private',
      participants: []
    }
  })
}

const mockMessageApi = {
  getMessages: vi.fn().mockResolvedValue({
    data: [],
    total: 0
  }),
  sendMessage: vi.fn().mockResolvedValue({
    data: {
      id: 'msg1',
      content: 'Test message',
      senderId: 'user1',
      sessionId: 'session1',
      timestamp: new Date().toISOString(),
      type: 'text',
      status: 'sent'
    }
  })
}

const mockFileApi = {
  uploadFile: vi.fn().mockResolvedValue({
    data: {
      id: 'file1',
      name: 'test.jpg',
      url: 'https://example.com/test.jpg',
      type: 'image/jpeg',
      size: 1024
    }
  })
}

// Mock 模块
vi.mock('../../api/session', () => ({ sessionApi: mockSessionApi }))
vi.mock('../../api/message', () => ({ messageApi: mockMessageApi }))
vi.mock('../../api/file', () => ({ fileApi: mockFileApi }))

// Mock 全局 WebSocket
global.WebSocket = MockWebSocket as any

describe('聊天模块集成测试', () => {
  let wrapper: any
  let router: any
  let chatStore: any
  let sessionStore: any
  let messageStore: any
  let fileStore: any
  let notificationStore: any

  beforeEach(async () => {
    setActivePinia(createPinia())
    
    // 创建路由
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/chat', component: ChatWindow },
        { path: '/chat/:sessionId', component: ChatWindow }
      ]
    })
    
    // 初始化 stores
    chatStore = useChatStore()
    sessionStore = useSessionStore()
    messageStore = useMessageStore()
    fileStore = useFileStore()
    notificationStore = useNotificationStore()
    
    // 设置当前用户
    chatStore.currentUser = {
      id: 'user1',
      name: 'Test User',
      avatar: 'avatar.jpg',
      email: '<EMAIL>',
      status: 'online'
    }
    
    // 挂载组件
    wrapper = mount(ChatWindow, {
      global: {
        plugins: [createPinia(), router]
      }
    })
    
    // 等待组件初始化
    await wrapper.vm.$nextTick()
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  describe('初始化流程', () => {
    it('应该正确初始化聊天客户端', async () => {
      expect(chatStore.client).toBeDefined()
      expect(chatStore.isConnected).toBe(true)
    })

    it('应该加载会话列表', async () => {
      expect(mockSessionApi.getSessions).toHaveBeenCalled()
      expect(sessionStore.sessions.length).toBeGreaterThan(0)
    })

    it('应该设置事件监听器', () => {
      expect(chatStore.client?.listeners).toBeDefined()
    })
  })

  describe('会话管理流程', () => {
    it('应该能够创建新会话', async () => {
      const createBtn = wrapper.find('.create-session-btn')
      await createBtn.trigger('click')
      
      const createDialog = wrapper.find('.create-session-dialog')
      expect(createDialog.exists()).toBe(true)
      
      // 填写会话信息
      const nameInput = createDialog.find('input[name="name"]')
      await nameInput.setValue('New Test Chat')
      
      const confirmBtn = createDialog.find('.confirm-btn')
      await confirmBtn.trigger('click')
      
      expect(mockSessionApi.createSession).toHaveBeenCalled()
    })

    it('应该能够选择会话', async () => {
      // 等待会话列表加载
      await wrapper.vm.$nextTick()
      
      const sessionItem = wrapper.find('.session-item')
      await sessionItem.trigger('click')
      
      expect(sessionStore.currentSessionId).toBe('session1')
      expect(mockMessageApi.getMessages).toHaveBeenCalledWith('session1')
    })

    it('应该能够搜索会话', async () => {
      const searchInput = wrapper.find('.search-input')
      await searchInput.setValue('Test')
      
      expect(sessionStore.searchQuery).toBe('Test')
      
      // 验证过滤结果
      const sessionItems = wrapper.findAll('.session-item')
      expect(sessionItems.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('消息发送流程', () => {
    beforeEach(async () => {
      // 选择一个会话
      sessionStore.currentSessionId = 'session1'
      await wrapper.vm.$nextTick()
    })

    it('应该能够发送文本消息', async () => {
      const messageInput = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await messageInput.setValue('Hello, this is a test message!')
      await sendBtn.trigger('click')
      
      expect(mockMessageApi.sendMessage).toHaveBeenCalledWith({
        content: 'Hello, this is a test message!',
        sessionId: 'session1',
        type: 'text'
      })
      
      // 验证消息已添加到列表
      await wrapper.vm.$nextTick()
      expect(messageStore.messages.length).toBeGreaterThan(0)
    })

    it('应该能够发送文件消息', async () => {
      const fileInput = wrapper.find('input[type="file"]')
      const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' })
      
      Object.defineProperty(fileInput.element, 'files', {
        value: [testFile],
        writable: false
      })
      
      await fileInput.trigger('change')
      
      expect(fileStore.uploadQueue.length).toBe(1)
      expect(mockFileApi.uploadFile).toHaveBeenCalled()
    })

    it('应该能够回复消息', async () => {
      // 先添加一条消息
      const originalMessage: Message = {
        id: 'msg1',
        content: 'Original message',
        senderId: 'user2',
        senderName: 'Alice',
        sessionId: 'session1',
        timestamp: new Date(),
        type: 'text',
        status: 'sent',
        isOwn: false
      }
      messageStore.messages = [originalMessage]
      await wrapper.vm.$nextTick()
      
      // 点击回复按钮
      const messageItem = wrapper.find('.message-item')
      await messageItem.trigger('mouseenter')
      
      const replyBtn = wrapper.find('.reply-btn')
      await replyBtn.trigger('click')
      
      expect(messageStore.replyingTo).toEqual(originalMessage)
      
      // 发送回复
      const messageInput = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await messageInput.setValue('This is a reply')
      await sendBtn.trigger('click')
      
      expect(mockMessageApi.sendMessage).toHaveBeenCalledWith({
        content: 'This is a reply',
        sessionId: 'session1',
        type: 'text',
        replyTo: 'msg1'
      })
    })
  })

  describe('实时通信流程', () => {
    it('应该接收实时消息', async () => {
      // 模拟接收消息
      const incomingMessage = {
        type: 'new_message',
        data: {
          id: 'incoming_msg',
          content: 'Hello from another user',
          senderId: 'user2',
          senderName: 'Alice',
          sessionId: 'session1',
          timestamp: new Date().toISOString(),
          type: 'text',
          status: 'sent'
        }
      }
      
      // 触发 WebSocket 消息事件
      const ws = chatStore.client?.ws as MockWebSocket
      ws.onmessage?.(new MessageEvent('message', {
        data: JSON.stringify(incomingMessage)
      }))
      
      await wrapper.vm.$nextTick()
      
      // 验证消息已添加
      expect(messageStore.messages.some(msg => msg.id === 'incoming_msg')).toBe(true)
    })

    it('应该显示用户在线状态', async () => {
      // 模拟用户上线
      const userOnlineEvent = {
        type: 'user_online',
        data: {
          userId: 'user2',
          status: 'online'
        }
      }
      
      const ws = chatStore.client?.ws as MockWebSocket
      ws.onmessage?.(new MessageEvent('message', {
        data: JSON.stringify(userOnlineEvent)
      }))
      
      await wrapper.vm.$nextTick()
      
      // 验证用户状态更新
      const onlineUser = chatStore.onlineUsers.find(user => user.id === 'user2')
      expect(onlineUser?.status).toBe('online')
    })

    it('应该显示输入状态', async () => {
      // 模拟其他用户正在输入
      const typingEvent = {
        type: 'user_typing',
        data: {
          userId: 'user2',
          sessionId: 'session1',
          isTyping: true
        }
      }
      
      const ws = chatStore.client?.ws as MockWebSocket
      ws.onmessage?.(new MessageEvent('message', {
        data: JSON.stringify(typingEvent)
      }))
      
      await wrapper.vm.$nextTick()
      
      // 验证输入状态显示
      expect(wrapper.find('.typing-indicator').exists()).toBe(true)
    })
  })

  describe('通知流程', () => {
    it('应该显示新消息通知', async () => {
      // 模拟接收来自其他会话的消息
      const notificationMessage = {
        type: 'new_message',
        data: {
          id: 'notification_msg',
          content: 'Message from another session',
          senderId: 'user3',
          senderName: 'Charlie',
          sessionId: 'session2',
          timestamp: new Date().toISOString(),
          type: 'text',
          status: 'sent'
        }
      }
      
      const ws = chatStore.client?.ws as MockWebSocket
      ws.onmessage?.(new MessageEvent('message', {
        data: JSON.stringify(notificationMessage)
      }))
      
      await wrapper.vm.$nextTick()
      
      // 验证通知显示
      expect(notificationStore.notifications.length).toBeGreaterThan(0)
      expect(wrapper.find('.notification-toast').exists()).toBe(true)
    })

    it('应该播放通知声音', async () => {
      // Mock Audio
      const mockAudio = {
        play: vi.fn().mockResolvedValue(undefined),
        pause: vi.fn(),
        currentTime: 0
      }
      global.Audio = vi.fn().mockImplementation(() => mockAudio)
      
      // 启用声音通知
      notificationStore.settings.soundEnabled = true
      
      // 触发通知
      await notificationStore.createNotification({
        type: 'message',
        title: 'New Message',
        content: 'Test notification',
        senderId: 'user2'
      })
      
      expect(mockAudio.play).toHaveBeenCalled()
    })
  })

  describe('文件上传流程', () => {
    it('应该显示上传进度', async () => {
      const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' })
      
      // 添加文件到上传队列
      fileStore.addFilesToUploadQueue([testFile])
      await wrapper.vm.$nextTick()
      
      // 验证上传进度组件显示
      expect(wrapper.find('.file-upload-progress').exists()).toBe(true)
      
      // 开始上传
      await fileStore.startAllUploads()
      
      // 验证上传状态
      expect(fileStore.uploadQueue[0].status).toBe('uploading')
    })

    it('应该处理上传错误', async () => {
      const error = new Error('Upload failed')
      mockFileApi.uploadFile.mockRejectedValueOnce(error)
      
      const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' })
      
      fileStore.addFilesToUploadQueue([testFile])
      await fileStore.startAllUploads()
      
      await wrapper.vm.$nextTick()
      
      // 验证错误状态
      expect(fileStore.uploadQueue[0].status).toBe('failed')
      expect(fileStore.uploadQueue[0].error).toBe('Upload failed')
    })
  })

  describe('错误处理流程', () => {
    it('应该处理网络连接错误', async () => {
      // 模拟连接断开
      const ws = chatStore.client?.ws as MockWebSocket
      ws.onclose?.(new CloseEvent('close', { code: 1006, reason: 'Network error' }))
      
      await wrapper.vm.$nextTick()
      
      expect(chatStore.isConnected).toBe(false)
      expect(wrapper.find('.connection-error').exists()).toBe(true)
    })

    it('应该自动重连', async () => {
      // 模拟连接断开
      chatStore.isConnected = false
      
      // 触发重连
      await chatStore.reconnect()
      
      expect(chatStore.isConnected).toBe(true)
    })

    it('应该处理 API 错误', async () => {
      const error = new Error('API Error')
      mockMessageApi.sendMessage.mockRejectedValueOnce(error)
      
      const messageInput = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await messageInput.setValue('Test message')
      await sendBtn.trigger('click')
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.error-message').exists()).toBe(true)
    })
  })

  describe('性能优化', () => {
    it('应该使用虚拟滚动优化大量消息', async () => {
      // 添加大量消息
      const messages = Array.from({ length: 1000 }, (_, i) => ({
        id: `msg_${i}`,
        content: `Message ${i}`,
        senderId: 'user1',
        senderName: 'Test User',
        sessionId: 'session1',
        timestamp: new Date(),
        type: 'text' as const,
        status: 'sent' as const,
        isOwn: i % 2 === 0
      }))
      
      messageStore.messages = messages
      await wrapper.vm.$nextTick()
      
      // 验证虚拟滚动组件存在
      expect(wrapper.find('.virtual-list').exists()).toBe(true)
      
      // 验证只渲染可见消息
      const renderedMessages = wrapper.findAll('.message-item')
      expect(renderedMessages.length).toBeLessThan(messages.length)
    })

    it('应该防抖输入事件', async () => {
      const messageInput = wrapper.find('.message-textarea')
      
      // 快速输入多次
      await messageInput.setValue('T')
      await messageInput.setValue('Te')
      await messageInput.setValue('Tes')
      await messageInput.setValue('Test')
      
      // 验证只触发一次输入事件
      setTimeout(() => {
        expect(wrapper.emitted('typing-start')?.length).toBe(1)
      }, 300)
    })
  })

  describe('响应式设计', () => {
    it('应该在移动端适配布局', async () => {
      // 模拟移动端屏幕
      Object.defineProperty(window, 'innerWidth', {
        value: 768,
        writable: true
      })
      
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()
      
      expect(wrapper.classes()).toContain('mobile')
      expect(wrapper.vm.showSidebar).toBe(false)
    })

    it('应该在桌面端显示完整布局', async () => {
      // 模拟桌面端屏幕
      Object.defineProperty(window, 'innerWidth', {
        value: 1200,
        writable: true
      })
      
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()
      
      expect(wrapper.classes()).not.toContain('mobile')
      expect(wrapper.vm.showSidebar).toBe(true)
    })
  })

  describe('数据持久化', () => {
    it('应该保存用户设置', async () => {
      const settings = {
        theme: 'dark',
        soundEnabled: false,
        notificationEnabled: true
      }
      
      // 更新设置
      Object.assign(notificationStore.settings, settings)
      
      // 验证设置已保存到 localStorage
      const savedSettings = JSON.parse(localStorage.getItem('chat_settings') || '{}')
      expect(savedSettings).toMatchObject(settings)
    })

    it('应该恢复用户设置', async () => {
      const settings = {
        theme: 'dark',
        soundEnabled: false
      }
      
      // 预设 localStorage 数据
      localStorage.setItem('chat_settings', JSON.stringify(settings))
      
      // 重新初始化 store
      const newNotificationStore = useNotificationStore()
      
      expect(newNotificationStore.settings.theme).toBe('dark')
      expect(newNotificationStore.settings.soundEnabled).toBe(false)
    })
  })
})