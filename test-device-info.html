<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备信息测试页面 - 统一版本</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info-section {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .info-item {
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 150px;
        }
        .info-value {
            color: #333;
            font-family: monospace;
            background: #fff;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 设备信息测试页面 - 统一版本</h1>
        
        <div class="highlight">
            <strong>测试说明：</strong>此页面使用与项目中完全相同的统一设备信息生成逻辑。
            同一设备多次刷新页面应该生成相同的设备ID。
        </div>
        
        <div class="button-group">
            <button onclick="generateAndDisplay()">🔄 生成设备信息</button>
            <button onclick="testConsistency()">🧪 测试一致性</button>
            <button onclick="clearDeviceId()" class="clear-btn">🗑️ 清除设备ID</button>
        </div>
        
        <div class="info-section">
            <h3>📱 当前设备信息</h3>
            <div id="deviceInfo">点击"生成设备信息"按钮开始测试...</div>
        </div>
        
        <div class="info-section">
            <h3>💾 本地存储状态</h3>
            <div id="storageInfo">等待检查...</div>
        </div>
        
        <div id="testResults" class="test-results" style="display: none;">
            <h3>🧪 一致性测试结果</h3>
            <div id="consistencyResults"></div>
        </div>
    </div>

    <script>
        // 统一的设备信息生成逻辑 - 与项目中的 /src/utils/deviceInfo.ts 完全一致
        const DEVICE_ID_KEY = 'global_device_id';
        
        // 生成稳定的设备ID
        function generateStableDeviceId() {
            // 先检查是否已存在设备ID
            const existingId = localStorage.getItem(DEVICE_ID_KEY);
            if (existingId) {
                return existingId;
            }

            // 生成基于浏览器指纹的稳定设备ID
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            let canvasFingerprint = '';
            
            if (ctx) {
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Device fingerprint for O_Mall', 2, 2);
                canvasFingerprint = canvas.toDataURL();
            }
            
            // 收集浏览器指纹信息
            const fingerprint = [
                navigator.userAgent,
                navigator.language,
                navigator.languages?.join(',') || '',
                screen.width + 'x' + screen.height,
                screen.colorDepth,
                new Date().getTimezoneOffset(),
                navigator.platform,
                navigator.cookieEnabled,
                canvasFingerprint.slice(-50) // 取canvas指纹的后50个字符
            ].join('|');
            
            // 生成哈希值
            let hash = 0;
            for (let i = 0; i < fingerprint.length; i++) {
                const char = fingerprint.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            
            // 转换为36进制字符串并添加前缀
            const deviceId = 'omall_' + Math.abs(hash).toString(36);
            
            // 存储到localStorage
            localStorage.setItem(DEVICE_ID_KEY, deviceId);
            
            return deviceId;
        }
        
        // 获取设备类型
        function getDeviceType() {
            const userAgent = navigator.userAgent.toLowerCase();
            
            if (/ipad|tablet/.test(userAgent)) {
                return 'tablet';
            }
            
            if (/mobile|android|iphone|phone/.test(userAgent)) {
                return 'mobile';
            }
            
            return 'desktop';
        }
        
        // 获取操作系统信息
        function getOperatingSystem() {
            const userAgent = navigator.userAgent.toLowerCase();
            
            if (/iphone|ipad/.test(userAgent)) return 'ios';
            if (/android/.test(userAgent)) return 'android';
            if (/windows/.test(userAgent)) return 'windows';
            if (/mac/.test(userAgent)) return 'macos';
            if (/linux/.test(userAgent)) return 'linux';
            
            return 'unknown';
        }
        
        // 获取浏览器信息
        function getBrowserInfo() {
            const userAgent = navigator.userAgent.toLowerCase();
            
            if (/edg/.test(userAgent)) return 'Edge';
            if (/chrome/.test(userAgent) && !/edg/.test(userAgent)) return 'Chrome';
            if (/firefox/.test(userAgent)) return 'Firefox';
            if (/safari/.test(userAgent) && !/chrome/.test(userAgent)) return 'Safari';
            if (/opera/.test(userAgent)) return 'Opera';
            
            return 'Unknown';
        }
        
        // 获取设备名称
        function getDeviceName() {
            const os = getOperatingSystem();
            const browser = getBrowserInfo();
            return `${os.charAt(0).toUpperCase() + os.slice(1)} ${browser}`;
        }
        
        // 生成完整的设备信息
        function generateDeviceInfo() {
            const os = getOperatingSystem();
            return {
                device_type: getDeviceType(),
                device_id: generateStableDeviceId(),
                device_name: getDeviceName(),
                os: os,
                platform: os, // platform与os保持一致
                browser: getBrowserInfo(),
                app_version: '1.0.0', // 应用版本
                os_version: navigator.userAgent, // 操作系统版本信息
                user_agent: navigator.userAgent
            };
        }
        
        // 显示设备信息
        function generateAndDisplay() {
            const deviceInfo = generateDeviceInfo();
            const deviceInfoDiv = document.getElementById('deviceInfo');
            
            let html = '';
            for (const [key, value] of Object.entries(deviceInfo)) {
                html += `
                    <div class="info-item">
                        <span class="info-label">${key}:</span>
                        <span class="info-value">${value}</span>
                    </div>
                `;
            }
            
            deviceInfoDiv.innerHTML = html;
            updateStorageInfo();
        }
        
        // 更新存储信息显示
        function updateStorageInfo() {
            const storageInfoDiv = document.getElementById('storageInfo');
            const deviceId = localStorage.getItem(DEVICE_ID_KEY);
            
            if (deviceId) {
                storageInfoDiv.innerHTML = `
                    <div class="info-item">
                        <span class="info-label">存储键名:</span>
                        <span class="info-value">${DEVICE_ID_KEY}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">存储的设备ID:</span>
                        <span class="info-value">${deviceId}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">存储状态:</span>
                        <span class="info-value" style="color: green;">✅ 已存储</span>
                    </div>
                `;
            } else {
                storageInfoDiv.innerHTML = `
                    <div class="info-item">
                        <span class="info-label">存储状态:</span>
                        <span class="info-value" style="color: red;">❌ 未找到设备ID</span>
                    </div>
                `;
            }
        }
        
        // 测试一致性
        function testConsistency() {
            const results = [];
            
            // 生成5次设备信息，检查device_id是否一致
            for (let i = 0; i < 5; i++) {
                const info = generateDeviceInfo();
                results.push(info.device_id);
            }
            
            const isConsistent = results.every(id => id === results[0]);
            const testResultsDiv = document.getElementById('testResults');
            const consistencyResultsDiv = document.getElementById('consistencyResults');
            
            let html = `
                <div class="info-item">
                    <span class="info-label">测试次数:</span>
                    <span class="info-value">5次</span>
                </div>
                <div class="info-item">
                    <span class="info-label">一致性结果:</span>
                    <span class="info-value" style="color: ${isConsistent ? 'green' : 'red'};">
                        ${isConsistent ? '✅ 通过 - 设备ID保持一致' : '❌ 失败 - 设备ID不一致'}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">生成的设备ID:</span>
                    <div style="margin-top: 5px;">
            `;
            
            results.forEach((id, index) => {
                html += `<div style="margin: 2px 0;">第${index + 1}次: <span class="info-value">${id}</span></div>`;
            });
            
            html += `
                    </div>
                </div>
            `;
            
            consistencyResultsDiv.innerHTML = html;
            testResultsDiv.style.display = 'block';
        }
        
        // 清除设备ID
        function clearDeviceId() {
            localStorage.removeItem(DEVICE_ID_KEY);
            alert('设备ID已清除！下次生成将创建新的设备ID。');
            updateStorageInfo();
            
            // 清除显示的设备信息
            document.getElementById('deviceInfo').innerHTML = '设备ID已清除，点击"生成设备信息"重新生成...';
            document.getElementById('testResults').style.display = 'none';
        }
        
        // 页面加载时自动检查存储状态
        window.onload = function() {
            updateStorageInfo();
        };
    </script>
</body>
</html>