/**
 * 消息服务
 */

import type {
  Message,
  SendMessageRequest,
  SendMessageResponse,
  GetMessagesRequest,
  GetMessagesResponse,
  MarkMessageReadRequest,
  MarkMessageReadResponse,
  SearchMessagesRequest,
  SearchMessagesResponse,
  // MessageStats, // 暂时注释掉未使用的导入
  MessagePreview,
  MessageDraft,
  MessageType,
  // MessageStatus // 暂时注释掉未使用的导入
} from '../types/message'
import { Logger } from '../utils/logger'
// import { API_ENDPOINTS, API_TIMEOUT } from '../constants' // 暂时注释掉

/**
 * 消息服务类
 */
export class MessageService {
  private baseUrl: string
  private logger: Logger
  private abortController: AbortController | null = null

  constructor(baseUrl: string = '/api/v1') {
    this.baseUrl = baseUrl
    this.logger = new Logger('MessageService')
  }

  /**
   * 发送消息
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      this.logger.debug('Sending message:', request)
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as SendMessageResponse
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to send message')
      }
      
      this.logger.debug('Message sent successfully:', result.message)
      return result
      
    } catch (error) {
      this.logger.error('Failed to send message:', error)
      throw error
    }
  }

  /**
   * 获取消息列表
   */
  async getMessages(request: GetMessagesRequest): Promise<GetMessagesResponse> {
    try {
      this.logger.debug('Getting messages:', request)
      
      const params = new URLSearchParams()
      if (request.session_id) params.append('session_id', request.session_id.toString())
      if (request.page) params.append('page', request.page.toString())
      if (request.page_size) params.append('page_size', request.page_size.toString())
      if (request.before_message_id) params.append('before_message_id', request.before_message_id.toString())
      if (request.after_message_id) params.append('after_message_id', request.after_message_id.toString())
      
      const url = `${this.baseUrl}/api/v1/messages?${params.toString()}`
      const response = await this.fetch(url)
      
      const result = await response.json() as GetMessagesResponse
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get messages')
      }
      
      this.logger.debug(`Retrieved ${result.data?.messages?.length || 0} messages`)
      return result
      
    } catch (error) {
      this.logger.error('Failed to get messages:', error)
      throw error
    }
  }

  /**
   * 标记消息已读
   */
  async markMessageRead(request: MarkMessageReadRequest): Promise<MarkMessageReadResponse> {
    try {
      this.logger.debug('Marking message as read:', request)
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as MarkMessageReadResponse
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to mark message as read')
      }
      
      this.logger.debug('Message marked as read successfully')
      return result
      
    } catch (error) {
      this.logger.error('Failed to mark message as read:', error)
      throw error
    }
  }

  /**
   * 搜索消息
   */
  async searchMessages(request: SearchMessagesRequest): Promise<SearchMessagesResponse> {
    try {
      this.logger.debug('Searching messages:', request)
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })
      
      const result = await response.json() as SearchMessagesResponse
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to search messages')
      }
      
      this.logger.debug(`Found ${result.data?.messages?.length || 0} messages`)
      return result
      
    } catch (error) {
      this.logger.error('Failed to search messages:', error)
      throw error
    }
  }

  /**
   * 获取消息详情
   */
  async getMessageById(messageId: number): Promise<Message | null> {
    try {
      this.logger.debug('Getting message by ID:', messageId)
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/${messageId}`)
      
      if (response.status === 404) {
        return null
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get message')
      }
      
      this.logger.debug('Message retrieved successfully:', result.message)
      return result.message
      
    } catch (error) {
      this.logger.error('Failed to get message by ID:', error)
      throw error
    }
  }

  /**
   * 删除消息
   */
  async deleteMessage(messageId: number, hard: boolean = false): Promise<boolean> {
    try {
      this.logger.debug('Deleting message:', { messageId, hard })
      
      const params = new URLSearchParams()
      if (hard) params.append('hard', 'true')
      
      const url = `${this.baseUrl}/api/v1/messages/${messageId}?${params.toString()}`
      const response = await this.fetch(url, {
        method: 'DELETE'
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete message')
      }
      
      this.logger.debug('Message deleted successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to delete message:', error)
      throw error
    }
  }

  /**
   * 撤回消息
   */
  async recallMessage(messageId: number, reason?: string): Promise<boolean> {
    try {
      this.logger.debug('Recalling message:', { messageId, reason })
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/${messageId}/recall`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to recall message')
      }
      
      this.logger.debug('Message recalled successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to recall message:', error)
      throw error
    }
  }

  /**
   * 转发消息
   */
  async forwardMessage(messageId: number, targetSessionIds: number[]): Promise<Message[]> {
    try {
      this.logger.debug('Forwarding message:', { messageId, targetSessionIds })
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/${messageId}/forward`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ target_session_ids: targetSessionIds })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to forward message')
      }
      
      this.logger.debug('Message forwarded successfully')
      return result.messages || []
      
    } catch (error) {
      this.logger.error('Failed to forward message:', error)
      throw error
    }
  }

  /**
   * 获取消息统计
   */
  async getMessageStatistics(sessionId?: number, startTime?: number, endTime?: number): Promise<any> {
    try {
      this.logger.debug('Getting message statistics:', { sessionId, startTime, endTime })
      
      const params = new URLSearchParams()
      if (sessionId) params.append('session_id', sessionId.toString())
      if (startTime) params.append('start_time', startTime.toString())
      if (endTime) params.append('end_time', endTime.toString())
      
      const url = `${this.baseUrl}/api/v1/messages/statistics?${params.toString()}`
      const response = await this.fetch(url)
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get message statistics')
      }
      
      this.logger.debug('Message statistics retrieved successfully')
      return result.statistics
      
    } catch (error) {
      this.logger.error('Failed to get message statistics:', error)
      throw error
    }
  }

  /**
   * 获取消息预览
   */
  async getMessagePreview(sessionId: number): Promise<MessagePreview | null> {
    try {
      this.logger.debug('Getting message preview for session:', sessionId)
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/preview?session_id=${sessionId}`)
      
      if (response.status === 404) {
        return null
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get message preview')
      }
      
      return result.preview
      
    } catch (error) {
      this.logger.error('Failed to get message preview:', error)
      throw error
    }
  }

  /**
   * 保存消息草稿
   */
  async saveMessageDraft(sessionId: number, content: string, messageType: MessageType = 'TEXT' as any): Promise<boolean> {
    try {
      this.logger.debug('Saving message draft:', { sessionId, messageType, contentLength: content.length })
      
      const draft: MessageDraft = {
        session_id: sessionId,
        type: messageType,
        content,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/draft`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(draft)
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to save message draft')
      }
      
      this.logger.debug('Message draft saved successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to save message draft:', error)
      throw error
    }
  }

  /**
   * 获取消息草稿
   */
  async getMessageDraft(sessionId: number): Promise<MessageDraft | null> {
    try {
      this.logger.debug('Getting message draft for session:', sessionId)
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/draft?session_id=${sessionId}`)
      
      if (response.status === 404) {
        return null
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get message draft')
      }
      
      return result.draft
      
    } catch (error) {
      this.logger.error('Failed to get message draft:', error)
      throw error
    }
  }

  /**
   * 删除消息草稿
   */
  async deleteMessageDraft(sessionId: number): Promise<boolean> {
    try {
      this.logger.debug('Deleting message draft for session:', sessionId)
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/draft?session_id=${sessionId}`, {
        method: 'DELETE'
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete message draft')
      }
      
      this.logger.debug('Message draft deleted successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to delete message draft:', error)
      throw error
    }
  }

  /**
   * 批量标记消息已读
   */
  async batchMarkMessagesRead(messageIds: number[]): Promise<boolean> {
    try {
      this.logger.debug('Batch marking messages as read:', messageIds)
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/batch-read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message_ids: messageIds })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to batch mark messages as read')
      }
      
      this.logger.debug('Messages marked as read successfully')
      return true
      
    } catch (error) {
      this.logger.error('Failed to batch mark messages as read:', error)
      throw error
    }
  }

  /**
   * 获取未读消息数量
   */
  async getUnreadCount(sessionId?: number): Promise<number> {
    try {
      this.logger.debug('Getting unread count for session:', sessionId)
      
      const params = new URLSearchParams()
      if (sessionId) params.append('session_id', sessionId.toString())
      
      const url = `${this.baseUrl}/api/v1/messages/unread-count?${params.toString()}`
      const response = await this.fetch(url)
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get unread count')
      }
      
      return result.count || 0
      
    } catch (error) {
      this.logger.error('Failed to get unread count:', error)
      throw error
    }
  }

  /**
   * 发送输入状态
   */
  async sendTypingStatus(sessionId: number, isTyping: boolean): Promise<boolean> {
    try {
      this.logger.debug('Sending typing status:', { sessionId, isTyping })
      
      const response = await this.fetch(`${this.baseUrl}/api/v1/messages/typing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: sessionId,
          is_typing: isTyping
        })
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to send typing status')
      }
      
      return true
      
    } catch (error) {
      this.logger.error('Failed to send typing status:', error)
      throw error
    }
  }

  /**
   * 取消请求
   */
  cancelRequests(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }

  /**
   * 通用fetch方法
   */
  private async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    // 创建新的AbortController
    this.abortController = new AbortController()
    
    const defaultOptions: RequestInit = {
      signal: this.abortController.signal,
      headers: {
        'Accept': 'application/json',
        ...options.headers
      }
    }
    
    const mergedOptions = { ...defaultOptions, ...options }
    
    // 设置超时
    const timeoutId = setTimeout(() => {
      if (this.abortController) {
        this.abortController.abort()
      }
    }, 30000) // 30秒超时
    
    try {
      const response = await fetch(url, mergedOptions)
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      return response
      
    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout or cancelled')
      }
      
      throw error
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.cancelRequests()
    this.logger.info('MessageService destroyed')
  }
}

export default MessageService