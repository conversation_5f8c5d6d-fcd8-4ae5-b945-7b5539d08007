# Request模块拆分迁移指南

## 概述

原来的统一 `src/utils/request.ts` 文件已经按照功能拆分为三个独立的模块：

- **Admin模块**: `src/modules/admin/utils/request.ts`
- **Merchant模块**: `src/modules/merchant/utils/request.ts`
- **User模块**: `src/modules/user/utils/request.ts`

## 迁移步骤

### 1. Admin模块迁移

**原来的导入方式：**
```typescript
import { get, post, put, del, patch } from '@/utils/request';
```

**新的导入方式：**
```typescript
import { get, post, put, del, patch } from '@/modules/admin/utils/request';
```

**需要修改的文件：**
- `src/modules/admin/api/*.ts` - 所有admin模块的API文件
- `src/modules/admin/views/*.vue` - 直接使用request的Vue组件
- `src/modules/admin/service/*.ts` - admin模块的服务文件

### 2. Merchant模块迁移

**原来的导入方式：**
```typescript
import { get, post, put, del, patch } from '@/utils/request';
```

**新的导入方式：**
```typescript
import { get, post, put, del, patch } from '@/modules/merchant/utils/request';
```

**需要修改的文件：**
- `src/modules/merchant/api/*.ts` - 所有merchant模块的API文件
- `src/modules/merchant/views/*.vue` - 直接使用request的Vue组件
- `src/modules/merchant/service/*.ts` - merchant模块的服务文件

### 3. User模块迁移

**原来的导入方式：**
```typescript
import request from '@/utils/request';
// 或者
import { get, post, put, del, patch } from '@/utils/request';
```

**新的导入方式：**
```typescript
import request from '@/modules/user/utils/request';
// 或者
import { get, post, put, del, patch } from '@/modules/user/utils/request';
```

**需要修改的文件：**
- `src/modules/user/api/*.ts` - 所有user模块的API文件
- `src/modules/user/views/*.vue` - 直接使用request的Vue组件
- `src/modules/user/service/*.ts` - user模块的服务文件
- **注意**: Runner相关的API文件也使用user模块的request

## 主要改进

### 1. 模块隔离
- 每个模块有独立的token管理
- 独立的错误处理和路由跳转
- 减少模块间的耦合

### 2. 简化逻辑
- 移除了复杂的namespace动态判断
- 每个模块只关心自己的业务逻辑
- 更清晰的代码结构

### 3. 更好的维护性
- 模块独立，便于维护和调试
- 减少了全局状态的复杂性
- 更容易进行单元测试

## 特殊说明

### Runner模块
Runner模块使用User模块的request工具，因为：
- Runner和User共享相同的token存储机制
- Runner的API请求使用user模块的认证体系
- 简化了token管理的复杂性

### 白名单配置
每个模块都有自己的白名单配置：

**Admin模块白名单：**
- `/v1/admin/login`
- `/v1/admin/refresh-token`
- `/v1/admin/resetpassword`

**Merchant模块白名单：**
- `/v1/merchant/public/login`
- `/v1/merchant/refresh-token`
- `/v1/merchant/resetpassword`

**User模块白名单：**
- `/v1/user/login`
- `/v1/user/refresh-token`
- `/v1/user/resetpassword`
- `/v1/runner/login`
- `/v1/runner/refresh-token`
- `/v1/runner/resetpassword`

## 批量替换命令

可以使用以下命令进行批量替换：

### Admin模块
```bash
# 在admin模块目录下执行
find src/modules/admin -name "*.ts" -o -name "*.vue" | xargs sed -i '' "s|from '@/utils/request'|from '@/modules/admin/utils/request'|g"
```

### Merchant模块
```bash
# 在merchant模块目录下执行
find src/modules/merchant -name "*.ts" -o -name "*.vue" | xargs sed -i '' "s|from '@/utils/request'|from '@/modules/merchant/utils/request'|g"
```

### User模块
```bash
# 在user模块目录下执行
find src/modules/user -name "*.ts" -o -name "*.vue" | xargs sed -i '' "s|from '@/utils/request'|from '@/modules/user/utils/request'|g"
```

## 验证迁移

迁移完成后，请确保：

1. **编译检查**: 运行 `npm run build` 确保没有编译错误
2. **功能测试**: 测试各模块的登录、token刷新等功能
3. **路由跳转**: 验证401错误时的路由跳转是否正确
4. **Token管理**: 确认各模块的token独立管理

## 回滚方案

如果迁移过程中出现问题，可以：

1. 保留原来的 `src/utils/request.ts` 文件作为备份
2. 将导入路径改回原来的 `@/utils/request`
3. 删除新创建的模块request文件

## 注意事项

1. **逐步迁移**: 建议按模块逐步迁移，而不是一次性全部修改
2. **测试验证**: 每个模块迁移完成后都要进行充分测试
3. **团队协调**: 确保团队成员了解新的导入路径
4. **文档更新**: 更新相关的开发文档和API文档