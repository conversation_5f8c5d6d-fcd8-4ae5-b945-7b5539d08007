/**
 * 聊天模块端到端测试
 * 使用 Playwright 进行真实浏览器环境测试
 */

import { test, expect, type Page } from '@playwright/test'

// 测试数据
const testUsers = {
  alice: {
    id: 'user1',
    name: 'Alice',
    email: '<EMAIL>',
    password: 'password123'
  },
  bob: {
    id: 'user2',
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123'
  }
}

const testSession = {
  id: 'session1',
  name: 'Test Chat Room',
  type: 'group'
}

// 页面对象模式
class ChatPage {
  constructor(private page: Page) {}

  // 登录
  async login(user: typeof testUsers.alice) {
    await this.page.goto('/login')
    await this.page.fill('[data-testid="email-input"]', user.email)
    await this.page.fill('[data-testid="password-input"]', user.password)
    await this.page.click('[data-testid="login-button"]')
    await this.page.waitForURL('/chat')
  }

  // 导航到聊天页面
  async goToChat() {
    await this.page.goto('/chat')
    await this.page.waitForSelector('[data-testid="chat-window"]')
  }

  // 创建新会话
  async createSession(name: string, type: 'private' | 'group' = 'group') {
    await this.page.click('[data-testid="create-session-btn"]')
    await this.page.waitForSelector('[data-testid="create-session-dialog"]')
    
    await this.page.fill('[data-testid="session-name-input"]', name)
    await this.page.selectOption('[data-testid="session-type-select"]', type)
    
    await this.page.click('[data-testid="confirm-create-btn"]')
    await this.page.waitForSelector(`[data-testid="session-item-${name}"]`)
  }

  // 选择会话
  async selectSession(sessionName: string) {
    await this.page.click(`[data-testid="session-item-${sessionName}"]`)
    await this.page.waitForSelector('[data-testid="message-list"]')
  }

  // 发送消息
  async sendMessage(content: string) {
    await this.page.fill('[data-testid="message-input"]', content)
    await this.page.click('[data-testid="send-button"]')
    
    // 等待消息出现在列表中
    await this.page.waitForSelector(`[data-testid="message-item"]:has-text("${content}")`)
  }

  // 上传文件
  async uploadFile(filePath: string) {
    await this.page.setInputFiles('[data-testid="file-input"]', filePath)
    await this.page.waitForSelector('[data-testid="file-upload-progress"]')
    await this.page.waitForSelector('[data-testid="file-upload-success"]', { timeout: 10000 })
  }

  // 回复消息
  async replyToMessage(originalMessage: string, replyContent: string) {
    // 悬停在消息上显示操作按钮
    await this.page.hover(`[data-testid="message-item"]:has-text("${originalMessage}")`)
    await this.page.click(`[data-testid="message-item"]:has-text("${originalMessage}") [data-testid="reply-btn"]`)
    
    // 验证回复状态显示
    await this.page.waitForSelector('[data-testid="reply-indicator"]')
    
    // 发送回复
    await this.sendMessage(replyContent)
  }

  // 搜索消息
  async searchMessages(query: string) {
    await this.page.click('[data-testid="search-btn"]')
    await this.page.fill('[data-testid="search-input"]', query)
    await this.page.waitForSelector('[data-testid="search-results"]')
  }

  // 获取消息列表
  async getMessages() {
    return await this.page.locator('[data-testid="message-item"]').all()
  }

  // 获取会话列表
  async getSessions() {
    return await this.page.locator('[data-testid^="session-item-"]').all()
  }

  // 等待通知出现
  async waitForNotification(content: string) {
    await this.page.waitForSelector(`[data-testid="notification"]:has-text("${content}")`)
  }

  // 检查在线状态
  async checkUserOnlineStatus(userName: string) {
    return await this.page.locator(`[data-testid="user-${userName}"] [data-testid="online-indicator"]`).isVisible()
  }
}

test.describe('聊天模块端到端测试', () => {
  let chatPage: ChatPage

  test.beforeEach(async ({ page }) => {
    chatPage = new ChatPage(page)
    
    // 设置测试环境
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: testUsers.alice,
            token: 'mock-jwt-token'
          }
        })
      })
    })
    
    // Mock WebSocket 连接
    await page.addInitScript(() => {
      class MockWebSocket {
        static CONNECTING = 0
        static OPEN = 1
        static CLOSING = 2
        static CLOSED = 3
        
        readyState = MockWebSocket.OPEN
        onopen: ((event: Event) => void) | null = null
        onclose: ((event: CloseEvent) => void) | null = null
        onmessage: ((event: MessageEvent) => void) | null = null
        onerror: ((event: Event) => void) | null = null
        
        constructor(public url: string) {
          setTimeout(() => {
            this.onopen?.(new Event('open'))
          }, 100)
        }
        
        send(data: string) {
          // 模拟服务器响应
          setTimeout(() => {
            const message = JSON.parse(data)
            this.simulateResponse(message)
          }, 50)
        }
        
        close() {
          this.readyState = MockWebSocket.CLOSED
          this.onclose?.(new CloseEvent('close'))
        }
        
        private simulateResponse(message: any) {
          if (message.type === 'send_message') {
            this.onmessage?.(new MessageEvent('message', {
              data: JSON.stringify({
                type: 'message_sent',
                data: {
                  id: `msg_${Date.now()}`,
                  ...message.data,
                  timestamp: new Date().toISOString(),
                  status: 'sent'
                }
              })
            }))
          }
        }
      }
      
      (window as any).WebSocket = MockWebSocket
    })
  })

  test.describe('用户认证流程', () => {
    test('用户应该能够登录并进入聊天界面', async () => {
      await chatPage.login(testUsers.alice)
      
      // 验证聊天界面加载
      await expect(chatPage.page.locator('[data-testid="chat-window"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="session-list"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="user-info"]')).toContainText(testUsers.alice.name)
    })

    test('用户应该能够登出', async () => {
      await chatPage.login(testUsers.alice)
      
      await chatPage.page.click('[data-testid="user-menu"]')
      await chatPage.page.click('[data-testid="logout-btn"]')
      
      await expect(chatPage.page).toHaveURL('/login')
    })
  })

  test.describe('会话管理', () => {
    test.beforeEach(async () => {
      await chatPage.login(testUsers.alice)
    })

    test('应该能够创建新的群聊会话', async () => {
      const sessionName = 'Test Group Chat'
      await chatPage.createSession(sessionName, 'group')
      
      // 验证会话出现在列表中
      await expect(chatPage.page.locator(`[data-testid="session-item-${sessionName}"]`)).toBeVisible()
    })

    test('应该能够创建私聊会话', async () => {
      const sessionName = 'Private Chat with Bob'
      await chatPage.createSession(sessionName, 'private')
      
      await expect(chatPage.page.locator(`[data-testid="session-item-${sessionName}"]`)).toBeVisible()
    })

    test('应该能够搜索会话', async () => {
      // 先创建几个会话
      await chatPage.createSession('Work Discussion')
      await chatPage.createSession('Family Chat')
      await chatPage.createSession('Project Team')
      
      // 搜索会话
      await chatPage.page.fill('[data-testid="session-search-input"]', 'Work')
      
      // 验证搜索结果
      await expect(chatPage.page.locator('[data-testid="session-item-Work Discussion"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="session-item-Family Chat"]')).not.toBeVisible()
    })

    test('应该能够删除会话', async () => {
      const sessionName = 'To Be Deleted'
      await chatPage.createSession(sessionName)
      
      // 右键点击会话项
      await chatPage.page.click(`[data-testid="session-item-${sessionName}"]`, { button: 'right' })
      await chatPage.page.click('[data-testid="delete-session-btn"]')
      
      // 确认删除
      await chatPage.page.click('[data-testid="confirm-delete-btn"]')
      
      // 验证会话已删除
      await expect(chatPage.page.locator(`[data-testid="session-item-${sessionName}"]`)).not.toBeVisible()
    })
  })

  test.describe('消息发送和接收', () => {
    test.beforeEach(async () => {
      await chatPage.login(testUsers.alice)
      await chatPage.createSession('Test Chat')
      await chatPage.selectSession('Test Chat')
    })

    test('应该能够发送文本消息', async () => {
      const messageContent = 'Hello, this is a test message!'
      await chatPage.sendMessage(messageContent)
      
      // 验证消息出现在消息列表中
      await expect(chatPage.page.locator(`[data-testid="message-item"]:has-text("${messageContent}")`)).toBeVisible()
      
      // 验证消息状态
      await expect(chatPage.page.locator('[data-testid="message-status-sent"]')).toBeVisible()
    })

    test('应该能够发送多行消息', async () => {
      const multilineMessage = 'Line 1\nLine 2\nLine 3'
      await chatPage.page.fill('[data-testid="message-input"]', multilineMessage)
      await chatPage.page.press('[data-testid="message-input"]', 'Control+Enter')
      
      await expect(chatPage.page.locator(`[data-testid="message-item"]:has-text("Line 1")`)).toBeVisible()
      await expect(chatPage.page.locator(`[data-testid="message-item"]:has-text("Line 3")`)).toBeVisible()
    })

    test('应该能够发送表情符号', async () => {
      // 点击表情按钮
      await chatPage.page.click('[data-testid="emoji-btn"]')
      await chatPage.page.waitForSelector('[data-testid="emoji-picker"]')
      
      // 选择表情
      await chatPage.page.click('[data-testid="emoji-😀"]')
      
      // 发送消息
      await chatPage.page.click('[data-testid="send-button"]')
      
      await expect(chatPage.page.locator('[data-testid="message-item"]:has-text("😀")')).toBeVisible()
    })

    test('应该能够回复消息', async () => {
      const originalMessage = 'Original message'
      const replyMessage = 'This is a reply'
      
      // 发送原始消息
      await chatPage.sendMessage(originalMessage)
      
      // 回复消息
      await chatPage.replyToMessage(originalMessage, replyMessage)
      
      // 验证回复消息显示
      await expect(chatPage.page.locator(`[data-testid="message-item"]:has-text("${replyMessage}")`)).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="reply-reference"]')).toContainText(originalMessage)
    })

    test('应该能够编辑消息', async () => {
      const originalMessage = 'Original message'
      const editedMessage = 'Edited message'
      
      await chatPage.sendMessage(originalMessage)
      
      // 悬停并点击编辑按钮
      await chatPage.page.hover(`[data-testid="message-item"]:has-text("${originalMessage}")`)
      await chatPage.page.click(`[data-testid="message-item"]:has-text("${originalMessage}") [data-testid="edit-btn"]`)
      
      // 编辑消息
      await chatPage.page.fill('[data-testid="edit-input"]', editedMessage)
      await chatPage.page.click('[data-testid="save-edit-btn"]')
      
      // 验证消息已编辑
      await expect(chatPage.page.locator(`[data-testid="message-item"]:has-text("${editedMessage}")`)).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="edited-indicator"]')).toBeVisible()
    })

    test('应该能够删除消息', async () => {
      const messageToDelete = 'Message to be deleted'
      await chatPage.sendMessage(messageToDelete)
      
      // 悬停并点击删除按钮
      await chatPage.page.hover(`[data-testid="message-item"]:has-text("${messageToDelete}")`)
      await chatPage.page.click(`[data-testid="message-item"]:has-text("${messageToDelete}") [data-testid="delete-btn"]`)
      
      // 确认删除
      await chatPage.page.click('[data-testid="confirm-delete-btn"]')
      
      // 验证消息已删除
      await expect(chatPage.page.locator(`[data-testid="message-item"]:has-text("${messageToDelete}")`)).not.toBeVisible()
    })
  })

  test.describe('文件上传', () => {
    test.beforeEach(async () => {
      await chatPage.login(testUsers.alice)
      await chatPage.createSession('File Test Chat')
      await chatPage.selectSession('File Test Chat')
    })

    test('应该能够上传图片文件', async () => {
      // 创建测试图片文件
      const testImagePath = 'tests/fixtures/test-image.jpg'
      
      await chatPage.uploadFile(testImagePath)
      
      // 验证文件消息出现
      await expect(chatPage.page.locator('[data-testid="message-item-file"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="file-preview-image"]')).toBeVisible()
    })

    test('应该能够上传文档文件', async () => {
      const testDocPath = 'tests/fixtures/test-document.pdf'
      
      await chatPage.uploadFile(testDocPath)
      
      await expect(chatPage.page.locator('[data-testid="message-item-file"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="file-icon-pdf"]')).toBeVisible()
    })

    test('应该显示上传进度', async () => {
      const testFilePath = 'tests/fixtures/large-file.zip'
      
      // 开始上传
      await chatPage.page.setInputFiles('[data-testid="file-input"]', testFilePath)
      
      // 验证进度条显示
      await expect(chatPage.page.locator('[data-testid="upload-progress-bar"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="upload-percentage"]')).toBeVisible()
    })

    test('应该能够取消文件上传', async () => {
      const testFilePath = 'tests/fixtures/large-file.zip'
      
      await chatPage.page.setInputFiles('[data-testid="file-input"]', testFilePath)
      
      // 点击取消按钮
      await chatPage.page.click('[data-testid="cancel-upload-btn"]')
      
      // 验证上传已取消
      await expect(chatPage.page.locator('[data-testid="upload-cancelled"]')).toBeVisible()
    })
  })

  test.describe('消息搜索', () => {
    test.beforeEach(async () => {
      await chatPage.login(testUsers.alice)
      await chatPage.createSession('Search Test Chat')
      await chatPage.selectSession('Search Test Chat')
      
      // 发送一些测试消息
      await chatPage.sendMessage('Hello world')
      await chatPage.sendMessage('This is a test message')
      await chatPage.sendMessage('Another message here')
      await chatPage.sendMessage('Final message with keyword')
    })

    test('应该能够搜索消息内容', async () => {
      await chatPage.searchMessages('test')
      
      // 验证搜索结果
      await expect(chatPage.page.locator('[data-testid="search-result-item"]')).toHaveCount(1)
      await expect(chatPage.page.locator('[data-testid="search-result-item"]')).toContainText('test message')
    })

    test('应该高亮搜索关键词', async () => {
      await chatPage.searchMessages('message')
      
      // 验证关键词高亮
      await expect(chatPage.page.locator('[data-testid="search-highlight"]')).toHaveCount(3)
    })

    test('应该显示搜索结果数量', async () => {
      await chatPage.searchMessages('message')
      
      await expect(chatPage.page.locator('[data-testid="search-count"]')).toContainText('3 results')
    })

    test('应该能够清除搜索', async () => {
      await chatPage.searchMessages('test')
      
      // 点击清除按钮
      await chatPage.page.click('[data-testid="clear-search-btn"]')
      
      // 验证搜索已清除
      await expect(chatPage.page.locator('[data-testid="search-results"]')).not.toBeVisible()
      await expect(chatPage.page.locator('[data-testid="search-input"]')).toHaveValue('')
    })
  })

  test.describe('实时通信', () => {
    test('应该显示用户在线状态', async () => {
      await chatPage.login(testUsers.alice)
      
      // 验证当前用户在线状态
      await expect(chatPage.page.locator('[data-testid="current-user-status"]')).toHaveClass(/online/)
    })

    test('应该显示正在输入指示器', async () => {
      await chatPage.login(testUsers.alice)
      await chatPage.createSession('Typing Test')
      await chatPage.selectSession('Typing Test')
      
      // 开始输入
      await chatPage.page.fill('[data-testid="message-input"]', 'Typing...')
      
      // 模拟其他用户看到输入状态
      await chatPage.page.evaluate(() => {
        const ws = (window as any).mockWebSocket
        if (ws && ws.onmessage) {
          ws.onmessage(new MessageEvent('message', {
            data: JSON.stringify({
              type: 'user_typing',
              data: {
                userId: 'user2',
                sessionId: 'session1',
                isTyping: true
              }
            })
          }))
        }
      })
      
      // 验证输入指示器显示
      await expect(chatPage.page.locator('[data-testid="typing-indicator"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="typing-indicator"]')).toContainText('is typing')
    })
  })

  test.describe('通知系统', () => {
    test.beforeEach(async () => {
      await chatPage.login(testUsers.alice)
      
      // 请求通知权限
      await chatPage.page.evaluate(() => {
        Object.defineProperty(Notification, 'permission', {
          value: 'granted',
          writable: true
        })
      })
    })

    test('应该显示新消息通知', async () => {
      await chatPage.createSession('Notification Test')
      
      // 模拟接收来自其他会话的消息
      await chatPage.page.evaluate(() => {
        const ws = (window as any).mockWebSocket
        if (ws && ws.onmessage) {
          ws.onmessage(new MessageEvent('message', {
            data: JSON.stringify({
              type: 'new_message',
              data: {
                id: 'notification_msg',
                content: 'New message notification',
                senderId: 'user2',
                senderName: 'Bob',
                sessionId: 'other_session',
                timestamp: new Date().toISOString()
              }
            })
          }))
        }
      })
      
      // 验证通知显示
      await chatPage.waitForNotification('New message notification')
    })

    test('应该能够关闭通知', async () => {
      // 触发通知
      await chatPage.page.evaluate(() => {
        const event = new CustomEvent('show-notification', {
          detail: {
            title: 'Test Notification',
            content: 'This is a test notification'
          }
        })
        window.dispatchEvent(event)
      })
      
      await expect(chatPage.page.locator('[data-testid="notification"]')).toBeVisible()
      
      // 点击关闭按钮
      await chatPage.page.click('[data-testid="notification-close-btn"]')
      
      await expect(chatPage.page.locator('[data-testid="notification"]')).not.toBeVisible()
    })
  })

  test.describe('响应式设计', () => {
    test('应该在移动端正确显示', async () => {
      // 设置移动端视口
      await chatPage.page.setViewportSize({ width: 375, height: 667 })
      
      await chatPage.login(testUsers.alice)
      
      // 验证移动端布局
      await expect(chatPage.page.locator('[data-testid="chat-window"]')).toHaveClass(/mobile/)
      
      // 验证侧边栏默认隐藏
      await expect(chatPage.page.locator('[data-testid="sidebar"]')).not.toBeVisible()
      
      // 点击菜单按钮显示侧边栏
      await chatPage.page.click('[data-testid="mobile-menu-btn"]')
      await expect(chatPage.page.locator('[data-testid="sidebar"]')).toBeVisible()
    })

    test('应该在平板端正确显示', async () => {
      // 设置平板端视口
      await chatPage.page.setViewportSize({ width: 768, height: 1024 })
      
      await chatPage.login(testUsers.alice)
      
      // 验证平板端布局
      await expect(chatPage.page.locator('[data-testid="chat-window"]')).toHaveClass(/tablet/)
    })

    test('应该在桌面端正确显示', async () => {
      // 设置桌面端视口
      await chatPage.page.setViewportSize({ width: 1200, height: 800 })
      
      await chatPage.login(testUsers.alice)
      
      // 验证桌面端布局
      await expect(chatPage.page.locator('[data-testid="chat-window"]')).toHaveClass(/desktop/)
      
      // 验证侧边栏始终可见
      await expect(chatPage.page.locator('[data-testid="sidebar"]')).toBeVisible()
    })
  })

  test.describe('性能测试', () => {
    test('应该能够处理大量消息', async () => {
      await chatPage.login(testUsers.alice)
      await chatPage.createSession('Performance Test')
      await chatPage.selectSession('Performance Test')
      
      // 发送大量消息
      for (let i = 0; i < 100; i++) {
        await chatPage.sendMessage(`Message ${i + 1}`)
      }
      
      // 验证页面仍然响应
      await expect(chatPage.page.locator('[data-testid="message-list"]')).toBeVisible()
      
      // 验证虚拟滚动工作正常
      const visibleMessages = await chatPage.page.locator('[data-testid="message-item"]').count()
      expect(visibleMessages).toBeLessThan(100) // 应该只渲染可见的消息
    })

    test('应该快速加载会话列表', async () => {
      await chatPage.login(testUsers.alice)
      
      // 测量加载时间
      const startTime = Date.now()
      await chatPage.page.waitForSelector('[data-testid="session-list"]')
      const loadTime = Date.now() - startTime
      
      // 验证加载时间合理（小于2秒）
      expect(loadTime).toBeLessThan(2000)
    })
  })

  test.describe('错误处理', () => {
    test('应该处理网络连接错误', async () => {
      await chatPage.login(testUsers.alice)
      
      // 模拟网络断开
      await chatPage.page.evaluate(() => {
        const ws = (window as any).mockWebSocket
        if (ws && ws.onclose) {
          ws.onclose(new CloseEvent('close', { code: 1006, reason: 'Network error' }))
        }
      })
      
      // 验证错误提示显示
      await expect(chatPage.page.locator('[data-testid="connection-error"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="reconnect-btn"]')).toBeVisible()
    })

    test('应该能够重新连接', async () => {
      await chatPage.login(testUsers.alice)
      
      // 模拟连接断开
      await chatPage.page.evaluate(() => {
        const ws = (window as any).mockWebSocket
        if (ws && ws.onclose) {
          ws.onclose(new CloseEvent('close'))
        }
      })
      
      // 点击重连按钮
      await chatPage.page.click('[data-testid="reconnect-btn"]')
      
      // 验证重连成功
      await expect(chatPage.page.locator('[data-testid="connection-success"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="connection-error"]')).not.toBeVisible()
    })

    test('应该处理消息发送失败', async () => {
      await chatPage.login(testUsers.alice)
      await chatPage.createSession('Error Test')
      await chatPage.selectSession('Error Test')
      
      // 模拟发送失败
      await chatPage.page.route('**/api/messages', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Server error' })
        })
      })
      
      await chatPage.sendMessage('This message will fail')
      
      // 验证错误状态显示
      await expect(chatPage.page.locator('[data-testid="message-error"]')).toBeVisible()
      await expect(chatPage.page.locator('[data-testid="retry-send-btn"]')).toBeVisible()
    })
  })

  test.describe('无障碍访问', () => {
    test('应该支持键盘导航', async () => {
      await chatPage.login(testUsers.alice)
      await chatPage.createSession('Accessibility Test')
      
      // 使用 Tab 键导航
      await chatPage.page.keyboard.press('Tab')
      await chatPage.page.keyboard.press('Tab')
      await chatPage.page.keyboard.press('Enter')
      
      // 验证会话被选中
      await expect(chatPage.page.locator('[data-testid="session-item"]:focus')).toBeVisible()
    })

    test('应该有正确的 ARIA 标签', async () => {
      await chatPage.login(testUsers.alice)
      
      // 验证重要元素有 ARIA 标签
      await expect(chatPage.page.locator('[data-testid="message-list"]')).toHaveAttribute('role', 'log')
      await expect(chatPage.page.locator('[data-testid="message-input"]')).toHaveAttribute('aria-label')
      await expect(chatPage.page.locator('[data-testid="send-button"]')).toHaveAttribute('aria-label')
    })

    test('应该支持屏幕阅读器', async () => {
      await chatPage.login(testUsers.alice)
      await chatPage.createSession('Screen Reader Test')
      await chatPage.selectSession('Screen Reader Test')
      
      await chatPage.sendMessage('Test message for screen reader')
      
      // 验证消息有适当的 ARIA 属性
      await expect(chatPage.page.locator('[data-testid="message-item"]').first()).toHaveAttribute('aria-label')
    })
  })
})