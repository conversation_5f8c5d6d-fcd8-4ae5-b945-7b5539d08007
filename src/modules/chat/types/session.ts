/**
 * 会话相关类型定义
 */

import type { MessagePreview, SenderType } from './message'

// 会话类型枚举
export enum SessionType {
  PRESALE_CONSULTATION = 'presale_consultation', // 售前咨询
  AFTER_SALE_SERVICE = 'after_sale_service',     // 售后服务
  FRIEND_CHAT = 'friend_chat',                   // 好友聊天
  CUSTOMER_SERVICE = 'customer_service',         // 客服服务
  SYSTEM_NOTIFICATION = 'system_notification'    // 系统通知
}

// 会话状态枚举
export enum SessionStatus {
  ACTIVE = 'active',       // 活跃
  INACTIVE = 'inactive',   // 非活跃
  CLOSED = 'closed',       // 已关闭
  ARCHIVED = 'archived',   // 已归档
  BLOCKED = 'blocked'      // 已屏蔽
}

// 会话优先级枚举
export enum SessionPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 参与者信息
export interface SessionParticipant {
  id: number
  user_id: number
  user_type: SenderType
  name: string
  avatar?: string
  is_online: boolean
  last_seen?: string
  role?: string // 在会话中的角色，如 'owner', 'member', 'admin'
  joined_at: string
  left_at?: string
}

// 会话设置
export interface SessionSettings {
  mute_notifications: boolean
  auto_reply_enabled: boolean
  auto_reply_message?: string
  working_hours?: {
    enabled: boolean
    start_time: string // HH:mm
    end_time: string   // HH:mm
    timezone: string
    days: number[]     // 0-6, 0为周日
  }
  max_response_time?: number // 最大响应时间（分钟）
  tags: string[]
}

// 基础会话接口
export interface BaseSession {
  id: number
  type: SessionType
  status: SessionStatus
  priority: SessionPriority
  title?: string
  description?: string
  creator_id: number
  creator_type: SenderType
  participants: SessionParticipant[]
  last_message?: MessagePreview
  last_message_time?: string
  unread_count: number
  total_messages: number
  created_at: string
  updated_at: string
  closed_at?: string
  settings: SessionSettings
  metadata?: Record<string, any>
}

// 售前咨询会话
export interface PresaleConsultationSession extends BaseSession {
  type: SessionType.PRESALE_CONSULTATION
  merchant_id: number
  merchant_name: string
  product_id?: number
  product_name?: string
  customer_info: {
    id: number
    name: string
    avatar?: string
    phone?: string
    email?: string
    level?: string // VIP等级
    is_new_customer: boolean
    registration_date: string
  }
}

// 售后服务会话
export interface AfterSaleServiceSession extends BaseSession {
  type: SessionType.AFTER_SALE_SERVICE
  merchant_id: number
  merchant_name: string
  order_id: number
  order_number: string
  issue_type: 'refund' | 'exchange' | 'complaint' | 'technical_support' | 'other'
  issue_description: string
  customer_info: {
    id: number
    name: string
    avatar?: string
    phone?: string
    email?: string
    order_history_count: number
  }
}

// 好友聊天会话
export interface FriendChatSession extends BaseSession {
  type: SessionType.FRIEND_CHAT
  friend_info: {
    id: number
    name: string
    avatar?: string
    nickname?: string
    relationship: 'friend' | 'family' | 'colleague' | 'other'
    added_date: string
  }
}

// 客服服务会话
export interface CustomerServiceSession extends BaseSession {
  type: SessionType.CUSTOMER_SERVICE
  service_type: 'general' | 'technical' | 'billing' | 'complaint'
  assigned_agent?: {
    id: number
    name: string
    avatar?: string
    department: string
    specialties: string[]
  }
  queue_position?: number
  estimated_wait_time?: number
}

// 系统通知会话
export interface SystemNotificationSession extends BaseSession {
  type: SessionType.SYSTEM_NOTIFICATION
  notification_category: 'order' | 'payment' | 'promotion' | 'system' | 'security'
  is_broadcast: boolean
  target_users?: number[]
}

// 联合会话类型
export type Session = 
  | PresaleConsultationSession 
  | AfterSaleServiceSession 
  | FriendChatSession 
  | CustomerServiceSession 
  | SystemNotificationSession

// 创建会话请求
export interface CreateSessionRequest {
  type: SessionType
  title?: string
  description?: string
  receiver_id: number
  receiver_type: SenderType
  initial_message?: string
  metadata?: Record<string, any>
  // 特定类型的额外数据
  product_id?: number      // 售前咨询
  order_id?: number        // 售后服务
  issue_type?: string      // 售后服务
  service_type?: string    // 客服服务
}

// 创建会话响应
export interface CreateSessionResponse {
  success: boolean
  session: Session
  error?: string
}

// 获取会话列表请求
export interface GetSessionsRequest {
  type?: SessionType
  status?: SessionStatus
  priority?: SessionPriority
  participant_id?: number
  participant_type?: SenderType
  has_unread?: boolean
  page?: number
  page_size?: number
  sort_by?: 'created_at' | 'updated_at' | 'last_message_time' | 'priority'
  sort_order?: 'asc' | 'desc'
  search_keyword?: string
}

// 获取会话列表响应
export interface GetSessionsResponse {
  success: boolean
  data: {
    sessions: Session[]
    total: number
    page: number
    page_size: number
    has_more: boolean
    unread_total: number
  }
  error?: string
}

// 更新会话请求
export interface UpdateSessionRequest {
  session_id: number
  title?: string
  description?: string
  status?: SessionStatus
  priority?: SessionPriority
  settings?: Partial<SessionSettings>
  metadata?: Record<string, any>
}

// 更新会话响应
export interface UpdateSessionResponse {
  success: boolean
  session: Session
  error?: string
}

// 会话统计信息
export interface SessionStats {
  total_sessions: number
  active_sessions: number
  unread_sessions: number
  today_sessions: number
  session_types: Record<SessionType, number>
  session_statuses: Record<SessionStatus, number>
  average_response_time: number // 分钟
  customer_satisfaction: number // 0-5
}

// 会话搜索请求
export interface SearchSessionsRequest {
  keyword: string
  type?: SessionType
  status?: SessionStatus
  participant_name?: string
  date_range?: {
    start_date: string
    end_date: string
  }
  page?: number
  page_size?: number
}

// 会话搜索响应
export interface SearchSessionsResponse {
  success: boolean
  data: {
    sessions: Session[]
    total: number
    page: number
    page_size: number
    has_more: boolean
  }
  error?: string
}

// 会话操作日志
export interface SessionLog {
  id: number
  session_id: number
  action: 'created' | 'updated' | 'closed' | 'archived' | 'participant_added' | 'participant_removed' | 'settings_changed'
  operator_id: number
  operator_type: SenderType
  operator_name: string
  details: string
  metadata?: Record<string, any>
  created_at: string
}

// 会话邀请
export interface SessionInvitation {
  id: number
  session_id: number
  inviter_id: number
  inviter_type: SenderType
  inviter_name: string
  invitee_id: number
  invitee_type: SenderType
  invitee_name: string
  message?: string
  status: 'pending' | 'accepted' | 'declined' | 'expired'
  created_at: string
  responded_at?: string
  expires_at: string
}

// 会话转接
export interface SessionTransfer {
  id: number
  session_id: number
  from_user_id: number
  from_user_type: SenderType
  from_user_name: string
  to_user_id: number
  to_user_type: SenderType
  to_user_name: string
  reason?: string
  notes?: string
  status: 'pending' | 'accepted' | 'declined' | 'completed'
  created_at: string
  completed_at?: string
}