{"name": "o_mall_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.8.3", "clipboard": "^2.0.11", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.8", "grid-layout-plus": "^1.0.6", "localforage": "^1.10.0", "lodash": "^4.17.21", "pinia": "^3.0.1", "plus-pro-components": "^0.1.24", "qrcode": "^1.5.4", "sass": "^1.85.1", "vue": "^3.5.13", "vue-i18n": "^9.14.3", "vue-router": "^4.5.0", "vue3-json-viewer": "^2.3.0"}, "devDependencies": {"@pinia/testing": "^1.0.0", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "eslint": "^9.22.0", "eslint-plugin-vue": "^10.0.0", "express": "^5.1.0", "jsdom": "^26.0.0", "prettier": "^3.5.3", "typescript": "~5.7.2", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0", "vitest": "^3.0.8", "vue-draggable-next": "^2.2.1", "vue-tsc": "^2.2.4", "vuedraggable": "^4.1.0"}}