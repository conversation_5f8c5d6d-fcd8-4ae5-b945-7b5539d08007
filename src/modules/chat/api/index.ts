/**
 * 聊天API接口定义
 */

import type {
  SendMessageRequest,
  SendMessageResponse,
  GetMessagesRequest,
  GetMessagesResponse,
  MarkMessageReadRequest,
  MarkMessageReadResponse,
  SearchMessagesRequest,
  SearchMessagesResponse
} from '../types/message'

import type {
  CreateSessionRequest,
  CreateSessionResponse,
  GetSessionsRequest,
  GetSessionsResponse,
  UpdateSessionRequest,
  UpdateSessionResponse,
  SearchSessionsRequest,
  SearchSessionsResponse
} from '../types/session'

import type {
  SendNotificationRequest,
  SendNotificationResponse,
  GetNotificationsRequest,
  GetNotificationsResponse,
  MarkNotificationReadRequest,
  MarkNotificationReadResponse,
  UpdateNotificationSettingsRequest,
  UpdateNotificationSettingsResponse
} from '../types/notification'

import { API_CONFIG } from '../constants'
import { Logger } from '../utils/logger'

/**
 * API响应基础接口
 */
export interface BaseApiResponse {
  success: boolean
  error?: string
  message?: string
}

/**
 * API请求配置
 */
export interface ApiRequestConfig {
  timeout?: number
  retries?: number
  headers?: Record<string, string>
}

/**
 * 聊天API类
 */
export class ChatApi {
  private baseUrl: string
  private logger: Logger
  private defaultConfig: ApiRequestConfig
  private abortController: AbortController | null = null

  constructor(baseUrl: string, config?: ApiRequestConfig) {
    this.baseUrl = baseUrl
    this.logger = new Logger('ChatApi')
    this.defaultConfig = {
      timeout: API_CONFIG.TIMEOUT.DEFAULT,
      retries: 3,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      ...config
    }
  }

  // ==================== 消息相关API ====================

  /**
   * 发送消息
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    return this.request('POST', API_CONFIG.ENDPOINTS.MESSAGES, request)
  }

  /**
   * 获取消息列表
   */
  async getMessages(request: GetMessagesRequest): Promise<GetMessagesResponse> {
    const params = this.buildQueryParams(request)
    return this.request('GET', `${API_CONFIG.ENDPOINTS.MESSAGES}?${params}`)
  }

  /**
   * 标记消息已读
   */
  async markMessageRead(request: MarkMessageReadRequest): Promise<MarkMessageReadResponse> {
    return this.request('POST', `${API_CONFIG.ENDPOINTS.MESSAGES}/read`, request)
  }

  /**
   * 搜索消息
   */
  async searchMessages(request: SearchMessagesRequest): Promise<SearchMessagesResponse> {
    const params = this.buildQueryParams(request)
    return this.request('GET', `${API_CONFIG.ENDPOINTS.MESSAGES}/search?${params}`)
  }

  // 其他消息相关方法暂时移除，只保留基本功能

  // ==================== 会话相关API ====================

  /**
   * 创建会话
   */
  async createSession(request: CreateSessionRequest): Promise<CreateSessionResponse> {
    return this.request('POST', API_CONFIG.ENDPOINTS.SESSIONS, request)
  }

  /**
   * 获取会话列表
   */
  async getSessions(request: GetSessionsRequest): Promise<GetSessionsResponse> {
    const params = this.buildQueryParams(request)
    return this.request('GET', `${API_CONFIG.ENDPOINTS.SESSIONS}?${params}`)
  }

  /**
   * 更新会话
   */
  async updateSession(request: UpdateSessionRequest): Promise<UpdateSessionResponse> {
    const { session_id, ...updateData } = request
    return this.request('PUT', `${API_CONFIG.ENDPOINTS.SESSIONS}/${session_id}`, updateData)
  }

  /**
   * 搜索会话
   */
  async searchSessions(request: SearchSessionsRequest): Promise<SearchSessionsResponse> {
    const params = this.buildQueryParams(request)
    return this.request('GET', `${API_CONFIG.ENDPOINTS.SESSIONS}/search?${params}`)
  }

  // 其他会话相关方法暂时移除，只保留基本功能

  // ==================== 通知相关API ====================

  /**
   * 发送通知
   */
  async sendNotification(request: SendNotificationRequest): Promise<SendNotificationResponse> {
    return this.request('POST', API_CONFIG.ENDPOINTS.NOTIFICATIONS, request)
  }

  /**
   * 获取通知列表
   */
  async getNotifications(request: GetNotificationsRequest): Promise<GetNotificationsResponse> {
    const params = this.buildQueryParams(request)
    return this.request('GET', `${API_CONFIG.ENDPOINTS.NOTIFICATIONS}?${params}`)
  }

  /**
   * 标记通知已读
   */
  async markNotificationRead(request: MarkNotificationReadRequest): Promise<MarkNotificationReadResponse> {
    return this.request('POST', `${API_CONFIG.ENDPOINTS.NOTIFICATIONS}/read`, request)
  }

  /**
   * 更新通知设置
   */
  async updateNotificationSettings(request: UpdateNotificationSettingsRequest): Promise<UpdateNotificationSettingsResponse> {
    return this.request('PUT', `${API_CONFIG.ENDPOINTS.NOTIFICATION_SETTINGS}`, request)
  }

  // ==================== 工具方法 ====================

  /**
   * 通用请求方法
   */
  private async request<T = any>(
    method: string,
    endpoint: string,
    data?: any,
    config?: Partial<ApiRequestConfig>
  ): Promise<T> {
    const requestConfig = { ...this.defaultConfig, ...config }
    const url = `${this.baseUrl}${endpoint}`

    this.logger.debug(`API Request: ${method} ${url}`, data)

    // 创建AbortController
    this.abortController = new AbortController()

    const requestOptions: RequestInit = {
      method,
      headers: requestConfig.headers,
      signal: this.abortController.signal
    }

    if (data && method !== 'GET') {
      if (data instanceof FormData) {
        requestOptions.body = data
        // 删除Content-Type，让浏览器自动设置
        if (requestOptions.headers && typeof requestOptions.headers === 'object') {
          const headers = requestOptions.headers as Record<string, string>
          delete headers['Content-Type']
        }
      } else {
        requestOptions.body = JSON.stringify(data)
      }
    }

    // 设置超时
    const timeoutId = setTimeout(() => {
      if (this.abortController) {
        this.abortController.abort()
      }
    }, requestConfig.timeout)

    try {
      const response = await fetch(url, requestOptions)
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentType = response.headers.get('content-type')
      let result: T

      if (contentType && contentType.includes('application/json')) {
        result = await response.json()
      } else {
        result = await response.blob() as unknown as T
      }

      this.logger.debug(`API Response: ${method} ${url}`, result)
      return result

    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout or cancelled')
      }

      this.logger.error(`API Error: ${method} ${url}`, error)
      throw error
    }
  }

  /**
   * 构建查询参数
   */
  private buildQueryParams(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, item.toString()))
        } else {
          searchParams.append(key, value.toString())
        }
      }
    })

    return searchParams.toString()
  }

  /**
   * 取消请求
   */
  cancelRequest(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }

  /**
   * 设置基础URL
   */
  setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl
    this.logger.debug('Base URL updated:', baseUrl)
  }

  /**
   * 设置默认请求头
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultConfig.headers = { ...this.defaultConfig.headers, ...headers }
    this.logger.debug('Default headers updated:', this.defaultConfig.headers)
  }

  /**
   * 获取配置
   */
  getConfig(): ApiRequestConfig {
    return { ...this.defaultConfig }
  }

  /**
   * 销毁API实例
   */
  destroy(): void {
    this.cancelRequest()
    this.logger.info('ChatApi destroyed')
  }
}

// 创建默认API实例
let defaultApiInstance: ChatApi | null = null

/**
 * 获取默认API实例
 */
export function getChatApi(baseUrl?: string, config?: ApiRequestConfig): ChatApi {
  if (!defaultApiInstance || baseUrl) {
    defaultApiInstance = new ChatApi(baseUrl || '', config)
  }
  return defaultApiInstance
}

/**
 * 销毁默认API实例
 */
export function destroyChatApi(): void {
  if (defaultApiInstance) {
    defaultApiInstance.destroy()
    defaultApiInstance = null
  }
}

export default ChatApi