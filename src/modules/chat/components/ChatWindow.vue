<template>
  <div class="chat-window" :class="{ 'chat-window--minimized': isMinimized }">
    <!-- 聊天窗口头部 -->
    <div class="chat-window__header">
      <div class="chat-window__header-left">
        <div class="chat-window__avatar">
          <img
            v-if="getSessionAvatar(currentSession)"
            :src="getSessionAvatar(currentSession)"
            :alt="getSessionName(currentSession)"
            class="chat-window__avatar-img"
          />
          <div v-else class="chat-window__avatar-placeholder">
            {{ getAvatarText(getSessionName(currentSession)) }}
          </div>
        </div>
        <div class="chat-window__info">
          <h3 class="chat-window__title">
            {{ getSessionName(currentSession) || '选择会话' }}
          </h3>
          <p class="chat-window__status">
            <span
              class="chat-window__status-dot"
              :class="`chat-window__status-dot--${getSessionStatus()}`"
            ></span>
            {{ getSessionStatusText() }}
          </p>
        </div>
      </div>
      
      <div class="chat-window__header-right">
        <!-- 会话操作按钮 -->
        <button
          v-if="currentSession"
          class="chat-window__action-btn"
          @click="showSessionInfo = !showSessionInfo"
          title="会话信息"
        >
          <svg class="chat-window__icon" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
          </svg>
        </button>
        
        <!-- 最小化/最大化按钮 -->
        <button
          class="chat-window__action-btn"
          @click="toggleMinimize"
          :title="isMinimized ? '展开' : '最小化'"
        >
          <svg class="chat-window__icon" viewBox="0 0 24 24">
            <path v-if="isMinimized" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            <path v-else d="M19 13H5v-2h14v2z"/>
          </svg>
        </button>
        
        <!-- 关闭按钮 -->
        <button
          class="chat-window__action-btn chat-window__action-btn--close"
          @click="$emit('close')"
          title="关闭"
        >
          <svg class="chat-window__icon" viewBox="0 0 24 24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 聊天窗口主体 -->
    <div v-show="!isMinimized" class="chat-window__body">
      <div class="chat-window__main">
        <!-- 会话列表 -->
        <div v-if="showSessionList" class="chat-window__sidebar">
          <SessionList
            @session-select="handleSessionSelect"
            @session-create="handleSessionCreate"
          />
        </div>
        
        <!-- 聊天区域 -->
        <div class="chat-window__content">
          <div v-if="!currentSession" class="chat-window__empty">
            <div class="chat-window__empty-icon">
              <svg viewBox="0 0 24 24">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
              </svg>
            </div>
            <h3>开始聊天</h3>
            <p>选择一个会话或创建新的会话开始聊天</p>
            <button class="chat-window__start-btn" @click="handleSessionCreate">
              创建新会话
            </button>
          </div>
          
          <div v-else class="chat-window__chat">
            <!-- 消息列表 -->
            <MessageList
              ref="messageListRef"
              :session-id="currentSession.id"
              @message-reply="handleMessageReply"
              @message-forward="handleMessageForward"
              @message-delete="handleMessageDelete"
            />
            
            <!-- 输入区域 -->
            <MessageInput
              ref="messageInputRef"
              :session-id="currentSession.id"
              :reply-message="replyMessage"
              @message-send="handleMessageSend"
              @reply-cancel="replyMessage = null"
              @file-upload="handleFileUpload"
            />
          </div>
        </div>
        
        <!-- 会话信息面板 -->
        <div v-if="showSessionInfo && currentSession" class="chat-window__info-panel">
          <SessionInfo
            :session="currentSession"
            @session-update="handleSessionUpdate"
            @session-close="handleSessionClose"
          />
        </div>
      </div>
    </div>
    
    <!-- 通知区域 -->
    <NotificationToast />
    
    <!-- 文件上传进度 -->
    <FileUploadProgress v-if="showUploadProgress" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import {
  useChatStore,
  useSessionStore,
  useFileStore
} from '../stores'
import type { Session, Message } from '../types'
import { SessionType, SessionStatus } from '../types'
import SessionList from './SessionList.vue'
import MessageList from './MessageList.vue'
import MessageInput from './MessageInput.vue'
import SessionInfo from './SessionInfo.vue'
import NotificationToast from './NotificationToast.vue'
import FileUploadProgress from './FileUploadProgress.vue'

// Props
interface Props {
  showSessionList?: boolean
  defaultMinimized?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showSessionList: true,
  defaultMinimized: false
})

// Emits
interface Emits {
  close: []
  sessionSelect: [session: Session]
  messageSend: [message: Message]
}

const emit = defineEmits<Emits>()

// Stores
const chatStore = useChatStore()
const sessionStore = useSessionStore()
const fileStore = useFileStore()

// Store refs
const { isConnected } = storeToRefs(chatStore)
const { sessions, currentSessionId } = storeToRefs(sessionStore)
const { uploadQueue } = storeToRefs(fileStore)

// Computed
const currentSession = computed(() => {
  if (!currentSessionId.value) return null
  return sessions.value.find(s => s.id.toString() === currentSessionId.value) || null
})

// Refs
const messageListRef = ref<InstanceType<typeof MessageList>>()
const messageInputRef = ref<InstanceType<typeof MessageInput>>()

// State
const isMinimized = ref(props.defaultMinimized)
const showSessionInfo = ref(false)
const replyMessage = ref<Message | null>(null)

// Computed
const showUploadProgress = computed(() => uploadQueue.value.length > 0)

// Methods
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
}

const getAvatarText = (name?: string) => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

const getSessionName = (session: Session | null) => {
  if (!session) return ''
  switch (session.type) {
    case SessionType.PRESALE_CONSULTATION:
      return session.product_name || '售前咨询'
    case SessionType.AFTER_SALE_SERVICE:
      return session.order_id ? `订单 ${session.order_id}` : '售后服务'
    case SessionType.FRIEND_CHAT:
      return session.friend_info?.name || '好友聊天'
    case SessionType.CUSTOMER_SERVICE:
      return '客服咨询'
    default:
      return '会话'
  }
}

const getSessionAvatar = (session: Session | null) => {
  if (!session) return ''
  switch (session.type) {
    case SessionType.PRESALE_CONSULTATION:
      return session.customer_info?.avatar || ''
    case SessionType.AFTER_SALE_SERVICE:
      return session.customer_info?.avatar || ''
    case SessionType.FRIEND_CHAT:
      return session.friend_info?.avatar || ''
    case SessionType.CUSTOMER_SERVICE:
      return '' // CustomerServiceSession 没有 service_info 属性
    default:
      return ''
  }
}

const getSessionStatus = () => {
  if (!isConnected.value) return 'offline'
  if (!currentSession.value) return 'idle'
  
  switch (currentSession.value.status) {
    case SessionStatus.ACTIVE:
      return 'online'
    case SessionStatus.INACTIVE:
      return 'away'
    case SessionStatus.CLOSED:
      return 'offline'
    default:
      return 'idle'
  }
}

const getSessionStatusText = () => {
  if (!isConnected.value) return '连接已断开'
  if (!currentSession.value) return '未选择会话'
  
  switch (currentSession.value.status) {
    case SessionStatus.ACTIVE:
      return '在线'
    case SessionStatus.INACTIVE:
      return '等待中'
    case SessionStatus.CLOSED:
      return '已关闭'
    default:
      return '空闲'
  }
}

const handleSessionSelect = (session: Session) => {
  sessionStore.currentSessionId = session.id.toString()
  showSessionInfo.value = false
  emit('sessionSelect', session)
}

const handleSessionCreate = async () => {
  try {
    // 创建会话的逻辑暂时简化
    console.log('创建新会话')
  } catch (error) {
    console.error('创建会话失败:', error)
  }
}

const handleSessionUpdate = (session: Session) => {
  // 更新会话的逻辑暂时简化
  console.log('更新会话:', session)
}

const handleSessionClose = async () => {
  const currentSessionId = sessionStore.currentSessionId
  if (!currentSessionId) return

  try {
    // 关闭会话的逻辑暂时简化
    sessionStore.currentSessionId = null
    showSessionInfo.value = false
  } catch (error) {
    console.error('关闭会话失败:', error)
  }
}

const handleMessageSend = (message: Message) => {
  emit('messageSend', message)
  
  // 清除回复消息
  replyMessage.value = null
  
  // 滚动到底部
  nextTick(() => {
    // 滚动到底部的逻辑暂时简化
    console.log('滚动到底部')
  })
}

const handleMessageReply = (message: Message) => {
  replyMessage.value = message
  
  // 聚焦到输入框
  nextTick(() => {
    // 聚焦输入框的逻辑暂时简化
    console.log('聚焦输入框')
  })
}

const handleMessageForward = (message: Message) => {
  // TODO: 实现消息转发功能
  console.log('转发消息:', message)
}

const handleMessageDelete = async (message: Message) => {
  try {
    // 删除消息的逻辑暂时简化
    console.log('删除消息:', message)
  } catch (error) {
    console.error('删除消息失败:', error)
  }
}

const handleFileUpload = async (files: File[]) => {
  try {
    // 文件上传的逻辑暂时简化
    console.log('上传文件:', files)
    
    // 文件上传成功后的处理逻辑暂时简化
  } catch (error) {
    console.error('文件上传失败:', error)
  }
}

// Lifecycle
onMounted(async () => {
  // 初始化聊天客户端的逻辑暂时简化
  try {
    console.log('初始化聊天客户端')
  } catch (error) {
    console.error('初始化聊天失败:', error)
  }
})

onUnmounted(() => {
  // 断开聊天连接的逻辑暂时简化
  console.log('断开聊天连接')
})

// Watch
watch(
  () => sessionStore.currentSessionId,
  (sessionId) => {
    if (sessionId) {
      // 加载会话消息的逻辑暂时简化
      console.log('切换到会话:', sessionId)
    }
  },
  { immediate: true }
)

watch(
  () => isMinimized.value,
  (minimized) => {
    if (!minimized && sessionStore.currentSessionId) {
      // 展开时滚动到底部
      nextTick(() => {
        // 滚动到底部的逻辑暂时简化
        console.log('滚动到底部')
      })
    }
  }
)
</script>

<style scoped>
.chat-window {
  @apply flex flex-col bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden;
  width: 800px;
  height: 600px;
  max-width: 90vw;
  max-height: 90vh;
}

.chat-window--minimized {
  height: 60px;
}

.chat-window__header {
  @apply flex items-center justify-between px-4 py-3 bg-gray-50 border-b border-gray-200;
}

.chat-window__header-left {
  @apply flex items-center space-x-3;
}

.chat-window__avatar {
  @apply w-10 h-10 rounded-full overflow-hidden;
}

.chat-window__avatar-img {
  @apply w-full h-full object-cover;
}

.chat-window__avatar-placeholder {
  @apply w-full h-full bg-blue-500 text-white flex items-center justify-center font-semibold;
}

.chat-window__info {
  @apply flex flex-col;
}

.chat-window__title {
  @apply text-sm font-semibold text-gray-900 m-0;
}

.chat-window__status {
  @apply flex items-center text-xs text-gray-500 m-0;
}

.chat-window__status-dot {
  @apply w-2 h-2 rounded-full mr-1;
}

.chat-window__status-dot--online {
  @apply bg-green-500;
}

.chat-window__status-dot--away {
  @apply bg-yellow-500;
}

.chat-window__status-dot--offline {
  @apply bg-gray-400;
}

.chat-window__status-dot--idle {
  @apply bg-blue-500;
}

.chat-window__header-right {
  @apply flex items-center space-x-2;
}

.chat-window__action-btn {
  @apply p-2 rounded-md hover:bg-gray-200 transition-colors;
}

.chat-window__action-btn--close {
  @apply hover:bg-red-100 hover:text-red-600;
}

.chat-window__icon {
  @apply w-4 h-4 fill-current;
}

.chat-window__body {
  @apply flex-1 flex flex-col overflow-hidden;
}

.chat-window__main {
  @apply flex-1 flex overflow-hidden;
}

.chat-window__sidebar {
  @apply w-80 border-r border-gray-200 bg-gray-50;
}

.chat-window__content {
  @apply flex-1 flex flex-col;
}

.chat-window__empty {
  @apply flex-1 flex flex-col items-center justify-center text-center p-8;
}

.chat-window__empty-icon {
  @apply w-16 h-16 text-gray-400 mb-4;
}

.chat-window__empty-icon svg {
  @apply w-full h-full fill-current;
}

.chat-window__empty h3 {
  @apply text-lg font-semibold text-gray-900 mb-2;
}

.chat-window__empty p {
  @apply text-gray-500 mb-6;
}

.chat-window__start-btn {
  @apply px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors;
}

.chat-window__chat {
  @apply flex-1 flex flex-col;
}

.chat-window__info-panel {
  @apply w-80 border-l border-gray-200 bg-gray-50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-window {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .chat-window__sidebar,
  .chat-window__info-panel {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 10;
  }
}
</style>