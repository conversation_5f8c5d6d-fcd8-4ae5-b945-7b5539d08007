/**
 * MessageInput 组件单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import MessageInput from '../../../components/MessageInput.vue'
import { useMessageStore } from '../../../stores/message'
import { useSessionStore } from '../../../stores/session'
import { useFileStore } from '../../../stores/file'
import { useChatStore } from '../../../stores/chat'

// Mock 组件
vi.mock('@/components/ui/Icon.vue', () => ({
  default: {
    name: 'Icon',
    template: '<span class="icon">{{ name }}</span>',
    props: ['name']
  }
}))

vi.mock('@/components/ui/Button.vue', () => ({
  default: {
    name: 'Button',
    template: '<button class="btn" :class="type" :disabled="disabled"><slot /></button>',
    props: ['type', 'disabled']
  }
}))

vi.mock('@/components/ui/Tooltip.vue', () => ({
  default: {
    name: 'Tooltip',
    template: '<div class="tooltip"><slot /></div>',
    props: ['content']
  }
}))

// Mock 表情选择器
vi.mock('@/components/ui/EmojiPicker.vue', () => ({
  default: {
    name: 'EmojiPicker',
    template: '<div class="emoji-picker"><button @click="$emit(\'select\', \'😀\')">😀</button></div>',
    emits: ['select']
  }
}))

describe('MessageInput', () => {
  let wrapper: any
  let messageStore: any
  let sessionStore: any
  let fileStore: any
  let chatStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    
    messageStore = useMessageStore()
    sessionStore = useSessionStore()
    fileStore = useFileStore()
    chatStore = useChatStore()
    
    // 设置 mock 数据
    sessionStore.currentSessionId = 'session1'
    messageStore.replyingTo = null
    messageStore.editingMessage = null
    chatStore.currentUser = {
      id: 'user1',
      name: 'Test User',
      avatar: 'avatar.jpg'
    }
    
    // Mock store 方法
    vi.spyOn(messageStore, 'sendMessage').mockResolvedValue({
      id: 'msg1',
      content: 'Test message',
      senderId: 'user1',
      sessionId: 'session1',
      timestamp: new Date(),
      type: 'text',
      status: 'sent'
    })
    vi.spyOn(messageStore, 'editMessage').mockResolvedValue(undefined)
    vi.spyOn(messageStore, 'clearReplyingTo').mockImplementation(() => {})
    vi.spyOn(messageStore, 'clearEditingMessage').mockImplementation(() => {})
    vi.spyOn(fileStore, 'addFilesToUploadQueue').mockImplementation(() => {})
    vi.spyOn(fileStore, 'startAllUploads').mockResolvedValue(undefined)
    
    wrapper = mount(MessageInput, {
      global: {
        plugins: [createPinia()]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染输入框结构', () => {
      expect(wrapper.find('.message-input').exists()).toBe(true)
      expect(wrapper.find('.input-container').exists()).toBe(true)
      expect(wrapper.find('.input-actions').exists()).toBe(true)
    })

    it('应该渲染文本输入区域', () => {
      const textarea = wrapper.find('.message-textarea')
      expect(textarea.exists()).toBe(true)
      expect(textarea.attributes('placeholder')).toContain('输入消息')
    })

    it('应该渲染操作按钮', () => {
      expect(wrapper.find('.emoji-btn').exists()).toBe(true)
      expect(wrapper.find('.file-btn').exists()).toBe(true)
      expect(wrapper.find('.send-btn').exists()).toBe(true)
    })

    it('应该渲染文件输入', () => {
      const fileInput = wrapper.find('input[type="file"]')
      expect(fileInput.exists()).toBe(true)
      expect(fileInput.attributes('multiple')).toBeDefined()
    })
  })

  describe('消息输入', () => {
    it('应该能够输入文本', async () => {
      const textarea = wrapper.find('.message-textarea')
      await textarea.setValue('Hello world')
      
      expect(wrapper.vm.messageContent).toBe('Hello world')
    })

    it('应该支持多行文本', async () => {
      const textarea = wrapper.find('.message-textarea')
      const multilineText = 'Line 1\nLine 2\nLine 3'
      await textarea.setValue(multilineText)
      
      expect(wrapper.vm.messageContent).toBe(multilineText)
    })

    it('应该自动调整输入框高度', async () => {
      const textarea = wrapper.find('.message-textarea')
      const longText = 'A'.repeat(200)
      await textarea.setValue(longText)
      
      expect(wrapper.vm.textareaHeight).toBeGreaterThan(wrapper.vm.minHeight)
    })

    it('应该限制最大高度', async () => {
      const textarea = wrapper.find('.message-textarea')
      const veryLongText = 'A'.repeat(1000)
      await textarea.setValue(veryLongText)
      
      expect(wrapper.vm.textareaHeight).toBeLessThanOrEqual(wrapper.vm.maxHeight)
    })
  })

  describe('消息发送', () => {
    it('应该能够发送文本消息', async () => {
      const textarea = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await textarea.setValue('Test message')
      await sendBtn.trigger('click')
      
      expect(messageStore.sendMessage).toHaveBeenCalledWith(
        'Test message',
        'session1'
      )
      expect(wrapper.vm.messageContent).toBe('')
    })

    it('应该支持 Enter 键发送', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      await textarea.setValue('Test message')
      await textarea.trigger('keydown', { key: 'Enter' })
      
      expect(messageStore.sendMessage).toHaveBeenCalledWith(
        'Test message',
        'session1'
      )
    })

    it('应该支持 Ctrl+Enter 换行', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      await textarea.setValue('Line 1')
      await textarea.trigger('keydown', { key: 'Enter', ctrlKey: true })
      
      expect(wrapper.vm.messageContent).toBe('Line 1\n')
      expect(messageStore.sendMessage).not.toHaveBeenCalled()
    })

    it('应该阻止发送空消息', async () => {
      const sendBtn = wrapper.find('.send-btn')
      
      await sendBtn.trigger('click')
      
      expect(messageStore.sendMessage).not.toHaveBeenCalled()
    })

    it('应该阻止发送只有空格的消息', async () => {
      const textarea = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await textarea.setValue('   ')
      await sendBtn.trigger('click')
      
      expect(messageStore.sendMessage).not.toHaveBeenCalled()
    })

    it('应该在发送时禁用发送按钮', async () => {
      const textarea = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await textarea.setValue('Test message')
      
      // 模拟发送中状态
      wrapper.vm.isSending = true
      await wrapper.vm.$nextTick()
      
      expect(sendBtn.attributes('disabled')).toBeDefined()
    })
  })

  describe('回复功能', () => {
    it('应该显示回复信息', async () => {
      messageStore.replyingTo = {
        id: 'msg1',
        content: 'Original message',
        senderName: 'Alice'
      }
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.reply-info').exists()).toBe(true)
      expect(wrapper.find('.reply-content').text()).toBe('Original message')
      expect(wrapper.find('.reply-sender').text()).toBe('Alice')
    })

    it('应该能够取消回复', async () => {
      messageStore.replyingTo = {
        id: 'msg1',
        content: 'Original message',
        senderName: 'Alice'
      }
      await wrapper.vm.$nextTick()
      
      const cancelBtn = wrapper.find('.cancel-reply-btn')
      await cancelBtn.trigger('click')
      
      expect(messageStore.clearReplyingTo).toHaveBeenCalled()
    })

    it('应该发送回复消息', async () => {
      messageStore.replyingTo = {
        id: 'msg1',
        content: 'Original message',
        senderName: 'Alice'
      }
      
      const textarea = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await textarea.setValue('Reply message')
      await sendBtn.trigger('click')
      
      expect(messageStore.sendMessage).toHaveBeenCalledWith(
        'Reply message',
        'session1',
        { replyTo: 'msg1' }
      )
      expect(messageStore.clearReplyingTo).toHaveBeenCalled()
    })
  })

  describe('编辑功能', () => {
    it('应该显示编辑状态', async () => {
      messageStore.editingMessage = {
        id: 'msg1',
        content: 'Original message',
        senderId: 'user1',
        sessionId: 'session1',
        timestamp: new Date(),
        type: 'text',
        status: 'sent'
      }
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.edit-info').exists()).toBe(true)
      expect(wrapper.vm.messageContent).toBe('Original message')
    })

    it('应该能够取消编辑', async () => {
      messageStore.editingMessage = {
        id: 'msg1',
        content: 'Original message',
        senderId: 'user1',
        sessionId: 'session1',
        timestamp: new Date(),
        type: 'text',
        status: 'sent'
      }
      await wrapper.vm.$nextTick()
      
      const cancelBtn = wrapper.find('.cancel-edit-btn')
      await cancelBtn.trigger('click')
      
      expect(messageStore.clearEditingMessage).toHaveBeenCalled()
      expect(wrapper.vm.messageContent).toBe('')
    })

    it('应该保存编辑的消息', async () => {
      messageStore.editingMessage = {
        id: 'msg1',
        content: 'Original message',
        senderId: 'user1',
        sessionId: 'session1',
        timestamp: new Date(),
        type: 'text',
        status: 'sent'
      }
      
      const textarea = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await textarea.setValue('Edited message')
      await sendBtn.trigger('click')
      
      expect(messageStore.editMessage).toHaveBeenCalledWith(
        'msg1',
        'Edited message'
      )
      expect(messageStore.clearEditingMessage).toHaveBeenCalled()
    })
  })

  describe('表情功能', () => {
    it('应该显示表情选择器', async () => {
      const emojiBtn = wrapper.find('.emoji-btn')
      await emojiBtn.trigger('click')
      
      expect(wrapper.find('.emoji-picker').exists()).toBe(true)
    })

    it('应该能够插入表情', async () => {
      const emojiBtn = wrapper.find('.emoji-btn')
      await emojiBtn.trigger('click')
      
      const emojiPicker = wrapper.findComponent({ name: 'EmojiPicker' })
      await emojiPicker.vm.$emit('select', '😀')
      
      expect(wrapper.vm.messageContent).toBe('😀')
    })

    it('应该在光标位置插入表情', async () => {
      const textarea = wrapper.find('.message-textarea')
      await textarea.setValue('Hello world')
      
      // 模拟光标位置
      wrapper.vm.cursorPosition = 5
      
      const emojiBtn = wrapper.find('.emoji-btn')
      await emojiBtn.trigger('click')
      
      const emojiPicker = wrapper.findComponent({ name: 'EmojiPicker' })
      await emojiPicker.vm.$emit('select', '😀')
      
      expect(wrapper.vm.messageContent).toBe('Hello😀 world')
    })

    it('应该关闭表情选择器', async () => {
      wrapper.vm.showEmojiPicker = true
      await wrapper.vm.$nextTick()
      
      // 点击外部区域
      document.dispatchEvent(new Event('click'))
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.showEmojiPicker).toBe(false)
    })
  })

  describe('文件上传', () => {
    it('应该能够选择文件', async () => {
      const fileInput = wrapper.find('input[type="file"]')
      const files = [
        new File(['test'], 'test.txt', { type: 'text/plain' })
      ]
      
      Object.defineProperty(fileInput.element, 'files', {
        value: files,
        writable: false
      })
      
      await fileInput.trigger('change')
      
      expect(fileStore.addFilesToUploadQueue).toHaveBeenCalledWith(files)
    })

    it('应该支持拖拽上传', async () => {
      const dropZone = wrapper.find('.message-input')
      const files = [
        new File(['test'], 'test.txt', { type: 'text/plain' })
      ]
      
      const dragEvent = {
        preventDefault: vi.fn(),
        stopPropagation: vi.fn(),
        dataTransfer: { files }
      }
      
      await dropZone.trigger('drop', dragEvent)
      
      expect(dragEvent.preventDefault).toHaveBeenCalled()
      expect(fileStore.addFilesToUploadQueue).toHaveBeenCalledWith(files)
    })

    it('应该显示拖拽状态', async () => {
      const dropZone = wrapper.find('.message-input')
      
      await dropZone.trigger('dragenter')
      expect(wrapper.vm.isDragOver).toBe(true)
      expect(wrapper.classes()).toContain('drag-over')
      
      await dropZone.trigger('dragleave')
      expect(wrapper.vm.isDragOver).toBe(false)
    })

    it('应该验证文件类型', async () => {
      const fileInput = wrapper.find('input[type="file"]')
      const invalidFile = new File(['test'], 'test.exe', { type: 'application/exe' })
      
      vi.spyOn(fileStore, 'validateFile').mockReturnValue({
        isValid: false,
        error: 'Invalid file type'
      })
      
      Object.defineProperty(fileInput.element, 'files', {
        value: [invalidFile],
        writable: false
      })
      
      await fileInput.trigger('change')
      
      expect(wrapper.vm.error).toBe('Invalid file type')
    })

    it('应该验证文件大小', async () => {
      const fileInput = wrapper.find('input[type="file"]')
      const largeFile = new File(['x'.repeat(10000000)], 'large.txt', { type: 'text/plain' })
      
      vi.spyOn(fileStore, 'validateFile').mockReturnValue({
        isValid: false,
        error: 'File too large'
      })
      
      Object.defineProperty(fileInput.element, 'files', {
        value: [largeFile],
        writable: false
      })
      
      await fileInput.trigger('change')
      
      expect(wrapper.vm.error).toBe('File too large')
    })
  })

  describe('快捷键', () => {
    it('应该支持 @ 提及用户', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      await textarea.setValue('@')
      await textarea.trigger('input')
      
      expect(wrapper.find('.mention-popup').exists()).toBe(true)
    })

    it('应该支持 / 命令', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      await textarea.setValue('/')
      await textarea.trigger('input')
      
      expect(wrapper.find('.command-popup').exists()).toBe(true)
    })

    it('应该支持 Shift+Enter 换行', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      await textarea.setValue('Line 1')
      await textarea.trigger('keydown', { key: 'Enter', shiftKey: true })
      
      expect(wrapper.vm.messageContent).toBe('Line 1\n')
      expect(messageStore.sendMessage).not.toHaveBeenCalled()
    })

    it('应该支持 Escape 取消操作', async () => {
      messageStore.replyingTo = {
        id: 'msg1',
        content: 'Original message',
        senderName: 'Alice'
      }
      
      const textarea = wrapper.find('.message-textarea')
      await textarea.trigger('keydown', { key: 'Escape' })
      
      expect(messageStore.clearReplyingTo).toHaveBeenCalled()
    })
  })

  describe('输入状态', () => {
    it('应该显示正在输入状态', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      await textarea.setValue('T')
      await textarea.trigger('input')
      
      expect(wrapper.emitted('typing-start')).toBeTruthy()
    })

    it('应该在停止输入后清除状态', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      await textarea.setValue('Test')
      await textarea.trigger('input')
      
      // 等待防抖
      setTimeout(() => {
        expect(wrapper.emitted('typing-stop')).toBeTruthy()
      }, 1000)
    })
  })

  describe('字数统计', () => {
    it('应该显示字数统计', async () => {
      const textarea = wrapper.find('.message-textarea')
      await textarea.setValue('Hello world')
      
      expect(wrapper.find('.char-count').text()).toBe('11')
    })

    it('应该在接近限制时显示警告', async () => {
      const textarea = wrapper.find('.message-textarea')
      const longText = 'A'.repeat(4900) // 接近5000字符限制
      await textarea.setValue(longText)
      
      expect(wrapper.find('.char-count').classes()).toContain('warning')
    })

    it('应该在超出限制时显示错误', async () => {
      const textarea = wrapper.find('.message-textarea')
      const tooLongText = 'A'.repeat(5100) // 超出5000字符限制
      await textarea.setValue(tooLongText)
      
      expect(wrapper.find('.char-count').classes()).toContain('error')
      expect(wrapper.find('.send-btn').attributes('disabled')).toBeDefined()
    })
  })

  describe('粘贴处理', () => {
    it('应该处理文本粘贴', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      const pasteEvent = new ClipboardEvent('paste', {
        clipboardData: new DataTransfer()
      })
      pasteEvent.clipboardData?.setData('text/plain', 'Pasted text')
      
      await textarea.trigger('paste', pasteEvent)
      
      expect(wrapper.vm.messageContent).toContain('Pasted text')
    })

    it('应该处理图片粘贴', async () => {
      const textarea = wrapper.find('.message-textarea')
      
      const file = new File(['image'], 'image.png', { type: 'image/png' })
      const pasteEvent = new ClipboardEvent('paste', {
        clipboardData: new DataTransfer()
      })
      pasteEvent.clipboardData?.items.add(file)
      
      await textarea.trigger('paste', pasteEvent)
      
      expect(fileStore.addFilesToUploadQueue).toHaveBeenCalledWith([file])
    })
  })

  describe('错误处理', () => {
    it('应该处理发送失败', async () => {
      const error = new Error('Send failed')
      vi.spyOn(messageStore, 'sendMessage').mockRejectedValue(error)
      
      const textarea = wrapper.find('.message-textarea')
      const sendBtn = wrapper.find('.send-btn')
      
      await textarea.setValue('Test message')
      await sendBtn.trigger('click')
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.error).toBe('Send failed')
      expect(wrapper.find('.error-message').exists()).toBe(true)
    })

    it('应该清除错误状态', async () => {
      wrapper.vm.error = 'Test error'
      await wrapper.vm.$nextTick()
      
      const textarea = wrapper.find('.message-textarea')
      await textarea.trigger('input')
      
      expect(wrapper.vm.error).toBe('')
    })
  })

  describe('无障碍支持', () => {
    it('应该有正确的 ARIA 标签', () => {
      const textarea = wrapper.find('.message-textarea')
      expect(textarea.attributes('aria-label')).toBeDefined()
      expect(textarea.attributes('role')).toBe('textbox')
    })

    it('应该支持屏幕阅读器', () => {
      const sendBtn = wrapper.find('.send-btn')
      expect(sendBtn.attributes('aria-label')).toBeDefined()
    })
  })
})