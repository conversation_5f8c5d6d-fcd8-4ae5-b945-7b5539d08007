/**
 * Playwright 全局清理文件
 */

import { FullConfig } from '@playwright/test'
import path from 'path'
import fs from 'fs'

/**
 * 全局清理函数
 * 在所有测试运行完成后执行一次
 */
async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown for chat module e2e tests...')
  
  try {
    // 停止模拟服务器
    await stopMockServer()
    
    // 清理测试数据库
    await cleanupTestDatabase()
    
    // 清理临时文件
    await cleanupTemporaryFiles()
    
    // 生成测试报告摘要
    await generateTestSummary()
    
    // 清理认证状态（可选）
    await cleanupAuthState()
    
    console.log('✅ Global teardown completed successfully')
  } catch (error) {
    console.error('❌ Global teardown failed:', error)
    // 不抛出错误，避免影响测试结果
  }
}

/**
 * 停止模拟服务器
 */
async function stopMockServer() {
  console.log('🛑 Stopping mock server...')
  
  try {
    // 读取模拟服务器配置
    const configPath = path.join(__dirname, '../test-data/mock-server-config.json')
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'))
      console.log(`Mock server was configured on ${config.host}:${config.port}`)
      
      // 这里可以添加实际停止服务器的逻辑
      // 例如：发送停止信号、关闭端口等
    }
    
    console.log('✅ Mock server stopped successfully')
  } catch (error) {
    console.error('❌ Failed to stop mock server:', error)
  }
}

/**
 * 清理测试数据库
 */
async function cleanupTestDatabase() {
  console.log('🗄️ Cleaning up test database...')
  
  try {
    // 清理测试数据文件
    const testDataPath = path.join(__dirname, '../test-data/database.json')
    if (fs.existsSync(testDataPath)) {
      // 可以选择删除或重置数据
      const emptyData = {
        users: [],
        sessions: [],
        messages: []
      }
      fs.writeFileSync(testDataPath, JSON.stringify(emptyData, null, 2))
      console.log('Test database data reset')
    }
    
    // 这里可以添加实际数据库清理逻辑
    // 例如：删除测试表、清理测试数据等
    
    console.log('✅ Test database cleaned up successfully')
  } catch (error) {
    console.error('❌ Failed to cleanup test database:', error)
  }
}

/**
 * 清理临时文件
 */
async function cleanupTemporaryFiles() {
  console.log('📁 Cleaning up temporary files...')
  
  try {
    // 清理上传的测试文件
    const uploadsDir = path.join(__dirname, '../test-data/uploads')
    if (fs.existsSync(uploadsDir)) {
      fs.rmSync(uploadsDir, { recursive: true, force: true })
      console.log('Temporary upload files cleaned up')
    }
    
    // 清理下载的文件
    const downloadsDir = path.join(__dirname, '../test-data/downloads')
    if (fs.existsSync(downloadsDir)) {
      fs.rmSync(downloadsDir, { recursive: true, force: true })
      console.log('Temporary download files cleaned up')
    }
    
    // 清理缓存文件
    const cacheDir = path.join(__dirname, '../test-data/cache')
    if (fs.existsSync(cacheDir)) {
      fs.rmSync(cacheDir, { recursive: true, force: true })
      console.log('Cache files cleaned up')
    }
    
    console.log('✅ Temporary files cleaned up successfully')
  } catch (error) {
    console.error('❌ Failed to cleanup temporary files:', error)
  }
}

/**
 * 生成测试报告摘要
 */
async function generateTestSummary() {
  console.log('📊 Generating test summary...')
  
  try {
    const testResultsDir = path.join(__dirname, '../test-results')
    if (!fs.existsSync(testResultsDir)) {
      console.log('No test results directory found')
      return
    }
    
    // 收集测试结果信息
    const summary = {
      timestamp: new Date().toISOString(),
      testRun: {
        startTime: process.env.TEST_START_TIME || new Date().toISOString(),
        endTime: new Date().toISOString(),
        duration: 0
      },
      results: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0
      },
      coverage: {
        statements: 0,
        branches: 0,
        functions: 0,
        lines: 0
      },
      artifacts: {
        screenshots: 0,
        videos: 0,
        traces: 0
      }
    }
    
    // 计算测试持续时间
    if (process.env.TEST_START_TIME) {
      const startTime = new Date(process.env.TEST_START_TIME)
      const endTime = new Date()
      summary.testRun.duration = endTime.getTime() - startTime.getTime()
    }
    
    // 扫描测试结果文件
    const files = fs.readdirSync(testResultsDir)
    files.forEach(file => {
      const filePath = path.join(testResultsDir, file)
      const stat = fs.statSync(filePath)
      
      if (file.endsWith('.json') && file.includes('results')) {
        try {
          const results = JSON.parse(fs.readFileSync(filePath, 'utf-8'))
          // 解析测试结果
          if (results.suites) {
            results.suites.forEach((suite: any) => {
              suite.specs?.forEach((spec: any) => {
                summary.results.total++
                if (spec.ok) {
                  summary.results.passed++
                } else {
                  summary.results.failed++
                }
              })
            })
          }
        } catch (error) {
          console.warn(`Failed to parse results file: ${file}`)
        }
      }
      
      // 统计截图数量
      if (file.endsWith('.png') || file.endsWith('.jpg')) {
        summary.artifacts.screenshots++
      }
      
      // 统计视频数量
      if (file.endsWith('.webm') || file.endsWith('.mp4')) {
        summary.artifacts.videos++
      }
      
      // 统计追踪文件数量
      if (file.endsWith('.zip') && file.includes('trace')) {
        summary.artifacts.traces++
      }
    })
    
    // 保存测试摘要
    const summaryPath = path.join(testResultsDir, 'test-summary.json')
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2))
    
    // 生成简单的文本报告
    const textReport = `
=== Chat Module E2E Test Summary ===
Test Run: ${summary.testRun.startTime} - ${summary.testRun.endTime}
Duration: ${Math.round(summary.testRun.duration / 1000)}s

Results:
  Total: ${summary.results.total}
  Passed: ${summary.results.passed}
  Failed: ${summary.results.failed}
  Skipped: ${summary.results.skipped}
  Success Rate: ${summary.results.total > 0 ? Math.round((summary.results.passed / summary.results.total) * 100) : 0}%

Artifacts:
  Screenshots: ${summary.artifacts.screenshots}
  Videos: ${summary.artifacts.videos}
  Traces: ${summary.artifacts.traces}

=====================================
`
    
    const textReportPath = path.join(testResultsDir, 'test-summary.txt')
    fs.writeFileSync(textReportPath, textReport)
    
    console.log('✅ Test summary generated successfully')
    console.log(textReport)
  } catch (error) {
    console.error('❌ Failed to generate test summary:', error)
  }
}

/**
 * 清理认证状态
 */
async function cleanupAuthState() {
  console.log('🔐 Cleaning up authentication state...')
  
  try {
    const authStatePath = path.join(__dirname, '../test-data/auth-state.json')
    if (fs.existsSync(authStatePath)) {
      // 可以选择保留认证状态以供下次测试使用
      // 或者删除以确保每次测试都是全新的状态
      
      // 选项1：删除认证状态
      // fs.unlinkSync(authStatePath)
      // console.log('Authentication state deleted')
      
      // 选项2：保留认证状态
      console.log('Authentication state preserved for next test run')
    }
    
    console.log('✅ Authentication state cleanup completed')
  } catch (error) {
    console.error('❌ Failed to cleanup authentication state:', error)
  }
}

/**
 * 清理浏览器数据
 */
async function cleanupBrowserData() {
  console.log('🌐 Cleaning up browser data...')
  
  try {
    // 清理浏览器缓存、Cookie、本地存储等
    // Playwright 通常会自动清理，但可以在这里添加额外的清理逻辑
    
    console.log('✅ Browser data cleanup completed')
  } catch (error) {
    console.error('❌ Failed to cleanup browser data:', error)
  }
}

/**
 * 归档测试结果
 */
async function archiveTestResults() {
  console.log('📦 Archiving test results...')
  
  try {
    const testResultsDir = path.join(__dirname, '../test-results')
    const archiveDir = path.join(__dirname, '../archives')
    
    if (fs.existsSync(testResultsDir)) {
      // 创建归档目录
      if (!fs.existsSync(archiveDir)) {
        fs.mkdirSync(archiveDir, { recursive: true })
      }
      
      // 创建带时间戳的归档文件夹
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const archivePath = path.join(archiveDir, `test-results-${timestamp}`)
      
      // 复制测试结果到归档目录
      fs.cpSync(testResultsDir, archivePath, { recursive: true })
      
      console.log(`✅ Test results archived to: ${archivePath}`)
    }
  } catch (error) {
    console.error('❌ Failed to archive test results:', error)
  }
}

/**
 * 发送测试通知
 */
async function sendTestNotification() {
  console.log('📧 Sending test notification...')
  
  try {
    // 这里可以添加发送测试结果通知的逻辑
    // 例如：发送邮件、Slack 消息、企业微信等
    
    const summaryPath = path.join(__dirname, '../test-results/test-summary.json')
    if (fs.existsSync(summaryPath)) {
      const summary = JSON.parse(fs.readFileSync(summaryPath, 'utf-8'))
      
      // 模拟发送通知
      const notification = {
        title: 'Chat Module E2E Test Completed',
        message: `Tests: ${summary.results.total}, Passed: ${summary.results.passed}, Failed: ${summary.results.failed}`,
        timestamp: new Date().toISOString()
      }
      
      console.log('📨 Notification:', notification)
    }
    
    console.log('✅ Test notification sent successfully')
  } catch (error) {
    console.error('❌ Failed to send test notification:', error)
  }
}

/**
 * 验证清理结果
 */
function validateCleanup() {
  console.log('🔍 Validating cleanup results...')
  
  try {
    const checks = [
      {
        name: 'Temporary files',
        path: path.join(__dirname, '../test-data/uploads'),
        shouldExist: false
      },
      {
        name: 'Cache files',
        path: path.join(__dirname, '../test-data/cache'),
        shouldExist: false
      },
      {
        name: 'Test results',
        path: path.join(__dirname, '../test-results'),
        shouldExist: true
      }
    ]
    
    checks.forEach(check => {
      const exists = fs.existsSync(check.path)
      if (exists === check.shouldExist) {
        console.log(`✅ ${check.name}: OK`)
      } else {
        console.warn(`⚠️ ${check.name}: Expected ${check.shouldExist ? 'to exist' : 'not to exist'}`)
      }
    })
    
    console.log('✅ Cleanup validation completed')
  } catch (error) {
    console.error('❌ Failed to validate cleanup:', error)
  }
}

// 主清理函数
export default async function teardown(config: FullConfig) {
  try {
    // 记录清理开始时间
    const startTime = Date.now()
    
    // 执行全局清理
    await globalTeardown(config)
    
    // 清理浏览器数据
    await cleanupBrowserData()
    
    // 归档测试结果（可选）
    if (process.env.ARCHIVE_RESULTS === 'true') {
      await archiveTestResults()
    }
    
    // 发送测试通知（可选）
    if (process.env.SEND_NOTIFICATIONS === 'true') {
      await sendTestNotification()
    }
    
    // 验证清理结果
    validateCleanup()
    
    const duration = Date.now() - startTime
    console.log(`🎉 All teardown tasks completed successfully in ${duration}ms!`)
  } catch (error) {
    console.error('💥 Global teardown failed:', error)
    // 不退出进程，避免影响测试结果报告
  }
}