/**
 * 聊天模块主入口文件
 */

// 导出类型定义
export * from './types'

// 导出常量
// export * from './constants' // 暂时注释掉避免重复导出

// 导出工具函数
export * from './utils'

// 导出服务层
export * from './services'

// 导出API层
export * from './api'

// 导出状态管理
export * from './stores'

// 导出主要服务实例
export { ChatClient } from './services/chat-client'
export { WebSocketManager } from './services/websocket'
export { MessageService } from './services/message'
export { SessionService } from './services/session'
export { NotificationService } from './services/notification'
export { FileService } from './services/file'
export { ChatApi } from './api'

// 导出主要状态管理
export {
  useChatStore,
  useMessageStore,
  useSessionStore,
  useNotificationStore,
  useFileStore
} from './stores'

// 导出工具类
export { EventEmitter, createEventEmitter } from './utils/event-emitter'
export { Logger, LoggerManager } from './utils/logger'

// 默认导出聊天客户端
export { ChatClient as default } from './services/chat-client'