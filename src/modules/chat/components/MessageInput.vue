<template>
  <div class="message-input">
    <!-- 回复消息预览 -->
    <div v-if="replyMessage" class="message-input__reply-preview">
      <div class="message-input__reply-content">
        <div class="message-input__reply-header">
          <span class="message-input__reply-label">回复</span>
          <span class="message-input__reply-sender">{{ replyMessage.sender_id }}</span>
        </div>
        <div class="message-input__reply-message">
          {{ getReplyPreviewText(replyMessage) }}
        </div>
      </div>
      <button
        class="message-input__reply-close"
        @click="clearReply"
        title="取消回复"
      >
        <svg viewBox="0 0 24 24">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
      </button>
    </div>
    
    <!-- 编辑消息预览 -->
    <div v-if="editMessage" class="message-input__edit-preview">
      <div class="message-input__edit-content">
        <div class="message-input__edit-header">
          <span class="message-input__edit-label">编辑消息</span>
        </div>
        <div class="message-input__edit-message">
          {{ getEditPreviewText(editMessage) }}
        </div>
      </div>
      <button
        class="message-input__edit-close"
        @click="clearEdit"
        title="取消编辑"
      >
        <svg viewBox="0 0 24 24">
          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </svg>
      </button>
    </div>
    
    <!-- 文件上传预览 -->
    <div v-if="uploadFiles.length > 0" class="message-input__file-preview">
      <div class="message-input__file-list">
        <div
          v-for="(file, index) in uploadFiles"
          :key="index"
          class="message-input__file-item"
        >
          <!-- 图片预览 -->
          <div v-if="isImageFile(file)" class="message-input__file-image">
            <img
              :src="getFilePreviewUrl(file)"
              :alt="file.name"
              class="message-input__file-image-preview"
            />
            <button
              class="message-input__file-remove"
              @click="removeFile(index)"
              title="移除文件"
            >
              <svg viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
          
          <!-- 文件信息 -->
          <div v-else class="message-input__file-info">
            <div class="message-input__file-icon">
              <svg viewBox="0 0 24 24">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </div>
            <div class="message-input__file-details">
              <p class="message-input__file-name">{{ file.name }}</p>
              <p class="message-input__file-size">{{ formatFileSize(file.size) }}</p>
            </div>
            <button
              class="message-input__file-remove"
              @click="removeFile(index)"
              title="移除文件"
            >
              <svg viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 输入区域 -->
    <div class="message-input__input-area">
      <!-- 工具栏 -->
      <div class="message-input__toolbar">
        <!-- 表情按钮 -->
        <button
          class="message-input__tool-btn"
          @click="showEmojiPicker = !showEmojiPicker"
          title="表情"
        >
          <svg class="message-input__tool-icon" viewBox="0 0 24 24">
            <path d="M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,17.5C14.33,17.5 16.31,16.04 17.11,14H6.89C7.69,16.04 9.67,17.5 12,17.5M8.5,11A1.5,1.5 0 0,0 10,9.5A1.5,1.5 0 0,0 8.5,8A1.5,1.5 0 0,0 7,9.5A1.5,1.5 0 0,0 8.5,11M15.5,11A1.5,1.5 0 0,0 17,9.5A1.5,1.5 0 0,0 15.5,8A1.5,1.5 0 0,0 14,9.5A1.5,1.5 0 0,0 15.5,11Z"/>
          </svg>
        </button>
        
        <!-- 文件上传按钮 -->
        <button
          class="message-input__tool-btn"
          @click="triggerFileUpload"
          title="上传文件"
        >
          <svg class="message-input__tool-icon" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
        </button>
        
        <!-- 图片上传按钮 -->
        <button
          class="message-input__tool-btn"
          @click="triggerImageUpload"
          title="上传图片"
        >
          <svg class="message-input__tool-icon" viewBox="0 0 24 24">
            <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
          </svg>
        </button>
        
        <!-- 快捷回复按钮 -->
        <button
          class="message-input__tool-btn"
          @click="showQuickReplies = !showQuickReplies"
          title="快捷回复"
        >
          <svg class="message-input__tool-icon" viewBox="0 0 24 24">
            <path d="M12,3A9,9 0 0,0 3,12A9,9 0 0,0 12,21A8.69,8.69 0 0,0 16.6,19.35L21,21L19.35,16.6A8.69,8.69 0 0,0 21,12A9,9 0 0,0 12,3M12,5A7,7 0 0,1 19,12A7,7 0 0,1 12,19A7,7 0 0,1 5,12A7,7 0 0,1 12,5M9,10V12H15V10H9M9,14V16H13V14H9Z"/>
          </svg>
        </button>
        
        <!-- 语音输入按钮 -->
        <button
          class="message-input__tool-btn"
          :class="{ 'message-input__tool-btn--active': isRecording }"
          @mousedown="startRecording"
          @mouseup="stopRecording"
          @mouseleave="stopRecording"
          title="语音输入"
        >
          <svg class="message-input__tool-icon" viewBox="0 0 24 24">
            <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
          </svg>
        </button>
      </div>
      
      <!-- 文本输入框 -->
      <div class="message-input__input-container">
        <textarea
          ref="textareaRef"
          v-model="messageText"
          class="message-input__textarea"
          :placeholder="getPlaceholder()"
          :disabled="isLoading || isRecording"
          @keydown="handleKeyDown"
          @input="handleInput"
          @paste="handlePaste"
          @focus="handleFocus"
          @blur="handleBlur"
        ></textarea>
        
        <!-- 字数统计 -->
        <div v-if="messageText.length > 0" class="message-input__char-count">
          {{ messageText.length }}/{{ maxLength }}
        </div>
      </div>
      
      <!-- 发送按钮 -->
      <button
        class="message-input__send-btn"
        :disabled="!canSend"
        @click="handleSend"
        title="发送消息 (Ctrl+Enter)"
      >
        <svg v-if="!isLoading" class="message-input__send-icon" viewBox="0 0 24 24">
          <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
        </svg>
        <div v-else class="message-input__sending-spinner"></div>
      </button>
    </div>
    
    <!-- 表情选择器 -->
    <div v-if="showEmojiPicker" class="message-input__emoji-picker">
      <div class="message-input__emoji-categories">
        <button
          v-for="category in emojiCategories"
          :key="category.name"
          class="message-input__emoji-category"
          :class="{ 'message-input__emoji-category--active': selectedEmojiCategory === category.name }"
          @click="selectedEmojiCategory = category.name"
          :title="category.label"
        >
          {{ category.icon }}
        </button>
      </div>
      
      <div class="message-input__emoji-grid">
        <button
          v-for="emoji in currentEmojis"
          :key="emoji"
          class="message-input__emoji-item"
          @click="insertEmoji(emoji)"
          :title="emoji"
        >
          {{ emoji }}
        </button>
      </div>
    </div>
    
    <!-- 快捷回复 -->
    <div v-if="showQuickReplies" class="message-input__quick-replies">
      <div class="message-input__quick-reply-header">
        <h4>快捷回复</h4>
        <button
          class="message-input__quick-reply-close"
          @click="showQuickReplies = false"
        >
          <svg viewBox="0 0 24 24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
      
      <div class="message-input__quick-reply-list">
        <button
          v-for="reply in quickReplies"
          :key="reply.id"
          class="message-input__quick-reply-item"
          @click="insertQuickReply(reply.content)"
        >
          {{ reply.content }}
        </button>
      </div>
    </div>
    
    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      multiple
      class="message-input__file-input"
      @change="handleFileSelect"
    />
    
    <input
      ref="imageInputRef"
      type="file"
      accept="image/*"
      multiple
      class="message-input__file-input"
      @change="handleImageSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useMessageStore } from '../stores'
import type { Message, QuickReply } from '../types'
import { debounce } from 'lodash-es'

// Props
interface Props {
  replyMessage?: Message | null
  editMessage?: Message | null
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  replyMessage: null,
  editMessage: null,
  maxLength: 2000
})

// Emits
interface Emits {
  send: [content: string, options?: any]
  clearReply: []
  clearEdit: []
  typing: [isTyping: boolean]
}

const emit = defineEmits<Emits>()

// Stores
const messageStore = useMessageStore()

// Store refs
const { isLoading } = storeToRefs(messageStore)

// Refs
const textareaRef = ref<HTMLTextAreaElement>()
const fileInputRef = ref<HTMLInputElement>()
const imageInputRef = ref<HTMLInputElement>()

// State
const messageText = ref('')
const uploadFiles = ref<File[]>([])
const showEmojiPicker = ref(false)
const showQuickReplies = ref(false)
const selectedEmojiCategory = ref('smileys')
const isRecording = ref(false)
const isFocused = ref(false)
const typingTimer = ref<number | null>(null)

// 表情数据
const emojiCategories = ref([
  {
    name: 'smileys',
    label: '笑脸',
    icon: '😀',
    emojis: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳']
  },
  {
    name: 'gestures',
    label: '手势',
    icon: '👍',
    emojis: ['👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲', '🤝', '🙏']
  },
  {
    name: 'hearts',
    label: '爱心',
    icon: '❤️',
    emojis: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟']
  },
  {
    name: 'objects',
    label: '物品',
    icon: '🎉',
    emojis: ['🎉', '🎊', '🎈', '🎁', '🏆', '🥇', '🥈', '🥉', '⭐', '🌟', '💫', '✨', '🔥', '💯', '💢', '💥', '💦', '💨']
  }
])

// 快捷回复数据
const quickReplies = ref<QuickReply[]>([
  {
    id: 1,
    title: '问候语',
    content: '您好，有什么可以帮助您的吗？',
    category: 'greeting',
    user_id: 1,
    user_type: 'system' as any,
    usage_count: 0,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: 2,
    title: '感谢语',
    content: '感谢您的咨询，我会尽快为您处理。',
    category: 'thanks',
    user_id: 1,
    user_type: 'system' as any,
    usage_count: 0,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
])

// Computed
const canSend = computed(() => {
  return (
    !isLoading.value &&
    (messageText.value.trim() || uploadFiles.value.length > 0) &&
    messageText.value.length <= props.maxLength
  )
})

const currentEmojis = computed(() => {
  const category = emojiCategories.value.find(c => c.name === selectedEmojiCategory.value)
  return category ? category.emojis : []
})

// Methods
const getPlaceholder = () => {
  if (isRecording.value) {
    return '正在录音...'
  }
  if (props.editMessage) {
    return '编辑消息...'
  }
  if (props.replyMessage) {
    return '回复消息...'
  }
  return '输入消息...'
}

const getReplyPreviewText = (message: Message) => {
  if (message.type === 'text') {
    return message.content.length > 50 
      ? message.content.substring(0, 50) + '...'
      : message.content
  } else if (message.type === 'image') {
    return '[图片]'
  } else if (message.type === 'file') {
    return `[文件] ${message.file_name}`
  }
  return '[消息]'
}

const getEditPreviewText = (message: Message) => {
  return getReplyPreviewText(message)
}

const isImageFile = (file: File) => {
  return file.type.startsWith('image/')
}

const getFilePreviewUrl = (file: File) => {
  return URL.createObjectURL(file)
}

const formatFileSize = (size: number) => {
  if (size < 1024) {
    return `${size} B`
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} KB`
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / (1024 * 1024)).toFixed(1)} MB`
  } else {
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`
  }
}

const adjustTextareaHeight = () => {
  if (textareaRef.value) {
    textareaRef.value.style.height = 'auto'
    textareaRef.value.style.height = Math.min(textareaRef.value.scrollHeight, 120) + 'px'
  }
}

const clearReply = () => {
  emit('clearReply')
}

const clearEdit = () => {
  emit('clearEdit')
}

const removeFile = (index: number) => {
  uploadFiles.value.splice(index, 1)
}

const triggerFileUpload = () => {
  fileInputRef.value?.click()
}

const triggerImageUpload = () => {
  imageInputRef.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    uploadFiles.value.push(...Array.from(target.files))
    target.value = ''
  }
}

const handleImageSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    uploadFiles.value.push(...Array.from(target.files))
    target.value = ''
  }
}

const insertEmoji = (emoji: string) => {
  const textarea = textareaRef.value
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = messageText.value
    
    messageText.value = text.substring(0, start) + emoji + text.substring(end)
    
    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + emoji.length, start + emoji.length)
      adjustTextareaHeight()
    })
  }
  
  showEmojiPicker.value = false
}

const insertQuickReply = (content: string) => {
  messageText.value = content
  showQuickReplies.value = false
  nextTick(() => {
    textareaRef.value?.focus()
    adjustTextareaHeight()
  })
}

const startRecording = () => {
  // TODO: 实现语音录制功能
  isRecording.value = true
  console.log('开始录音')
}

const stopRecording = () => {
  // TODO: 实现语音录制功能
  isRecording.value = false
  console.log('停止录音')
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (event.ctrlKey || event.metaKey) {
      // Ctrl+Enter 或 Cmd+Enter 发送消息
      event.preventDefault()
      handleSend()
    } else if (!event.shiftKey) {
      // Enter 发送消息（Shift+Enter 换行）
      event.preventDefault()
      handleSend()
    }
  } else if (event.key === 'Escape') {
    // ESC 取消回复或编辑
    if (props.replyMessage) {
      clearReply()
    } else if (props.editMessage) {
      clearEdit()
    }
  }
}

const handleInput = debounce(() => {
  adjustTextareaHeight()
  
  // 发送正在输入状态
  if (messageText.value.trim()) {
    emit('typing', true)
    
    // 清除之前的定时器
    if (typingTimer.value) {
      clearTimeout(typingTimer.value)
    }
    
    // 3秒后停止输入状态
    typingTimer.value = window.setTimeout(() => {
      emit('typing', false)
    }, 3000)
  } else {
    emit('typing', false)
  }
}, 300)

const handlePaste = (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (!items) return
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      if (file) {
        uploadFiles.value.push(file)
      }
    }
  }
}

const handleFocus = () => {
  isFocused.value = true
}

const handleBlur = () => {
  isFocused.value = false
  emit('typing', false)
}

const handleSend = async () => {
  if (!canSend.value) return
  
  const content = messageText.value.trim()
  const files = [...uploadFiles.value]
  
  // 构建发送选项
  const options: any = {}
  
  if (props.replyMessage) {
    options.replyTo = props.replyMessage.id
  }
  
  if (props.editMessage) {
    options.editId = props.editMessage.id
  }
  
  if (files.length > 0) {
    options.files = files
  }
  
  try {
    // 发送消息
    emit('send', content, options)
    
    // 清空输入
    messageText.value = ''
    uploadFiles.value = []
    
    // 清除回复和编辑状态
    if (props.replyMessage) {
      clearReply()
    }
    if (props.editMessage) {
      clearEdit()
    }
    
    // 重置文本框高度
    nextTick(() => {
      adjustTextareaHeight()
      textareaRef.value?.focus()
    })
    
    // 停止输入状态
    emit('typing', false)
    
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}

const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  
  if (showEmojiPicker.value && !target.closest('.message-input__emoji-picker')) {
    showEmojiPicker.value = false
  }
  
  if (showQuickReplies.value && !target.closest('.message-input__quick-replies')) {
    showQuickReplies.value = false
  }
}

// Lifecycle
onMounted(() => {
  adjustTextareaHeight()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  
  if (typingTimer.value) {
    clearTimeout(typingTimer.value)
  }
  
  // 清理文件预览URL
  uploadFiles.value.forEach(file => {
    if (isImageFile(file)) {
      URL.revokeObjectURL(getFilePreviewUrl(file))
    }
  })
})

// Watch
watch(
  () => messageText.value,
  () => {
    nextTick(() => {
      adjustTextareaHeight()
    })
  }
)

watch(
  () => props.editMessage,
  (newEditMessage) => {
    if (newEditMessage && newEditMessage.type === 'text') {
      messageText.value = newEditMessage.content
      nextTick(() => {
        adjustTextareaHeight()
        textareaRef.value?.focus()
      })
    }
  }
)
</script>

<style scoped>
.message-input {
  @apply bg-white border-t border-gray-200;
}

.message-input__reply-preview,
.message-input__edit-preview {
  @apply flex items-center justify-between p-3 bg-blue-50 border-b border-blue-200;
}

.message-input__reply-content,
.message-input__edit-content {
  @apply flex-1 min-w-0;
}

.message-input__reply-header,
.message-input__edit-header {
  @apply flex items-center space-x-2 mb-1;
}

.message-input__reply-label,
.message-input__edit-label {
  @apply text-xs font-semibold text-blue-600 uppercase;
}

.message-input__reply-sender {
  @apply text-sm font-medium text-gray-900;
}

.message-input__reply-message,
.message-input__edit-message {
  @apply text-sm text-gray-600 truncate;
}

.message-input__reply-close,
.message-input__edit-close {
  @apply p-1 rounded hover:bg-blue-100 transition-colors;
}

.message-input__reply-close svg,
.message-input__edit-close svg {
  @apply w-4 h-4 fill-current text-gray-500;
}

.message-input__file-preview {
  @apply p-3 bg-gray-50 border-b border-gray-200;
}

.message-input__file-list {
  @apply flex flex-wrap gap-2;
}

.message-input__file-item {
  @apply relative;
}

.message-input__file-image {
  @apply relative w-20 h-20 rounded-lg overflow-hidden;
}

.message-input__file-image-preview {
  @apply w-full h-full object-cover;
}

.message-input__file-info {
  @apply flex items-center space-x-2 p-2 bg-white rounded-lg border border-gray-200;
}

.message-input__file-icon {
  @apply w-8 h-8 text-gray-600;
}

.message-input__file-icon svg {
  @apply w-full h-full fill-current;
}

.message-input__file-details {
  @apply flex flex-col min-w-0;
}

.message-input__file-name {
  @apply text-sm font-medium text-gray-900 truncate m-0;
}

.message-input__file-size {
  @apply text-xs text-gray-500 m-0;
}

.message-input__file-remove {
  @apply absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors;
}

.message-input__file-remove svg {
  @apply w-3 h-3 fill-current;
}

.message-input__input-area {
  @apply flex items-end p-4 space-x-3;
}

.message-input__toolbar {
  @apply flex items-center space-x-2;
}

.message-input__tool-btn {
  @apply p-2 rounded-md hover:bg-gray-100 transition-colors;
}

.message-input__tool-btn--active {
  @apply bg-red-100 text-red-600;
}

.message-input__tool-icon {
  @apply w-5 h-5 fill-current text-gray-600;
}

.message-input__tool-btn--active .message-input__tool-icon {
  @apply text-red-600;
}

.message-input__input-container {
  @apply flex-1 relative;
}

.message-input__textarea {
  @apply w-full min-h-[40px] max-h-[120px] px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed;
}

.message-input__char-count {
  @apply absolute bottom-1 right-2 text-xs text-gray-500;
}

.message-input__send-btn {
  @apply p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed;
}

.message-input__send-icon {
  @apply w-5 h-5 fill-current;
}

.message-input__sending-spinner {
  @apply w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}

.message-input__emoji-picker {
  @apply absolute bottom-full left-0 right-0 bg-white border border-gray-200 rounded-t-lg shadow-lg z-10;
}

.message-input__emoji-categories {
  @apply flex border-b border-gray-200;
}

.message-input__emoji-category {
  @apply flex-1 p-3 text-center hover:bg-gray-100 transition-colors;
}

.message-input__emoji-category--active {
  @apply bg-blue-50 text-blue-600 border-b-2 border-blue-600;
}

.message-input__emoji-grid {
  @apply grid grid-cols-8 gap-1 p-3 max-h-48 overflow-y-auto;
}

.message-input__emoji-item {
  @apply p-2 text-xl hover:bg-gray-100 rounded transition-colors;
}

.message-input__quick-replies {
  @apply absolute bottom-full left-0 right-0 bg-white border border-gray-200 rounded-t-lg shadow-lg z-10;
}

.message-input__quick-reply-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200;
}

.message-input__quick-reply-header h4 {
  @apply text-sm font-semibold text-gray-900 m-0;
}

.message-input__quick-reply-close {
  @apply p-1 rounded hover:bg-gray-100 transition-colors;
}

.message-input__quick-reply-close svg {
  @apply w-4 h-4 fill-current text-gray-500;
}

.message-input__quick-reply-list {
  @apply max-h-48 overflow-y-auto;
}

.message-input__quick-reply-item {
  @apply w-full p-3 text-left hover:bg-gray-100 transition-colors border-b border-gray-100 last:border-b-0;
}

.message-input__file-input {
  @apply hidden;
}
</style>