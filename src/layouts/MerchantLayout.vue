<template>
  <div class="merchant-layout">
    <!-- 头部区域 -->
    <header class="merchant-header">
      <div class="logo-container">
        <img v-if="systemInfo?.siteLogo" :src="adjustLinkProtocol(systemInfo?.siteLogo)" alt="siteLogo" class="logo" />
        <h1 class="title">商家中心</h1>
      </div>
      
      <div class="header-right">
        <!-- 聊天组件 -->
        <MerchantChat 
          v-if="merchantStore.isApproved"
          :merchant-id="merchantStore.merchantInfo?.id"
          :merchant-token="merchantStore.token"
          class="merchant-chat-component"
        />
        
        <!-- 用户菜单 -->
        <el-dropdown trigger="click" @command="handleCommand">
          <div class="user-info">
            <el-avatar :src="merchantStore.logo" :size="32" />
            <span class="merchant-name">{{ merchantStore.merchantName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="shop">店铺管理</el-dropdown-item>
              <el-dropdown-item command="settings">账户设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <div class="merchant-container">
      <!-- 侧边栏导航 -->
      <aside class="merchant-sidebar" :class="{ 'collapse': isCollapse }" v-if="merchantStore.isLoggedIn">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :router="true"
          :collapse="isCollapse"
        >
          <!-- 静态菜单项 -->
          <el-menu-item index="/merchant/dashboard">
            <el-icon><HomeFilled /></el-icon>
            <span>控制台</span>
          </el-menu-item>
          
          <!-- 动态菜单项循环显示 -->
          <template v-for="item in menuItems" :key="item.key">
            <!-- 跳过静态菜单项 -->
            <template v-if="item.dynamic || (item.key !== 'dashboard' && item.key !== 'settings')">
              <!-- 有子菜单的情况 -->
              <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path">
                <template #title>
                  <el-icon>
                    <component :is="item.icon || Menu" />
                  </el-icon>
                  <span>{{ item.label }}</span>
                </template>
                <el-menu-item 
                  v-for="child in item.children" 
                  :key="child.key" 
                  :index="child.path"
                >
                  <span>{{ child.label }}</span>
                </el-menu-item>
              </el-sub-menu>
              
              <!-- 没有子菜单的情况 -->
              <el-menu-item v-else :index="item.path">
                <el-icon>
                  <component :is="item.icon || Menu" />
                </el-icon>
                <template #title>{{ item.label }}</template>
              </el-menu-item>
            </template>
          </template>
          
          <!-- 账户设置菜单项 -->
          <el-menu-item index="/merchant/settings">
            <el-icon><Setting /></el-icon>
            <span>账户设置</span>
          </el-menu-item>
        </el-menu>
        
        <!-- 收起/展开按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon :size="20">
            <component :is="isCollapse ? Expand : Fold" />
          </el-icon>
        </div>
      </aside>
      
      <!-- 主内容区域 -->
      <main class="merchant-main">
        <!-- 面包屑导航 -->
        <el-breadcrumb v-if="breadcrumbs.length > 0" class="breadcrumb">
          <el-breadcrumb-item :to="{ path: '/merchant/dashboard' }">控制台</el-breadcrumb-item>
          <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index" :to="item.path">
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        
        <!-- 路由视图 -->
        <div class="content-container">
          <!-- 添加全局加载状态 -->
          <div v-if="isRouteLoading" class="global-loading">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <p>加载中...</p>
          </div>

          <router-view v-slot="{ Component }">
            <div>
              <!-- 直接使用组件，避免多层嵌套导致的DOM问题 -->
              <component v-if="Component" :is="Component" />
              
              <!-- 仅在没有组件时显示加载状态 -->
              <div v-else class="page-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <p>页面加载中...</p>
              </div>
            </div>
          </router-view>
        </div>
      </main>
    </div>
    
    <!-- 聊天功能已集成到MerchantChat组件中 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onBeforeUnmount, provide } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { 
  HomeFilled, 
  Setting, 
  ArrowDown,
  Menu,
  Fold,
  Expand,
  Loading
} from '@element-plus/icons-vue';
import { useMerchantStore } from '@/modules/merchant/stores/merchantStore';

import { useSystemStore } from '@/stores/systemStore';
import { adjustLinkProtocol } from '@/utils/format';
import MerchantChat from '@/modules/merchant/components/MerchantChat.vue';
import getChatWebSocketService, { ChatWebSocketService } from '@/modules/merchant/services/chatWebSocketService';

const systemStore = useSystemStore();
const systemInfo = computed(() => systemStore.systemInfo);

// 全局WebSocket聊天服务实例 - 后台服务模式（响应式）
const backgroundChatService = ref<ChatWebSocketService | null>(null)



// 提供给子组件使用
provide('chatWebSocketService', backgroundChatService)


// 定义菜单项和路径数据的接口
interface MenuItem {
  key: string;
  label: string;
  icon?: string;
  path: string;
  children?: MenuItem[];
  dynamic?: boolean;
}

interface PathItem {
  path: string;
  title: string;
  icon?: string;
  count?: number;
  [key: string]: any;
}

interface ModuleData {
  module: string;
  paths: PathItem[];
}

// 路由与商家store
const route = useRoute();
const router = useRouter();
const merchantStore = useMerchantStore();
// const systemStore = useSystemStore();
// const systemInfo = computed(() => systemStore.systemInfo);

// 侧边栏状态
const isCollapse = ref(false);
const screenWidth = ref(window.innerWidth);

// 路由加载状态
const isRouteLoading = ref(false);

// 响应式布局断点
const MOBILE_BREAKPOINT = 992; // 在992px以下自动折叠侧边栏

// 移除通知相关状态，改为使用聊天组件

// 静态菜单项 - 只保留dashboard
const staticMenuItems = ref<MenuItem[]>([
  {
    key: 'dashboard',
    label: '控制台',
    icon: 'HomeFilled',
    path: '/merchant/dashboard'
  },
  {
    key: 'promotion',
    label: '促销管理',
    icon: 'Discount',
    path: '/merchant/promotion',
    children: [
      {
        key: 'promotion-list',
        label: '促销活动',
        path: '/merchant/promotion/list',
        dynamic: true
      },
      {
        key: 'coupon-list',
        label: '优惠券管理',
        path: '/merchant/coupon/list',
        dynamic: true
      }
    ]
  },
  {
    key: 'takeout-order',
    label: '外卖订单管理',
    icon: 'List', // Main icon for the group
    path: '/merchant/order/list', // Added path for the group
    children: [
      { key: 'takeout-order-list', label: '订单列表', path: '/merchant/order/list', icon: 'List' },
      { key: 'takeout-order-statistics', label: '订单统计', path: '/merchant/order/statistics', icon: 'DataAnalysis' },
    ]
  },
  {
    key: 'settings',
    label: '账户设置',
    icon: 'Setting',
    path: '/merchant/settings'
  }]);

// 所有菜单项 - 包含静态和动态生成的
const menuItems = ref<MenuItem[]>([...staticMenuItems.value]);

// 处理前端路径数据，生成动态菜单项
const generateDynamicMenuItems = (frontendPaths: ModuleData[]) => {
  // 记录菜单生成过程
  console.log('开始生成动态菜单，前端路径数据:', frontendPaths);
  
  // 创建核心菜单项数组 - 包含静态菜单项和必要的功能模块，无论前端路径是否为空
  const coreMenuItems = [...staticMenuItems.value];
  
  // 定义必要的菜单项 - 这些菜单会始终显示
  const essentialMenuItems: MenuItem[] = [
    // 外卖商品管理
    {
      key: 'takeout',
      label: '外卖管理',
      icon: 'Food',
      path: '/merchant/takeout',
      children: [
        {
          key: 'takeout-food-list',
          label: '商品管理',
          path: '/merchant/takeout/food/list',
          dynamic: true
        },
        {
          key: 'takeout-food-add',
          label: '新增商品',
          path: '/merchant/takeout/food/add',
          dynamic: true
        },
        {
          key: 'takeout-category-list',
          label: '分类管理',
          path: '/merchant/takeout/category/list',
          dynamic: true
        }
      ],
      dynamic: true
    },
    // 营业时间管理
    {
      key: 'business-hours',
      label: '营业时间管理',
      icon: 'Timer',
      path: '/merchant/business-hours',
      dynamic: true
    }
  ];
  
  // 首先添加必要菜单项到核心菜单
  essentialMenuItems.forEach(item => {
    if (!coreMenuItems.some(existing => existing.path === item.path)) {
      coreMenuItems.push(item);
    }
  });
  
  // 如果前端路径数据为空，返回核心菜单
  if (!frontendPaths || !Array.isArray(frontendPaths) || frontendPaths.length === 0) {
    console.log('前端路径数据为空，使用核心商家菜单');
    return coreMenuItems;
  }

  // 使用已经包含必要菜单的核心菜单作为基础
const newMenuItems: MenuItem[] = [...coreMenuItems];
  
  // 处理前端路径数据
  frontendPaths.forEach(moduleData => {
    if (!moduleData.module || !moduleData.paths || !Array.isArray(moduleData.paths)) {
      return;
    }
    
    // 根据模块生成子菜单
    const moduleName = moduleData.module;
    const modulePaths = moduleData.paths;
    
    // 跳过空路径
    if (modulePaths.length === 0) return;
    
    // 根据模块名称创建不同的菜单结构
    switch(moduleName) {
      case 'merchant':
        // merchant模块的路径直接添加到顶级菜单
        console.log('处理merchant模块，路径数量:', modulePaths.length);
        console.log('当前菜单项:', JSON.stringify(newMenuItems.map(item => ({key: item.key, path: item.path}))));
        
        modulePaths.forEach((pathItem: PathItem) => {
          console.log('处理商家模块路径:', pathItem);
          
          // 检查是否已存在相同路径的菜单项（避免重复）
          const existingItem = newMenuItems.find(item => item.path === `/merchant/${pathItem.path}`);
          
          console.log(`路径 /merchant/${pathItem.path} 是否已存在:`, !!existingItem);
          
          if (existingItem) {
            console.log('找到冲突的菜单项:', existingItem);
          }
          
          if (!existingItem && pathItem.path && pathItem.title) {
            console.log('添加新的商家菜单项:', pathItem.title);
            newMenuItems.push({
              key: pathItem.path,
              label: pathItem.title,
              icon: pathItem.icon || 'Menu', // 使用路径中的图标或默认图标
              path: `/merchant/${pathItem.path}`,
              dynamic: true // 标记为动态生成的
            });
          } else {
            console.log('跳过添加菜单项的原因:', 
              !pathItem.path ? '路径为空' : 
              !pathItem.title ? '标题为空' : 
              '菜单项已存在');
          }
        });
        break;
        
      default:
        // 其他模块创建新的顶级菜单
        const moduleMenu: MenuItem = {
          key: moduleName,
          label: moduleName.charAt(0).toUpperCase() + moduleName.slice(1), // 首字母大写
          icon: 'Menu',
          path: `/merchant/${moduleName}`,
          children: modulePaths.map((pathItem: PathItem) => ({
            key: pathItem.path,
            label: pathItem.title,
            icon: pathItem.icon || undefined, // 使用路径中的图标或不设置（继承父级）
            path: `/merchant/${moduleName}/${pathItem.path}`,
            dynamic: true
          }))
        };
        
        // 检查是否已存在相同模块名的菜单
        const existingModule = newMenuItems.find(item => item.key === moduleName);
        if (!existingModule) {
          newMenuItems.push(moduleMenu);
        }
        break;
    }
  });
  
  console.log('生成的动态菜单：', newMenuItems);
  return newMenuItems;
};

// 监听前端路径数据变化，更新菜单
watch(() => merchantStore.frontendPaths, (newPaths) => {
  console.log('监听到frontendPaths变化:', newPaths);
  // 无论前端路径数据是否为空，都应用菜单生成逻辑
  // 当为空时会使用默认菜单
  menuItems.value = generateDynamicMenuItems(newPaths);
}, { deep: true });

// 激活的菜单项
const activeMenu = computed(() => {
  return route.path;
});

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched;
  return matched.slice(1).map(item => {
    return {
      title: item.meta.title as string || '',
      path: item.path
    };
  });
});

// 切换侧边栏状态
function toggleSidebar() {
  isCollapse.value = !isCollapse.value;
}

// 窗口尺寸改变时更新状态
function handleResize() {
  screenWidth.value = window.innerWidth;
  isCollapse.value = screenWidth.value < MOBILE_BREAKPOINT;
}

// 启动后台聊天服务
function startBackgroundChatService() {
  try {
    console.log('启动后台聊天服务');

    // 检查必要的条件
    if (!merchantStore.isLoggedIn) {
      console.warn('商家未登录，无法启动聊天服务');
      return;
    }

    if (!merchantStore.merchantInfo?.id) {
      console.warn('商家信息不完整，无法启动聊天服务');
      return;
    }

    if (!merchantStore.token) {
      console.warn('缺少认证token，无法启动聊天服务');
      return;
    }

    // 如果服务已存在，先停止
    if (backgroundChatService.value) {
      console.log('停止现有的后台聊天服务');
      backgroundChatService.value.disconnect();
    }

    // 创建新的服务实例
    backgroundChatService.value = getChatWebSocketService();
    console.log('创建后台聊天服务实例');

    // 自动连接WebSocket
    if (backgroundChatService.value) {
      backgroundChatService.value.connect();
      console.log('后台聊天服务已启动并连接');
    }

  } catch (error) {
    console.error('启动后台聊天服务失败:', error);
  }
}

// 停止后台聊天服务
function stopBackgroundChatService() {
  try {
    console.log('停止后台聊天服务');

    if (backgroundChatService.value) {
      // 断开连接
      backgroundChatService.value.disconnect();
      // 清空引用
      backgroundChatService.value = null;
      console.log('后台聊天服务已停止');
    } else {
      console.log('后台聊天服务不存在，无需停止');
    }
  } catch (error) {
    console.error('停止后台聊天服务失败:', error);
  }
}



// 下拉菜单命令处理
function handleCommand(command: string) {
  if (command === 'logout') {
    // 登出时停止后台聊天服务
    stopBackgroundChatService();
    merchantStore.logout();
    router.push('/merchant/login');
  } else {
    router.push(`/merchant/${command}`);
  }
}

// 监听商家登录状态
watch(() => merchantStore.isLoggedIn, (isLoggedIn) => {
  if (!isLoggedIn) {
    // 登出时停止后台聊天服务
    stopBackgroundChatService();
    if (route.meta.requiresAuth) {
      router.push('/merchant/login');
    }
  } else {
    // 登录后启动后台聊天服务
    console.log('检测到商家登录，准备启动后台聊天服务');
    // 延迟一下确保商家信息已加载
    setTimeout(() => {
      startBackgroundChatService();
    }, 1000);
  }
});

// 监听商家审核状态变化，只在需要时初始化聊天服务
watch(() => merchantStore.isApproved, (isApproved) => {
  if (merchantStore.isLoggedIn) {
    if (isApproved) {
      // 商家通过审核后，启动后台聊天服务
      console.log('商家通过审核，启动后台聊天服务');
      setTimeout(() => {
        startBackgroundChatService();
      }, 500);
    } else if (route.meta.requireApproved) {
      // 商家未通过审核，跳转到状态页
      router.push('/merchant/status');
    }
  }
});



// 添加窗口尺寸变化监听
onMounted(() => {
  // 初始化时检查窗口尺寸
  handleResize();
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 清理后台聊天服务
  stopBackgroundChatService();
});

// 组件挂载时加载商家信息
onMounted(async () => {
  console.log('MerchantLayout 组件开始挂载');
  
  // 尝试恢复登录状态
  try {
    // 先尝试使用 retoken 恢复当前 token 状态
    const tokenRestored = await merchantStore.retoken();
    console.log('token恢复状态:', tokenRestored, '当前登录状态:', merchantStore.isLoggedIn);
    
    // 如果有 token 但没有商家信息，获取商家信息
    if (merchantStore.isLoggedIn && !merchantStore.merchantInfo) {
      console.log('已有token但缺少商家信息，进行获取');
      await merchantStore.fetchMerchantInfo();
    }
    // 如果没有登录但需要认证，尝试使用长期token登录
    else if (!merchantStore.isLoggedIn && route.meta.requiresAuth) {
      console.log('未登录但需要认证，尝试使用长期token登录');
      const success = await merchantStore.loginByLongTermTokenAction();
      if (!success && route.meta.requiresAuth) {
        console.log('长期token登录失败，跳转到登录页');
        router.push('/merchant/login');
        return;
      }
    }
    
    // 检查已登录状态下是否需要跳转到审核页
    if (merchantStore.isLoggedIn && merchantStore.merchantInfo) {
      // 如果需要审核但未通过，跳转到状态页
      if (route.meta.requireApproved && !merchantStore.isApproved) {
        console.log('商家需要审核但未通过，跳转到状态页');
        router.push('/merchant/status');
      }
      
      // 加载统计数据
      if (merchantStore.isApproved) {
        console.log('商家已通过审核，加载统计数据');
        merchantStore.fetchStatistics();
        // TODO: 加载通知数据
        // loadNotifications();
      }
      
      // 启动后台聊天服务
      if (merchantStore.merchantInfo?.id && merchantStore.token) {
        console.log('商家信息完整，启动后台聊天服务');
        startBackgroundChatService();
      }
    } else if (route.meta.requiresAuth) {
      // 如果需要登录但没有登录，跳转到登录页
      console.log('需要登录但未登录，跳转到登录页');
      router.push('/merchant/login');
    }
  } catch (error) {
    console.error('初始化商家信息出错:', error);
    // Token失效或出错，跳转登录页
    if (route.meta.requiresAuth) {
      router.push('/merchant/login');
    }
  }
  
  console.log('商家布局组件挂载完成，当前状态:', {
    isLoggedIn: merchantStore.isLoggedIn,
    hasInfo: !!merchantStore.merchantInfo,
    isApproved: merchantStore.isApproved
  });
  
  // 获取前端路径数据，用于生成动态菜单
  try {
    console.log('获取商家模块前端路径数据');
    
    // 不要清除缓存，而是使用缓存的数据（如果有的话）
    // 这样即使API请求失败，也能保留之前的菜单
    // await merchantStore.clearFrontendPathsCache(); // 注释掉清除缓存的操作
    
    // 获取前端路径数据
    const paths = await merchantStore.fetchFrontendPaths(false); // 先尝试从缓存获取
    console.log('获取到前端路径数据:', paths);
    
    // 确保更新菜单数据（不管是否为空）
    menuItems.value = generateDynamicMenuItems(paths);
    
    if (!paths || paths.length === 0) {
      console.warn('获取的前端路径数据为空，尝试重新获取...');
      // 延迟再试一次，这次强制从服务器获取
      setTimeout(async () => {
        const retryPaths = await merchantStore.fetchFrontendPaths(true); // 强制刷新
        console.log('重试获取前端路径数据:', retryPaths);
        // 重要：确保重试后也更新菜单
        menuItems.value = generateDynamicMenuItems(retryPaths);
      }, 1000);
    }
  } catch (error) {
    console.error('获取前端路径数据失败:', error);
    // 即使出错，也应用默认菜单
    menuItems.value = generateDynamicMenuItems([]);
  }
});
</script>

<style lang="scss" scoped>
//@use '@/styles/variables.scss' as *;
html, body {
  overflow: hidden; /* 禁止滚动条 */
}
.merchant-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .merchant-header {
    height: 60px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;
    
    .logo-container {
      display: flex;
      align-items: center;
      
      .logo {
        height: 40px;
        margin-right: 10px;
      }
      
      .title {
        font-size: 18px;
        font-weight: 600;
        color: $primary-color;
        margin: 0;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      
      .merchant-chat-component {
        margin-right: 20px;
      }
      
      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .merchant-name {
          margin: 0 8px;
          font-size: 14px;
          color: #333;
        }
      }
    }
  }
  
  .merchant-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .merchant-sidebar {
      width: 230px;
      height: 100%;
      position: relative;
      background-color: #fff;
      transition: width 0.3s;
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
      z-index: 5;
      
      &.collapse {
        width: 64px;
      }
      
      .sidebar-menu {
        height: calc(100% - 40px);
        border-right: none;
      }
      
      .sidebar-toggle {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-top: 1px solid #e0e0e0;
        
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
    
    .merchant-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      background-color: #f5f7fa;
      
      .breadcrumb {
        padding: 16px 20px;
        background-color: #fff;
        border-bottom: 1px solid #e0e0e0;
      }
      
      .content-container {
        flex: 1;
        padding: 20px;
        overflow: auto;
      }
    }
  }
}

/* 移除通知相关样式，聊天样式在MerchantChat组件中定义 */

/* 全局加载状态样式 */
.global-loading,
.page-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 999;
  
  .loading-icon,
  .is-loading {
    font-size: 42px;
    color: #409EFF;
    margin-bottom: 16px;
    animation: rotate 2s linear infinite;
  }
  
  p {
    font-size: 16px;
    color: #606266;
  }
  
  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>