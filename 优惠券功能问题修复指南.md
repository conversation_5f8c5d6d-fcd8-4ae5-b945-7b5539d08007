# 优惠券功能问题修复指南

## 问题概述

1. **控制台错误**: `TypeError: _ctx.getSelectedPromotionId is not a function`
2. **优惠券显示问题**: 优惠券中心有优惠券，但购物车中优惠券显示为空

## 已实施的修复方案

### 1. 修复 `getSelectedPromotionId` 函数未定义错误

**问题原因**: Vue模板中调用了不存在的函数，可能由于热更新或缓存问题导致。

**修复措施**:
- 在 `CheckoutDialog.vue` 中添加了 `getSelectedPromotionId` 函数
- 在 `CheckoutDialog.vue` 中添加了 `getSelectedCouponId` 函数
- 增强了相关函数的调试日志

**修复代码**:
```javascript
/**
 * 获取选中的促销活动ID（修复函数未定义错误）
 */
function getSelectedPromotionId(merchantId: string | number): string | number | null {
  const selectedPromotion = merchantPromotions.value[merchantId];
  const promotionId = selectedPromotion?.id || null;
  console.log(`[获取选中促销ID] 商家${merchantId}的选中促销ID:`, promotionId);
  return promotionId;
}

/**
 * 获取选中的优惠券ID（修复函数未定义错误）
 */
function getSelectedCouponId(merchantId: string | number): string | number | null {
  const selectedCoupon = selectedCoupons.value[merchantId];
  const couponId = selectedCoupon?.id || null;
  console.log(`[获取选中优惠券ID] 商家${merchantId}的选中优惠券ID:`, couponId);
  return couponId;
}
```

### 2. 增强优惠券数据加载调试

**修复措施**:
- 增强了 `loadAvailableCoupons` 函数的调试日志
- 增强了 `getMerchantCoupons` 函数的调试日志
- 添加了API响应的详细调试信息
- 添加了优惠券数据完整性验证

**关键调试日志**:
```javascript
console.log('[优惠券加载] 开始加载商家优惠券...');
console.log('[API响应] 商家优惠券API原始响应:', apiResponse);
console.log('[优惠券验证] 第一个优惠券数据结构:', firstCoupon);
```

### 3. 修复优惠券中心数据解析问题

**问题原因**: 后端返回 `list` 字段，但前端期望 `coupons` 字段。

**修复措施**:
- 修正了 `CouponCenter.vue` 中的数据解析逻辑
- 添加了字段映射处理
- 增强了错误处理和调试日志

## 测试和验证步骤

### 1. 清理浏览器缓存（重要）

由于 `getSelectedPromotionId` 错误可能与缓存有关，请执行以下步骤：

1. **硬刷新页面**: `Ctrl+F5` (Windows) 或 `Cmd+Shift+R` (Mac)
2. **清理浏览器缓存**:
   - 打开开发者工具 (F12)
   - 右键点击刷新按钮
   - 选择"清空缓存并硬性重新加载"
3. **清理本地存储**:
   - 在开发者工具中打开 Application 标签
   - 清理 Local Storage 和 Session Storage

### 2. 验证优惠券中心功能

1. 访问优惠券中心页面
2. 检查浏览器控制台，应该看到：
   ```
   === 优惠券数据结构验证 ===
   优惠券总数: X
   成功解析优惠券数据，数量: X
   ```

### 3. 验证购物车优惠券功能

1. 添加商品到购物车
2. 点击结算按钮
3. 检查浏览器控制台，应该看到：
   ```
   [监听器] 对话框打开，开始加载数据...
   [优惠券加载] 开始加载商家优惠券...
   [API响应] 商家X优惠券API原始响应: {...}
   [获取优惠券] 商家X的优惠券数量: X
   ```

### 4. 验证优惠券显示

1. 在结算弹窗中，应该能看到优惠券选择器
2. 优惠券选择器应该显示可用的优惠券
3. 选择优惠券后应该正确计算优惠金额

## 调试工具

### 1. 控制台调试命令

在浏览器控制台中执行以下命令来检查优惠券数据：

```javascript
// 检查优惠券中心数据
console.log('优惠券中心数据:', window.vue?.$data?.availableCoupons);

// 检查购物车优惠券数据
console.log('购物车优惠券数据:', window.vue?.$data?.availableCoupons);
```

### 2. 自动调试检查

修复后的代码会在组件挂载5秒后自动执行优惠券数据完整性检查，查看控制台输出：

```
=== 优惠券数据完整性检查 ===
可用优惠券数据: {...}
选中优惠券数据: {...}
```

## 常见问题排查

### 1. 如果仍然出现 `getSelectedPromotionId` 错误

- 执行硬刷新和缓存清理
- 检查是否有其他组件或模板中调用了这个函数
- 重启开发服务器

### 2. 如果优惠券仍然不显示

- 检查网络请求是否成功
- 检查API返回的数据格式
- 查看控制台的详细调试日志

### 3. 如果优惠券数据格式不正确

- 检查后端API返回的数据结构
- 确认字段映射是否正确
- 验证优惠券状态字段

## 后续优化建议

1. **错误边界处理**: 添加更完善的错误边界组件
2. **数据缓存**: 实现优惠券数据的本地缓存机制
3. **性能优化**: 优化优惠券加载的性能
4. **用户体验**: 添加加载状态和空状态提示

## 联系支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误日志
2. 网络请求的详细信息
3. 优惠券相关API的响应数据
