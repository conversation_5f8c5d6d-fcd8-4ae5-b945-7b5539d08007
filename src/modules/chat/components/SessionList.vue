<template>
  <div class="session-list">
    <!-- 会话列表头部 -->
    <div class="session-list__header">
      <h3 class="session-list__title">会话列表</h3>
      <div class="session-list__actions">
        <!-- 创建新会话按钮 -->
        <button
          class="session-list__action-btn"
          @click="handleCreateSession"
          title="创建新会话"
        >
          <svg class="session-list__icon" viewBox="0 0 24 24">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
          </svg>
        </button>
        
        <!-- 搜索按钮 -->
        <button
          class="session-list__action-btn"
          @click="showSearch = !showSearch"
          title="搜索会话"
        >
          <svg class="session-list__icon" viewBox="0 0 24 24">
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
        </button>
        
        <!-- 过滤按钮 -->
        <button
          class="session-list__action-btn"
          @click="showFilters = !showFilters"
          title="过滤会话"
        >
          <svg class="session-list__icon" viewBox="0 0 24 24">
            <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 搜索框 -->
    <div v-if="showSearch" class="session-list__search">
      <input
        v-model="searchQuery"
        type="text"
        placeholder="搜索会话..."
        class="session-list__search-input"
        @input="handleSearch"
      />
    </div>
    
    <!-- 过滤器 -->
    <div v-if="showFilters" class="session-list__filters">
      <select
        v-model="filters.status"
        class="session-list__filter-select"
        @change="handleFilterChange"
      >
        <option value="">所有状态</option>
        <option value="active">活跃</option>
        <option value="waiting">等待中</option>
        <option value="closed">已关闭</option>
        <option value="archived">已归档</option>
      </select>
      
      <select
        v-model="filters.type"
        class="session-list__filter-select"
        @change="handleFilterChange"
      >
        <option value="">所有类型</option>
        <option value="customer_service">客服咨询</option>
        <option value="technical_support">技术支持</option>
        <option value="sales">销售咨询</option>
        <option value="complaint">投诉建议</option>
      </select>
    </div>
    
    <!-- 会话统计 -->
    <div class="session-list__stats">
      <div class="session-list__stat">
        <span class="session-list__stat-label">总计:</span>
        <span class="session-list__stat-value">{{ totalSessions }}</span>
      </div>
      <div class="session-list__stat">
        <span class="session-list__stat-label">未读:</span>
        <span class="session-list__stat-value session-list__stat-value--unread">
          {{ unreadSessions }}
        </span>
      </div>
    </div>
    
    <!-- 会话列表 -->
    <div class="session-list__content">
      <div v-if="isLoading" class="session-list__loading">
        <div class="session-list__spinner"></div>
        <p>加载中...</p>
      </div>
      
      <div v-else-if="error" class="session-list__error">
        <p>{{ error }}</p>
        <button class="session-list__retry-btn" @click="loadSessions">
          重试
        </button>
      </div>
      
      <div v-else-if="filteredSessions.length === 0" class="session-list__empty">
        <div class="session-list__empty-icon">
          <svg viewBox="0 0 24 24">
            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
          </svg>
        </div>
        <p>暂无会话</p>
        <button class="session-list__create-btn" @click="handleCreateSession">
          创建新会话
        </button>
      </div>
      
      <div v-else class="session-list__items">
        <div
          v-for="session in filteredSessions"
          :key="session.id"
          class="session-list__item"
          :class="{
            'session-list__item--active': session.id.toString() === currentSessionId,
            'session-list__item--unread': hasUnreadMessages(session.id.toString())
          }"
          @click="handleSessionSelect(session)"
        >
          <!-- 会话头像 -->
          <div class="session-list__item-avatar">
            <img
              v-if="false"
              src=""
              alt=""
              class="session-list__item-avatar-img"
            />
            <div v-else class="session-list__item-avatar-placeholder">
              {{ getAvatarText(getSessionName(session)) }}
            </div>
            
            <!-- 状态指示器 -->
            <div
              class="session-list__item-status"
              :class="`session-list__item-status--${session.status}`"
            ></div>
          </div>
          
          <!-- 会话信息 -->
          <div class="session-list__item-info">
            <div class="session-list__item-header">
              <h4 class="session-list__item-name">{{ getSessionName(session) }}</h4>
              <span class="session-list__item-time">
                {{ formatTime(new Date(session.updated_at).getTime()) }}
              </span>
            </div>
            
            <div class="session-list__item-content">
              <p class="session-list__item-message">
                {{ getLastMessage(session.id.toString()) }}
              </p>
              
              <!-- 未读消息数量 -->
              <div
                v-if="getUnreadCount(session.id.toString()) > 0"
                class="session-list__item-badge"
              >
                {{ getUnreadCount(session.id.toString()) > 99 ? '99+' : getUnreadCount(session.id.toString()) }}
              </div>
            </div>
            
            <!-- 会话标签 -->
            <div v-if="false" class="session-list__item-tags">
              <!-- 暂时隐藏标签功能 -->
            </div>
          </div>
          
          <!-- 会话操作 -->
          <div class="session-list__item-actions">
            <button
              class="session-list__item-action"
              @click.stop="handleSessionPin(session)"
              :title="'置顶'"
            >
              <svg class="session-list__item-action-icon" viewBox="0 0 24 24">
                <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z"/>
              </svg>
            </button>
            
            <button
              class="session-list__item-action"
              @click.stop="handleSessionMute(session)"
              :title="'静音'"
            >
              <svg class="session-list__item-action-icon" viewBox="0 0 24 24">
                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
              </svg>
            </button>
            
            <button
              class="session-list__item-action"
              @click.stop="handleSessionMore(session)"
              title="更多操作"
            >
              <svg class="session-list__item-action-icon" viewBox="0 0 24 24">
                <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      <!-- 加载更多 -->
      <div v-if="hasMoreSessions && !isLoading" class="session-list__load-more">
        <button class="session-list__load-more-btn" @click="loadMoreSessions">
          加载更多
        </button>
      </div>
    </div>
    
    <!-- 会话操作菜单 -->
    <div v-if="showSessionMenu" class="session-list__menu" @click="showSessionMenu = false">
      <div class="session-list__menu-content" @click.stop>
        <button class="session-list__menu-item" @click="handleSessionEdit">
          <svg class="session-list__menu-icon" viewBox="0 0 24 24">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
          </svg>
          编辑会话
        </button>
        
        <button class="session-list__menu-item" @click="handleSessionArchive">
          <svg class="session-list__menu-icon" viewBox="0 0 24 24">
            <path d="M20.54 5.23l-1.39-1.68C18.88 3.21 18.47 3 18 3H6c-.47 0-.88.21-1.16.55L3.46 5.23C3.17 5.57 3 6.02 3 6.5V19c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6.5c0-.48-.17-.93-.46-1.27zM12 17.5L6.5 12H10v-2h4v2h3.5L12 17.5zM5.12 5l.81-1h12l.94 1H5.12z"/>
          </svg>
          归档会话
        </button>
        
        <button class="session-list__menu-item session-list__menu-item--danger" @click="handleSessionDelete">
          <svg class="session-list__menu-icon" viewBox="0 0 24 24">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
          删除会话
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useSessionStore, useMessageStore } from '../stores'
import type { Session } from '../types'
import { debounce } from 'lodash-es'

// Emits
interface Emits {
  sessionSelect: [session: Session]
  sessionCreate: []
}

const emit = defineEmits<Emits>()

// Stores
const sessionStore = useSessionStore()
const messageStore = useMessageStore()

// Store refs
const {
  sessions,
  currentSessionId,
  isLoading,
  error
} = storeToRefs(sessionStore)

const { unreadCounts } = storeToRefs(messageStore)

// State
const showSearch = ref(false)
const showFilters = ref(false)
const showSessionMenu = ref(false)
const selectedSession = ref<Session | null>(null)
const searchQuery = ref('')
const filters = ref({
  status: '',
  type: ''
})

// Computed
const totalSessions = computed(() => sessions.value.length)
const unreadSessions = computed(() => {
  return Array.from(unreadCounts.value.values()).reduce((sum: number, count: number) => sum + count, 0)
})

const filteredSessions = computed(() => {
  return sessions.value // 简化实现
})

const hasMoreSessions = computed(() => {
  return sessionStore.hasMoreSessions
})

// Methods
const getAvatarText = (name: string) => {
  return name ? name.charAt(0).toUpperCase() : '?'
}

const getSessionName = (session: any) => {
  return session?.title || '未知会话'
}

const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else if (diff < 604800000) { // 1周内
    return `${Math.floor(diff / 86400000)}天前`
  } else {
    return new Date(timestamp).toLocaleDateString()
  }
}

const getLastMessage = (sessionId: string) => {
  // 简化实现
  console.log('获取最后消息:', sessionId)
  return '暂无消息'
}

const hasUnreadMessages = (sessionId: string) => {
  return (unreadCounts.value.get(sessionId) || 0) > 0
}

const getUnreadCount = (sessionId: string) => {
  return unreadCounts.value.get(sessionId) || 0
}

const handleSessionSelect = (session: Session) => {
  emit('sessionSelect', session)
}

const handleCreateSession = () => {
  emit('sessionCreate')
}

const handleSearch = debounce((event: Event) => {
  const query = (event.target as HTMLInputElement).value
  sessionStore.searchSessions(query)
}, 300)

const handleFilterChange = () => {
  sessionStore.setFilters(filters)
}

const handleSessionPin = async (session: Session) => {
  try {
    await sessionStore.updateSession(session.id.toString(), {
      session_id: session.id,
      title: session.title
    })
  } catch (error) {
    console.error('置顶会话失败:', error)
  }
}

const handleSessionMute = async (session: Session) => {
  try {
    await sessionStore.updateSession(session.id.toString(), {
      session_id: session.id,
      title: session.title
    })
  } catch (error) {
    console.error('静音会话失败:', error)
  }
}

const handleSessionMore = (session: Session) => {
  selectedSession.value = session
  showSessionMenu.value = true
}

const handleSessionEdit = () => {
  // TODO: 实现编辑会话功能
  console.log('编辑会话:', selectedSession.value)
  showSessionMenu.value = false
}

const handleSessionArchive = async () => {
  if (!selectedSession.value) return
  
  try {
    await sessionStore.archiveSession(selectedSession.value.id.toString())
    showSessionMenu.value = false
  } catch (error) {
    console.error('归档会话失败:', error)
  }
}

const handleSessionDelete = async () => {
  if (!selectedSession.value) return
  
  if (confirm('确定要删除这个会话吗？此操作不可恢复。')) {
    try {
      await sessionStore.deleteSession(selectedSession.value.id.toString())
      showSessionMenu.value = false
    } catch (error) {
      console.error('删除会话失败:', error)
    }
  }
}

const loadSessions = async () => {
  try {
    await sessionStore.loadSessions()
  } catch (error) {
    console.error('加载会话列表失败:', error)
  }
}

const loadMoreSessions = async () => {
  try {
    await sessionStore.loadMoreSessions()
  } catch (error) {
    console.error('加载更多会话失败:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadSessions()
})

// Watch
watch(
  () => searchQuery.value,
  (query) => {
    if (query.trim()) {
      sessionStore.searchSessions(query)
    } else {
      sessionStore.clearSearchResults()
    }
  }
)
</script>

<style scoped>
.session-list {
  @apply h-full flex flex-col bg-white;
}

.session-list__header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.session-list__title {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.session-list__actions {
  @apply flex items-center space-x-2;
}

.session-list__action-btn {
  @apply p-2 rounded-md hover:bg-gray-100 transition-colors;
}

.session-list__icon {
  @apply w-4 h-4 fill-current text-gray-600;
}

.session-list__search {
  @apply p-4 border-b border-gray-200;
}

.session-list__search-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.session-list__filters {
  @apply p-4 border-b border-gray-200 space-y-2;
}

.session-list__filter-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.session-list__stats {
  @apply flex items-center justify-between px-4 py-2 bg-gray-50 border-b border-gray-200;
}

.session-list__stat {
  @apply flex items-center space-x-1;
}

.session-list__stat-label {
  @apply text-sm text-gray-600;
}

.session-list__stat-value {
  @apply text-sm font-semibold text-gray-900;
}

.session-list__stat-value--unread {
  @apply text-red-600;
}

.session-list__content {
  @apply flex-1 overflow-y-auto;
}

.session-list__loading {
  @apply flex flex-col items-center justify-center p-8;
}

.session-list__spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4;
}

.session-list__error {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.session-list__retry-btn {
  @apply mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors;
}

.session-list__empty {
  @apply flex flex-col items-center justify-center p-8 text-center;
}

.session-list__empty-icon {
  @apply w-16 h-16 text-gray-400 mb-4;
}

.session-list__empty-icon svg {
  @apply w-full h-full fill-current;
}

.session-list__create-btn {
  @apply mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors;
}

.session-list__items {
  @apply divide-y divide-gray-200;
}

.session-list__item {
  @apply flex items-center p-4 hover:bg-gray-50 cursor-pointer transition-colors relative;
}

.session-list__item--active {
  @apply bg-blue-50 border-r-4 border-blue-600;
}

.session-list__item--unread {
  @apply bg-blue-25;
}

.session-list__item-avatar {
  @apply relative w-12 h-12 rounded-full overflow-hidden mr-3 flex-shrink-0;
}

.session-list__item-avatar-img {
  @apply w-full h-full object-cover;
}

.session-list__item-avatar-placeholder {
  @apply w-full h-full bg-blue-500 text-white flex items-center justify-center font-semibold;
}

.session-list__item-status {
  @apply absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white;
}

.session-list__item-status--active {
  @apply bg-green-500;
}

.session-list__item-status--waiting {
  @apply bg-yellow-500;
}

.session-list__item-status--closed {
  @apply bg-gray-400;
}

.session-list__item-status--archived {
  @apply bg-gray-300;
}

.session-list__item-info {
  @apply flex-1 min-w-0;
}

.session-list__item-header {
  @apply flex items-center justify-between mb-1;
}

.session-list__item-name {
  @apply text-sm font-semibold text-gray-900 truncate m-0;
}

.session-list__item-time {
  @apply text-xs text-gray-500 flex-shrink-0 ml-2;
}

.session-list__item-content {
  @apply flex items-center justify-between;
}

.session-list__item-message {
  @apply text-sm text-gray-600 truncate m-0 flex-1;
}

.session-list__item-badge {
  @apply ml-2 px-2 py-1 bg-red-500 text-white text-xs rounded-full min-w-[20px] text-center;
}

.session-list__item-tags {
  @apply flex items-center space-x-1 mt-2;
}

.session-list__item-tag {
  @apply px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded;
}

.session-list__item-tag-more {
  @apply text-xs text-gray-500;
}

.session-list__item-actions {
  @apply flex items-center space-x-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity;
}

.session-list__item:hover .session-list__item-actions {
  @apply opacity-100;
}

.session-list__item-action {
  @apply p-1 rounded hover:bg-gray-200 transition-colors;
}

.session-list__item-action-icon {
  @apply w-4 h-4 fill-current text-gray-600;
}

.session-list__load-more {
  @apply p-4 text-center border-t border-gray-200;
}

.session-list__load-more-btn {
  @apply px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors;
}

.session-list__menu {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.session-list__menu-content {
  @apply bg-white rounded-lg shadow-lg p-2 min-w-[200px];
}

.session-list__menu-item {
  @apply w-full flex items-center px-4 py-2 text-left hover:bg-gray-100 rounded-md transition-colors;
}

.session-list__menu-item--danger {
  @apply text-red-600 hover:bg-red-50;
}

.session-list__menu-icon {
  @apply w-4 h-4 fill-current mr-3;
}
</style>