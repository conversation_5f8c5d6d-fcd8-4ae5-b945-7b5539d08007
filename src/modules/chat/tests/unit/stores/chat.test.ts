/**
 * 聊天 Store 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useChatStore } from '../../../stores/chat'
import type { User, ChatState } from '../../../types'

// Mock WebSocket
const mockWebSocket = {
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: WebSocket.OPEN
}

// Mock ChatClient
vi.mock('../../../services/client', () => ({
  ChatClient: vi.fn().mockImplementation(() => ({
    connect: vi.fn().mockResolvedValue(undefined),
    disconnect: vi.fn(),
    sendMessage: vi.fn().mockResolvedValue({ id: '1', content: 'test' }),
    joinSession: vi.fn().mockResolvedValue(undefined),
    leaveSession: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn()
  }))
}))

// Mock API
vi.mock('../../../api', () => ({
  chatApi: {
    getCurrentUser: vi.fn().mockResolvedValue({
      id: 'user1',
      username: 'testuser',
      nickname: 'Test User',
      avatar: 'avatar.jpg',
      status: 'online'
    }),
    updateUserStatus: vi.fn().mockResolvedValue(undefined)
  }
}))

describe('Chat Store', () => {
  let store: ReturnType<typeof useChatStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useChatStore()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.isConnected).toBe(false)
      expect(store.isConnecting).toBe(false)
      expect(store.currentUser).toBeNull()
      expect(store.onlineUsers).toEqual([])
      expect(store.userStatus).toBe('offline')
      expect(store.connectionError).toBeNull()
      expect(store.lastActivity).toBeNull()
    })
  })

  describe('用户管理', () => {
    const mockUser: User = {
      id: 'user1',
      username: 'testuser',
      nickname: 'Test User',
      avatar: 'avatar.jpg',
      email: '<EMAIL>',
      status: 'online',
      lastSeen: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    it('应该能够设置当前用户', () => {
      store.setCurrentUser(mockUser)
      expect(store.currentUser).toEqual(mockUser)
    })

    it('应该能够更新用户状态', async () => {
      store.setCurrentUser(mockUser)
      await store.updateUserStatus('away')
      expect(store.userStatus).toBe('away')
    })

    it('应该能够添加在线用户', () => {
      store.addOnlineUser(mockUser)
      expect(store.onlineUsers).toContain(mockUser)
    })

    it('应该能够移除在线用户', () => {
      store.addOnlineUser(mockUser)
      store.removeOnlineUser('user1')
      expect(store.onlineUsers).not.toContain(mockUser)
    })

    it('应该能够更新在线用户信息', () => {
      store.addOnlineUser(mockUser)
      const updatedUser = { ...mockUser, nickname: 'Updated User' }
      store.updateOnlineUser('user1', updatedUser)
      
      const user = store.onlineUsers.find(u => u.id === 'user1')
      expect(user?.nickname).toBe('Updated User')
    })
  })

  describe('连接管理', () => {
    it('应该能够初始化聊天客户端', async () => {
      await store.initializeClient()
      expect(store.client).toBeDefined()
    })

    it('应该能够连接到服务器', async () => {
      await store.initializeClient()
      await store.connect()
      expect(store.isConnected).toBe(true)
      expect(store.isConnecting).toBe(false)
    })

    it('应该能够断开连接', async () => {
      await store.initializeClient()
      await store.connect()
      store.disconnect()
      expect(store.isConnected).toBe(false)
    })

    it('应该能够重新连接', async () => {
      await store.initializeClient()
      await store.reconnect()
      expect(store.isConnected).toBe(true)
    })
  })

  describe('错误处理', () => {
    it('应该能够设置连接错误', () => {
      const error = new Error('Connection failed')
      store.setConnectionError(error)
      expect(store.connectionError).toBe(error)
    })

    it('应该能够清除错误', () => {
      const error = new Error('Connection failed')
      store.setConnectionError(error)
      store.clearError()
      expect(store.connectionError).toBeNull()
    })
  })

  describe('活动跟踪', () => {
    it('应该能够更新最后活动时间', () => {
      const now = new Date()
      store.updateLastActivity()
      expect(store.lastActivity).toBeInstanceOf(Date)
      expect(store.lastActivity!.getTime()).toBeGreaterThanOrEqual(now.getTime())
    })

    it('应该能够检查用户是否活跃', () => {
      store.updateLastActivity()
      expect(store.isUserActive).toBe(true)
      
      // 模拟过期的活动时间
      const oldTime = new Date(Date.now() - 20 * 60 * 1000) // 20分钟前
      store.lastActivity = oldTime
      expect(store.isUserActive).toBe(false)
    })
  })

  describe('Getters', () => {
    it('应该正确计算在线用户数量', () => {
      const user1: User = { id: '1', username: 'user1', status: 'online' } as User
      const user2: User = { id: '2', username: 'user2', status: 'online' } as User
      
      store.addOnlineUser(user1)
      store.addOnlineUser(user2)
      
      expect(store.onlineUserCount).toBe(2)
    })

    it('应该正确获取按状态分组的用户', () => {
      const onlineUser: User = { id: '1', username: 'user1', status: 'online' } as User
      const awayUser: User = { id: '2', username: 'user2', status: 'away' } as User
      const busyUser: User = { id: '3', username: 'user3', status: 'busy' } as User
      
      store.addOnlineUser(onlineUser)
      store.addOnlineUser(awayUser)
      store.addOnlineUser(busyUser)
      
      const grouped = store.usersByStatus
      expect(grouped.online).toContain(onlineUser)
      expect(grouped.away).toContain(awayUser)
      expect(grouped.busy).toContain(busyUser)
    })

    it('应该正确检查连接状态', () => {
      expect(store.connectionStatus).toBe('disconnected')
      
      store.isConnecting = true
      expect(store.connectionStatus).toBe('connecting')
      
      store.isConnecting = false
      store.isConnected = true
      expect(store.connectionStatus).toBe('connected')
      
      store.setConnectionError(new Error('Test error'))
      expect(store.connectionStatus).toBe('error')
    })
  })

  describe('状态重置', () => {
    it('应该能够重置状态', () => {
      // 设置一些状态
      store.isConnected = true
      store.setCurrentUser({ id: '1', username: 'test' } as User)
      store.addOnlineUser({ id: '2', username: 'user2' } as User)
      store.setConnectionError(new Error('Test'))
      
      // 重置状态
      store.resetState()
      
      // 验证状态已重置
      expect(store.isConnected).toBe(false)
      expect(store.isConnecting).toBe(false)
      expect(store.currentUser).toBeNull()
      expect(store.onlineUsers).toEqual([])
      expect(store.connectionError).toBeNull()
      expect(store.lastActivity).toBeNull()
    })
  })

  describe('持久化', () => {
    it('应该能够保存状态到本地存储', () => {
      const mockUser: User = {
        id: 'user1',
        username: 'testuser',
        nickname: 'Test User'
      } as User
      
      store.setCurrentUser(mockUser)
      store.updateUserStatus('away')
      
      // 验证状态已保存（这里需要mock localStorage）
      expect(localStorage.getItem).toHaveBeenCalled()
    })
  })
})