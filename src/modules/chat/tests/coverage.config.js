/**
 * 测试覆盖率配置文件
 * 为聊天模块提供详细的代码覆盖率配置
 */

module.exports = {
  // 覆盖率收集配置
  collectCoverage: true,
  collectCoverageFrom: [
    // 包含的文件
    'src/modules/chat/**/*.{js,ts,vue}',
    'src/modules/chat/components/**/*.vue',
    'src/modules/chat/stores/**/*.ts',
    'src/modules/chat/services/**/*.ts',
    'src/modules/chat/utils/**/*.ts',
    'src/modules/chat/composables/**/*.ts',
    'src/modules/chat/types/**/*.ts',
    
    // 排除的文件
    '!src/modules/chat/tests/**',
    '!src/modules/chat/**/*.test.{js,ts}',
    '!src/modules/chat/**/*.spec.{js,ts}',
    '!src/modules/chat/**/*.d.ts',
    '!src/modules/chat/**/index.ts',
    '!src/modules/chat/main.ts',
    '!src/modules/chat/router/index.ts',
    '!src/modules/chat/plugins/**',
    '!src/modules/chat/assets/**',
    '!src/modules/chat/public/**',
    '!src/modules/chat/dist/**',
    '!src/modules/chat/node_modules/**'
  ],
  
  // 覆盖率报告配置
  coverageReporters: [
    'text',           // 控制台文本报告
    'text-summary',   // 控制台摘要报告
    'html',           // HTML 报告
    'lcov',           // LCOV 报告（用于 CI/CD）
    'json',           // JSON 报告
    'json-summary',   // JSON 摘要报告
    'cobertura',      // Cobertura XML 报告
    'clover'          // Clover XML 报告
  ],
  
  // 覆盖率输出目录
  coverageDirectory: 'src/modules/chat/tests/coverage',
  
  // 覆盖率阈值配置
  coverageThreshold: {
    global: {
      branches: 80,     // 分支覆盖率
      functions: 85,    // 函数覆盖率
      lines: 85,        // 行覆盖率
      statements: 85    // 语句覆盖率
    },
    
    // 组件覆盖率要求
    'src/modules/chat/components/**/*.vue': {
      branches: 75,
      functions: 80,
      lines: 80,
      statements: 80
    },
    
    // Store 覆盖率要求
    'src/modules/chat/stores/**/*.ts': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    },
    
    // 服务层覆盖率要求
    'src/modules/chat/services/**/*.ts': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    },
    
    // 工具函数覆盖率要求
    'src/modules/chat/utils/**/*.ts': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95
    },
    
    // Composables 覆盖率要求
    'src/modules/chat/composables/**/*.ts': {
      branches: 80,
      functions: 85,
      lines: 85,
      statements: 85
    }
  },
  
  // 覆盖率提供者配置
  coverageProvider: 'v8',
  
  // 覆盖率路径映射
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/tests/',
    '/coverage/',
    '\\.d\\.ts$',
    '\\.test\\.(js|ts)$',
    '\\.spec\\.(js|ts)$'
  ],
  
  // HTML 报告配置
  coverageReporterOptions: {
    html: {
      subdir: 'html',
      skipCovered: false,
      skipEmpty: false
    },
    lcov: {
      subdir: 'lcov',
      file: 'lcov.info'
    },
    json: {
      subdir: 'json',
      file: 'coverage.json'
    },
    'json-summary': {
      subdir: 'json',
      file: 'coverage-summary.json'
    },
    text: {
      maxCols: 120,
      skipCovered: false,
      skipEmpty: false,
      skipFull: false
    },
    cobertura: {
      subdir: 'cobertura',
      file: 'cobertura-coverage.xml'
    },
    clover: {
      subdir: 'clover',
      file: 'clover.xml'
    }
  },
  
  // 自定义覆盖率配置
  customCoverageConfig: {
    // 关键文件列表（需要更高覆盖率）
    criticalFiles: [
      'src/modules/chat/stores/messageStore.ts',
      'src/modules/chat/stores/sessionStore.ts',
      'src/modules/chat/services/chatService.ts',
      'src/modules/chat/services/websocketService.ts',
      'src/modules/chat/utils/messageUtils.ts',
      'src/modules/chat/utils/fileUtils.ts'
    ],
    
    // 关键文件覆盖率要求
    criticalThreshold: {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95
    },
    
    // 忽略的文件模式
    ignorePatterns: [
      '**/*.mock.ts',
      '**/*.fixture.ts',
      '**/test-utils.ts',
      '**/mock-*.ts'
    ],
    
    // 覆盖率徽章配置
    badges: {
      enabled: true,
      outputDir: 'src/modules/chat/tests/coverage/badges',
      style: 'flat-square',
      colors: {
        excellent: '#4c1',  // >= 90%
        good: '#97ca00',     // >= 80%
        ok: '#a4a61d',       // >= 70%
        poor: '#dfb317',     // >= 60%
        terrible: '#e05d44'  // < 60%
      }
    },
    
    // 覆盖率趋势配置
    trends: {
      enabled: true,
      historyFile: 'src/modules/chat/tests/coverage/history.json',
      maxHistory: 50,
      generateChart: true
    },
    
    // 覆盖率通知配置
    notifications: {
      enabled: false,
      webhook: process.env.COVERAGE_WEBHOOK_URL,
      threshold: {
        decrease: 5,  // 覆盖率下降超过5%时通知
        minimum: 80   // 覆盖率低于80%时通知
      }
    }
  },
  
  // 环境特定配置
  environments: {
    development: {
      collectCoverage: false,
      coverageReporters: ['text']
    },
    
    test: {
      collectCoverage: true,
      coverageReporters: ['text', 'html', 'lcov']
    },
    
    ci: {
      collectCoverage: true,
      coverageReporters: ['text', 'lcov', 'json', 'cobertura'],
      coverageThreshold: {
        global: {
          branches: 85,
          functions: 90,
          lines: 90,
          statements: 90
        }
      }
    },
    
    production: {
      collectCoverage: false
    }
  },
  
  // 覆盖率脚本配置
  scripts: {
    // 生成覆盖率报告
    generate: {
      command: 'jest --coverage --coverageDirectory=src/modules/chat/tests/coverage',
      description: '生成完整的覆盖率报告'
    },
    
    // 查看覆盖率报告
    view: {
      command: 'open src/modules/chat/tests/coverage/html/index.html',
      description: '在浏览器中查看覆盖率报告'
    },
    
    // 覆盖率检查
    check: {
      command: 'jest --coverage --passWithNoTests --silent',
      description: '检查覆盖率是否达到阈值'
    },
    
    // 清理覆盖率文件
    clean: {
      command: 'rm -rf src/modules/chat/tests/coverage',
      description: '清理覆盖率输出文件'
    },
    
    // 覆盖率徽章生成
    badges: {
      command: 'node scripts/generate-coverage-badges.js',
      description: '生成覆盖率徽章'
    },
    
    // 覆盖率趋势分析
    trends: {
      command: 'node scripts/analyze-coverage-trends.js',
      description: '分析覆盖率趋势'
    }
  },
  
  // 集成配置
  integrations: {
    // SonarQube 集成
    sonarqube: {
      enabled: false,
      lcovReportPath: 'src/modules/chat/tests/coverage/lcov/lcov.info',
      exclusions: [
        'src/modules/chat/tests/**',
        'src/modules/chat/**/*.test.ts',
        'src/modules/chat/**/*.spec.ts'
      ]
    },
    
    // Codecov 集成
    codecov: {
      enabled: false,
      token: process.env.CODECOV_TOKEN,
      file: 'src/modules/chat/tests/coverage/lcov/lcov.info'
    },
    
    // Coveralls 集成
    coveralls: {
      enabled: false,
      token: process.env.COVERALLS_TOKEN,
      file: 'src/modules/chat/tests/coverage/lcov/lcov.info'
    }
  },
  
  // 高级配置
  advanced: {
    // 并行覆盖率收集
    parallel: true,
    
    // 缓存覆盖率数据
    cache: true,
    cacheDirectory: 'node_modules/.cache/jest-coverage',
    
    // 覆盖率数据压缩
    compress: true,
    
    // 增量覆盖率
    incremental: {
      enabled: false,
      baseRef: 'main',
      changedFilesOnly: true
    },
    
    // 覆盖率热图
    heatmap: {
      enabled: false,
      outputFile: 'src/modules/chat/tests/coverage/heatmap.html'
    },
    
    // 覆盖率差异报告
    diff: {
      enabled: false,
      baseFile: 'coverage-base.json',
      outputFile: 'coverage-diff.html'
    }
  }
}