/**
 * 简化的会话状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  SessionType,
  SessionStatus,
  SessionPriority
} from '../types/session'
import type {
  Session,
  SessionParticipant,
  // SessionSettings, // 未使用，注释掉
  CreateSessionRequest,
  UpdateSessionRequest,
  GetSessionsRequest
} from '../types/session'

export const useSessionStore = defineStore('session', () => {
  // State
  const sessions = ref<Session[]>([])
  const sessionCache = ref(new Map<string, Session>())
  const currentSessionId = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const participants = ref(new Map<string, SessionParticipant[]>())
  const statistics = ref<any>(null)
  const selectedSessions = ref(new Set<string>())
  const pagination = ref({
    page: 1,
    per_page: 20,
    total: 0,
    has_more: false
  })
  const searchResults = ref<Session[]>([])
  const searchQuery = ref('')
  const searchLoading = ref(false)
  const filters = ref<any>({})
  const sortBy = ref('updated_at')
  const sortOrder = ref<'asc' | 'desc'>('desc')

  // Getters
  const currentSession = computed(() => {
    if (!currentSessionId.value) return null
    return sessionCache.value.get(currentSessionId.value) || null
  })

  const filteredSessions = computed(() => {
    return sessions.value.filter(session => {
      // 简化的过滤逻辑
      if (filters.value.status && session.status !== filters.value.status) {
        return false
      }
      if (filters.value.type && session.type !== filters.value.type) {
        return false
      }
      return true
    })
  })

  const hasMoreSessions = computed(() => {
    return pagination.value.has_more
  })

  // Actions
  const createSession = async (request: CreateSessionRequest) => {
    try {
      isLoading.value = true
      error.value = null

      // 简化会话创建
      const session: any = {
        id: Date.now(),
        type: request.type || SessionType.CUSTOMER_SERVICE,
        status: SessionStatus.ACTIVE,
        priority: SessionPriority.NORMAL,
        title: request.title || '新会话',
        participants: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // 添加必要的默认属性
        creator_id: 1,
        creator_type: 'user',
        unread_count: 0,
        last_message_at: new Date().toISOString()
      }

      sessions.value.unshift(session)
      sessionCache.value.set(session.id.toString(), session)

      return session
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建会话失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const loadSessions = async (_request: GetSessionsRequest = {}) => {
    try {
      isLoading.value = true
      error.value = null

      // 简化模拟会话数据
      const mockSessions: any[] = [
        {
          id: 1,
          type: SessionType.CUSTOMER_SERVICE,
          status: SessionStatus.ACTIVE,
          priority: SessionPriority.NORMAL,
          title: '客服会话 1',
          participants: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          service_type: 'general',
          creator_id: 1,
          creator_type: 'user',
          unread_count: 0,
          last_message_at: new Date().toISOString()
        },
        {
          id: 2,
          type: SessionType.PRESALE_CONSULTATION,
          status: SessionStatus.INACTIVE,
          priority: SessionPriority.HIGH,
          title: '售前咨询 1',
          participants: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          merchant_id: 1,
          merchant_name: '测试商户',
          customer_info: { id: 1, name: '测试客户' },
          creator_id: 1,
          creator_type: 'user',
          unread_count: 0,
          last_message_at: new Date().toISOString()
        }
      ]

      sessions.value = mockSessions
      mockSessions.forEach(session => {
        sessionCache.value.set(session.id.toString(), session)
      })

      pagination.value = {
        page: 1,
        per_page: 20,
        total: mockSessions.length,
        has_more: false
      }

      return mockSessions
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载会话失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateSession = async (sessionId: string, request: UpdateSessionRequest) => {
    try {
      const session = sessionCache.value.get(sessionId)
      if (session) {
        Object.assign(session, request)
        session.updated_at = new Date().toISOString()
        sessionCache.value.set(sessionId, session)
      }
      return session
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新会话失败'
      throw err
    }
  }

  const deleteSession = async (sessionId: string) => {
    try {
      sessions.value = sessions.value.filter(s => s.id.toString() !== sessionId)
      sessionCache.value.delete(sessionId)
      if (currentSessionId.value === sessionId) {
        currentSessionId.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除会话失败'
      throw err
    }
  }

  const searchSessions = async (query: string) => {
    try {
      searchLoading.value = true
      searchQuery.value = query
      
      if (!query.trim()) {
        searchResults.value = []
        return
      }

      // 简化的搜索逻辑
      searchResults.value = sessions.value.filter(session =>
        session.title?.toLowerCase().includes(query.toLowerCase())
      )
    } catch (err) {
      error.value = err instanceof Error ? err.message : '搜索失败'
      throw err
    } finally {
      searchLoading.value = false
    }
  }

  const clearSearchResults = () => {
    searchResults.value = []
    searchQuery.value = ''
  }

  const setFilters = (newFilters: any) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const loadMoreSessions = async () => {
    if (!hasMoreSessions.value) return
    // 简化实现
    console.log('加载更多会话')
  }

  const archiveSession = async (sessionId: string) => {
    return updateSession(sessionId, {
      session_id: Number(sessionId),
      status: SessionStatus.ARCHIVED
    })
  }

  const joinSession = async (sessionId: string) => {
    console.log('加入会话:', sessionId)
  }

  const leaveSession = async (sessionId: string) => {
    console.log('离开会话:', sessionId)
  }

  const loadSessionParticipants = async (sessionId: string) => {
    participants.value.set(sessionId, [])
  }

  const loadSessionStats = async (sessionId: string) => {
    console.log('加载会话统计:', sessionId)
  }

  return {
    // State
    sessions,
    sessionCache,
    currentSessionId,
    isLoading,
    error,
    participants,
    statistics,
    selectedSessions,
    pagination,
    searchResults,
    searchQuery,
    searchLoading,
    filters,
    sortBy,
    sortOrder,
    
    // Getters
    currentSession,
    filteredSessions,
    hasMoreSessions,
    
    // Actions
    createSession,
    loadSessions,
    updateSession,
    deleteSession,
    searchSessions,
    clearSearchResults,
    setFilters,
    loadMoreSessions,
    archiveSession,
    joinSession,
    leaveSession,
    loadSessionParticipants,
    loadSessionStats
  }
})
