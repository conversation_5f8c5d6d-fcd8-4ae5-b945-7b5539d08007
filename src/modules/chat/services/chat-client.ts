/**
 * 聊天客户端核心服务
 */

import type {
  ChatClientConfig,
  ChatUser,
  ChatStatistics,
  ChatSettings,
  QuickReply,
  MessageTemplate
} from '../types/chat'
import { ChatClientStatus, UserOnlineStatus } from '../types/chat'
import type {
  Message,
  SendMessageRequest,
  SendMessageResponse,
  GetMessagesRequest,
  GetMessagesResponse,
  MarkMessageReadRequest,
  MarkMessageReadResponse,
  SenderType
} from '../types/message'
import type {
  Session,
  CreateSessionRequest,
  CreateSessionResponse,
  GetSessionsRequest,
  GetSessionsResponse,
  UpdateSessionRequest,
  UpdateSessionResponse
} from '../types/session'
import type {
  WebSocketMessage,
  WebSocketMessageType,
  ChatMessage,
  UserStatusMessage,
  SessionMessage,
  NotificationMessage
} from '../types/websocket'
// 移除未使用的导入

import { WebSocketManager, createWebSocketManager } from './websocket'
import { MessageService } from './message'
import { SessionService } from './session'
import { NotificationService } from './notification'
import { FileService } from './file'
import { EventEmitter } from '../utils/event-emitter'
import { Logger } from '../utils/logger'
import { EVENT_NAMES } from '../constants'

/**
 * 聊天客户端类
 */
export class ChatClient extends EventEmitter {
  private config: ChatClientConfig
  private status: ChatClientStatus = ChatClientStatus.DISCONNECTED
  private wsManager: WebSocketManager
  private messageService: MessageService
  private sessionService: SessionService
  private notificationService: NotificationService
  private fileService: FileService
  private logger: Logger
  private currentUser: ChatUser | null = null
  private activeSessions: Map<number, Session> = new Map()
  private onlineUsers: Map<number, ChatUser> = new Map()
  private statistics: ChatStatistics
  private settings: ChatSettings
  private quickReplies: QuickReply[] = []
  private messageTemplates: MessageTemplate[] = []
  // private _reconnectAttempts = 0 // 暂时注释掉未使用的变量
  private heartbeatInterval: number | null = null

  constructor(config: Partial<ChatClientConfig>) {
    super()
    this.config = this.mergeConfig(config)
    this.logger = new Logger('ChatClient')
    this.statistics = this.initStatistics()
    this.settings = this.initSettings()
    
    // 初始化服务
    this.wsManager = createWebSocketManager({
      url: this.config.wsBaseUrl,
      auth: {
        token: this.config.userToken,
        autoAuth: true,
        authTimeout: 10000
      },
      reconnect: {
        enabled: this.config.autoReconnect ?? true,
        maxAttempts: this.config.maxReconnectAttempts || 5,
        interval: 1000,
        backoff: 'exponential',
        maxInterval: 30000
      },
      heartbeat: {
        enabled: true,
        interval: 30000,
        timeout: 10000,
        maxMissed: 3
      },
      debug: this.config.debug
    })
    
    this.messageService = new MessageService(this.config.apiBaseUrl)
    this.sessionService = new SessionService(this.config.apiBaseUrl)
    this.notificationService = new NotificationService(this.config.apiBaseUrl)
    this.fileService = new FileService(this.config.apiBaseUrl)
    
    this.setupEventListeners()
  }

  /**
   * 合并配置
   */
  private mergeConfig(config: Partial<ChatClientConfig>): ChatClientConfig {
    return {
      apiBaseUrl: config.apiBaseUrl || '/api/v1/chat',
      wsBaseUrl: config.wsBaseUrl || 'ws://localhost:8080/ws',
      userToken: config.userToken || '',
      userId: config.userId || 0,
      userType: config.userType || 'user' as SenderType,
      autoReconnect: config.autoReconnect ?? true,
      maxReconnectAttempts: config.maxReconnectAttempts ?? 5,
      reconnectInterval: config.reconnectInterval ?? 1000,
      heartbeatInterval: config.heartbeatInterval ?? 30000,
      messageQueueSize: config.messageQueueSize ?? 100,
      enableNotifications: config.enableNotifications ?? true,
      enableSound: config.enableSound ?? true,
      debug: config.debug ?? false
    }
  }

  /**
   * 初始化统计信息
   */
  private initStatistics(): ChatStatistics {
    return {
      total_sessions: 0,
      active_sessions: 0,
      total_messages: 0,
      unread_messages: 0,
      online_users: 0,
      response_time: {
        average: 0,
        median: 0,
        percentile_95: 0
      },
      message_types: {},
      peak_hours: [],
      user_satisfaction: {
        average_rating: 0,
        total_ratings: 0,
        rating_distribution: {}
      }
    }
  }

  /**
   * 初始化设置
   */
  private initSettings(): ChatSettings {
    return {
      notifications: {
        enabled: true,
        sound: true,
        desktop: true,
        email: false,
        push: true,
        quiet_hours: {
          enabled: false,
          start_time: '22:00',
          end_time: '08:00'
        }
      },
      messages: {
        auto_read: false,
        show_typing_indicator: true,
        show_read_receipts: true,
        message_preview_length: 100,
        auto_delete_after_days: 0
      },
      privacy: {
        show_online_status: true,
        allow_stranger_messages: false,
        block_list: [],
        auto_accept_friend_requests: false
      },
      ui: {
        theme: 'light',
        font_size: 'medium',
        compact_mode: false,
        show_avatars: true,
        show_timestamps: true,
        emoji_style: 'native'
      },
      files: {
        auto_download_images: true,
        auto_download_files: false,
        max_file_size: 10 * 1024 * 1024, // 10MB
        allowed_file_types: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        save_location: 'downloads'
      }
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // WebSocket事件
    this.wsManager.on(EVENT_NAMES.WS_CONNECTED, this.handleWebSocketConnected.bind(this))
    this.wsManager.on(EVENT_NAMES.WS_DISCONNECTED, this.handleWebSocketDisconnected.bind(this))
    this.wsManager.on(EVENT_NAMES.WS_ERROR, this.handleWebSocketError.bind(this))
    this.wsManager.on(EVENT_NAMES.WS_MESSAGE_RECEIVED, this.handleWebSocketMessage.bind(this))
    this.wsManager.on(EVENT_NAMES.WS_RECONNECTING, this.handleWebSocketReconnecting.bind(this))
  }

  /**
   * 连接聊天服务
   */
  async connect(): Promise<void> {
    try {
      this.setStatus(ChatClientStatus.CONNECTING)
      this.logger.info('Connecting to chat service...')
      
      // 连接WebSocket
      await this.wsManager.connect()
      
      // 获取用户信息
      await this.loadUserProfile()
      
      // 加载初始数据
      await this.loadInitialData()
      
      this.setStatus(ChatClientStatus.CONNECTED)
      this.logger.info('Connected to chat service successfully')

      // this.emit(EVENT_NAMES.CHAT_CLIENT_CONNECTED) // 暂时注释掉

    } catch (error) {
      this.setStatus(ChatClientStatus.ERROR)
      this.logger.error('Failed to connect to chat service:', error)
      // this.emit(EVENT_NAMES.CHAT_CLIENT_ERROR, this.createError('CONNECTION_FAILED', error)) // 暂时注释掉
      throw error
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    try {
      this.setStatus(ChatClientStatus.CONNECTING) // 使用 CONNECTING 作为临时状态
      this.logger.info('Disconnecting from chat service...')
      
      // 清理定时器
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
      }
      
      // 断开WebSocket
      this.wsManager.disconnect()
      
      // 清理数据
      this.activeSessions.clear()
      this.onlineUsers.clear()
      this.currentUser = null
      
      this.setStatus(ChatClientStatus.DISCONNECTED)
      this.logger.info('Disconnected from chat service')

      // this.emit(EVENT_NAMES.CHAT_CLIENT_DISCONNECTED) // 暂时注释掉
      
    } catch (error) {
      this.logger.error('Error during disconnect:', error)
      throw error
    }
  }

  /**
   * 发送消息
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      if (!this.isConnected()) {
        throw new Error('Chat client is not connected')
      }
      
      this.logger.debug('Sending message:', request)
      
      // 通过API发送消息
      const response = await this.messageService.sendMessage(request)
      
      // 通过WebSocket发送实时消息
      if (response.success && response.message) {
        // 简化 WebSocket 消息发送 - 暂时注释掉
        // const wsMessage = { ... }
        // await this.wsManager.sendMessage(wsMessage)
      }
      
      this.statistics.total_messages++
      // this.statistics.last_activity = Date.now() // 属性不存在，暂时注释掉
      
      this.emit(EVENT_NAMES.MESSAGE_SENT, response.message)
      
      return response
      
    } catch (error) {
      this.logger.error('Failed to send message:', error)
      // this.emit(EVENT_NAMES.MESSAGE_FAILED, this.createError('MESSAGE_SEND_FAILED', error)) // 暂时注释掉
      throw error
    }
  }

  /**
   * 获取消息历史
   */
  async getMessages(request: GetMessagesRequest): Promise<GetMessagesResponse> {
    try {
      this.logger.debug('Getting messages:', request)
      return await this.messageService.getMessages(request)
    } catch (error) {
      this.logger.error('Failed to get messages:', error)
      throw error
    }
  }

  /**
   * 标记消息已读
   */
  async markMessageRead(request: MarkMessageReadRequest): Promise<MarkMessageReadResponse> {
    try {
      this.logger.debug('Marking message as read:', request)
      const response = await this.messageService.markMessageRead(request)
      
      this.emit(EVENT_NAMES.MESSAGE_READ, request)
      
      return response
    } catch (error) {
      this.logger.error('Failed to mark message as read:', error)
      throw error
    }
  }

  /**
   * 创建会话
   */
  async createSession(request: CreateSessionRequest): Promise<CreateSessionResponse> {
    try {
      this.logger.debug('Creating session:', request)
      
      const response = await this.sessionService.createSession(request)
      
      if (response.success && response.session) {
        this.activeSessions.set(response.session.id, response.session)
        this.statistics.total_sessions++
        this.statistics.active_sessions = this.activeSessions.size
        
        this.emit(EVENT_NAMES.SESSION_CREATED, response.session)
      }
      
      return response
      
    } catch (error) {
      this.logger.error('Failed to create session:', error)
      throw error
    }
  }

  /**
   * 获取会话列表
   */
  async getSessions(request: GetSessionsRequest): Promise<GetSessionsResponse> {
    try {
      this.logger.debug('Getting sessions:', request)
      return await this.sessionService.getSessions(request)
    } catch (error) {
      this.logger.error('Failed to get sessions:', error)
      throw error
    }
  }

  /**
   * 更新会话
   */
  async updateSession(sessionId: number, request: UpdateSessionRequest): Promise<UpdateSessionResponse> {
    try {
      this.logger.debug('Updating session:', { sessionId, request })
      
      const response = await this.sessionService.updateSession(sessionId, request)
      
      if (response.success && response.session) {
        this.activeSessions.set(sessionId, response.session)
        this.emit(EVENT_NAMES.SESSION_UPDATED, response.session)
      }
      
      return response
      
    } catch (error) {
      this.logger.error('Failed to update session:', error)
      throw error
    }
  }

  /**
   * 关闭会话
   */
  async closeSession(sessionId: number, reason?: string): Promise<void> {
    try {
      this.logger.debug('Closing session:', { sessionId, reason })
      
      await this.sessionService.closeSession(sessionId, reason)
      
      const session = this.activeSessions.get(sessionId)
      if (session) {
        this.activeSessions.delete(sessionId)
        // this.statistics.total_sessions_closed++ // 属性不存在
        this.statistics.active_sessions = this.activeSessions.size
        
        this.emit(EVENT_NAMES.SESSION_CLOSED, session)
      }
      
    } catch (error) {
      this.logger.error('Failed to close session:', error)
      throw error
    }
  }

  /**
   * 加入会话
   */
  async joinSession(sessionId: number): Promise<void> {
    try {
      this.logger.debug('Joining session:', sessionId)
      
      // 通过WebSocket发送加入会话消息
      const wsMessage: WebSocketMessage = {
        type: 'join_session' as WebSocketMessageType,
        id: `join_${Date.now()}`,
        timestamp: Date.now(),
        session_id: sessionId,
        user_id: this.config.userId,
        user_type: this.config.userType
      }
      
      await this.wsManager.sendMessage(wsMessage)
      
      this.emit(EVENT_NAMES.SESSION_JOINED, sessionId)
      
    } catch (error) {
      this.logger.error('Failed to join session:', error)
      throw error
    }
  }

  /**
   * 离开会话
   */
  async leaveSession(sessionId: number): Promise<void> {
    try {
      this.logger.debug('Leaving session:', sessionId)
      
      // 通过WebSocket发送离开会话消息
      const wsMessage: WebSocketMessage = {
        type: 'leave_session' as WebSocketMessageType,
        id: `leave_${Date.now()}`,
        timestamp: Date.now(),
        session_id: sessionId,
        user_id: this.config.userId,
        user_type: this.config.userType
      }
      
      await this.wsManager.sendMessage(wsMessage)
      
      this.emit(EVENT_NAMES.SESSION_LEFT, sessionId)
      
    } catch (error) {
      this.logger.error('Failed to leave session:', error)
      throw error
    }
  }

  /**
   * 上传文件
   */
  async uploadFile(file: File, sessionId: number): Promise<string> {
    try {
      this.logger.debug('Uploading file:', { fileName: file.name, size: file.size, sessionId })
      const result = await this.fileService.uploadFile(file, {
        onProgress: (progress) => console.log('Upload progress:', progress)
      })
      return result.file_url || ''
    } catch (error) {
      this.logger.error('Failed to upload file:', error)
      throw error
    }
  }

  /**
   * 获取通知
   */
  async getNotifications(request: any = {}) {
    try {
      return await this.notificationService.getNotifications(request)
    } catch (error) {
      this.logger.error('Failed to get notifications:', error)
      throw error
    }
  }

  /**
   * 发送通知
   */
  async sendNotification(request: any) {
    try {
      return await this.notificationService.sendNotification(request)
    } catch (error) {
      this.logger.error('Failed to send notification:', error)
      throw error
    }
  }

  /**
   * 处理WebSocket连接成功
   */
  private handleWebSocketConnected(): void {
    this.logger.info('WebSocket connected')
    // this.reconnectAttempts = 0 // 属性不存在
  }

  /**
   * 处理WebSocket断开连接
   */
  private handleWebSocketDisconnected(): void {
    this.logger.info('WebSocket disconnected')
    if (this.status === ChatClientStatus.CONNECTED) {
      this.setStatus(ChatClientStatus.RECONNECTING)
    }
  }

  /**
   * 处理WebSocket错误
   */
  private handleWebSocketError(error: any): void {
    this.logger.error('WebSocket error:', error)
    // this.emit(EVENT_NAMES.CHAT_CLIENT_ERROR, this.createError('WEBSOCKET_ERROR', error)) // 暂时注释掉
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(message: WebSocketMessage): void {
    this.logger.debug('Received WebSocket message:', message)
    
    switch (message.type) {
      case 'message':
        this.handleChatMessage(message as ChatMessage)
        break
      case 'user_online':
      case 'user_offline':
      case 'user_status_change':
        this.handleUserStatusMessage(message as UserStatusMessage)
        break
      case 'session_created':
      case 'session_updated':
      case 'session_closed':
        this.handleSessionMessage(message as SessionMessage)
        break
      case 'notification':
        this.handleNotificationMessage(message as NotificationMessage)
        break
      default:
        this.logger.debug('Unhandled message type:', message.type)
    }
  }

  /**
   * 处理聊天消息
   */
  private handleChatMessage(message: ChatMessage): void {
    const { message: chatMessage, session } = message.data
    
    // 更新会话信息
    if (session) {
      this.activeSessions.set(session.id, session)
    }
    
    this.statistics.total_messages++
    // this.statistics.last_activity = Date.now() // 属性不存在
    
    this.emit(EVENT_NAMES.MESSAGE_RECEIVED, chatMessage)
    
    // 播放通知声音
    if (this.config.enableSound && this.settings.notifications.sound) {
      this.playNotificationSound()
    }
    
    // 显示桌面通知
    if (this.config.enableNotifications && this.settings.notifications.desktop) {
      this.showDesktopNotification(chatMessage)
    }
  }

  /**
   * 处理用户状态消息
   */
  private handleUserStatusMessage(message: UserStatusMessage): void {
    const { user_id, user_type, status } = message.data
    
    if (status === 'online') {
      // 用户上线
      this.emit(EVENT_NAMES.USER_ONLINE, { user_id, user_type })
    } else if (status === 'offline') {
      // 用户下线
      this.onlineUsers.delete(user_id)
      this.emit(EVENT_NAMES.USER_OFFLINE, { user_id, user_type })
    }
    
    this.statistics.online_users = this.onlineUsers.size
  }

  /**
   * 处理会话消息
   */
  private handleSessionMessage(message: SessionMessage): void {
    const { session } = message.data
    // const action = message.data.action // 暂时注释掉未使用的变量
    
    switch (message.type) {
      case 'session_created':
        this.activeSessions.set(session.id, session)
        this.statistics.total_sessions++
        this.emit(EVENT_NAMES.SESSION_CREATED, session)
        break
      case 'session_updated':
        this.activeSessions.set(session.id, session)
        this.emit(EVENT_NAMES.SESSION_UPDATED, session)
        break
      case 'session_closed':
        this.activeSessions.delete(session.id)
        // this.statistics.total_sessions_closed++ // 属性不存在
        this.emit(EVENT_NAMES.SESSION_CLOSED, session)
        break
    }
    
    this.statistics.active_sessions = this.activeSessions.size
  }

  /**
   * 处理通知消息
   */
  private handleNotificationMessage(message: NotificationMessage): void {
    const notification = message.data
    this.emit(EVENT_NAMES.NOTIFICATION_RECEIVED, notification)
  }

  /**
   * 处理WebSocket重连
   */
  private handleWebSocketReconnecting(data: { attempt: number, delay: number }): void {
    // this.reconnectAttempts = data.attempt // 属性不存在
    this.logger.info(`Reconnecting attempt ${data.attempt}, delay: ${data.delay}ms`)
    // this.emit(EVENT_NAMES.CHAT_CLIENT_RECONNECTING, data) // 暂时注释掉
  }

  /**
   * 加载用户资料
   */
  private async loadUserProfile(): Promise<void> {
    // TODO: 实现用户资料加载
    this.currentUser = {
      id: this.config.userId,
      type: this.config.userType,
      name: 'User',
      avatar: '',
      status: UserOnlineStatus.ONLINE,
      last_seen: new Date().toISOString()
    }
  }

  /**
   * 加载初始数据
   */
  private async loadInitialData(): Promise<void> {
    try {
      // 加载活跃会话
      // 简化会话加载
      const sessionsResponse = await this.getSessions({
        page: 1
      })

      // 简化会话数据处理
      if (Array.isArray(sessionsResponse)) {
        sessionsResponse.forEach((session: any) => {
          this.activeSessions.set(session.id, session)
        })
      }
      
      // 加载快捷回复
      // TODO: 实现快捷回复加载
      
      // 加载消息模板
      // TODO: 实现消息模板加载
      
    } catch (error) {
      this.logger.error('Failed to load initial data:', error)
    }
  }

  /**
   * 播放通知声音
   */
  private playNotificationSound(): void {
    try {
      const audio = new Audio('/sounds/notification.mp3')
      audio.play().catch(error => {
        this.logger.debug('Failed to play notification sound:', error)
      })
    } catch (error) {
      this.logger.debug('Failed to create audio element:', error)
    }
  }

  /**
   * 显示桌面通知
   */
  private showDesktopNotification(message: Message): void {
    if (!('Notification' in window)) {
      return
    }
    
    if (Notification.permission === 'granted') {
      const notification = new Notification('新消息', {
        body: message.content.substring(0, 100),
        icon: '/icons/chat-notification.png',
        tag: `message_${message.id}`
      })
      
      notification.onclick = () => {
        window.focus()
        this.emit(EVENT_NAMES.NOTIFICATION_CLICKED, message)
        notification.close()
      }
      
      setTimeout(() => {
        notification.close()
      }, 5000)
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          this.showDesktopNotification(message)
        }
      })
    }
  }

  /**
   * 设置状态
   */
  private setStatus(status: ChatClientStatus): void {
    this.status = status
    // this.emit(EVENT_NAMES.CHAT_CLIENT_STATUS_CHANGED, status) // 暂时注释掉
  }

  /**
   * 创建错误对象
   */
  // private _createError(type: ChatErrorType, originalError?: any): ChatError {
  //   return {
  //     type,
  //     message: originalError?.message || 'Unknown error',
  //     code: originalError?.code || 'UNKNOWN',
  //     timestamp: Date.now(),
  //     details: originalError,
  //     is_recoverable: true
  //   }
  // }

  // 公共方法

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.status === 'connected' && this.wsManager.isConnected()
  }

  /**
   * 获取当前状态
   */
  getStatus(): ChatClientStatus {
    return this.status
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): ChatUser | null {
    return this.currentUser
  }

  /**
   * 获取活跃会话
   */
  getActiveSessions(): Session[] {
    return Array.from(this.activeSessions.values())
  }

  /**
   * 获取在线用户
   */
  getOnlineUsers(): ChatUser[] {
    return Array.from(this.onlineUsers.values())
  }

  /**
   * 获取统计信息
   */
  getStatistics(): ChatStatistics {
    return { ...this.statistics }
  }

  /**
   * 获取设置
   */
  getSettings(): ChatSettings {
    return { ...this.settings }
  }

  /**
   * 更新设置
   */
  updateSettings(settings: Partial<ChatSettings>): void {
    this.settings = { ...this.settings, ...settings }
    // this.emit(EVENT_NAMES.CHAT_SETTINGS_UPDATED, this.settings) // 暂时注释掉
  }

  /**
   * 获取快捷回复
   */
  getQuickReplies(): QuickReply[] {
    return [...this.quickReplies]
  }

  /**
   * 获取消息模板
   */
  getMessageTemplates(): MessageTemplate[] {
    return [...this.messageTemplates]
  }

  /**
   * 销毁客户端
   */
  destroy(): void {
    this.disconnect().catch(error => {
      this.logger.error('Error during destroy:', error)
    })
    
    this.removeAllListeners()
    this.logger.info('ChatClient destroyed')
  }
}

// 导出单例实例
let instance: ChatClient | null = null

export function createChatClient(config: Partial<ChatClientConfig>): ChatClient {
  if (instance) {
    instance.destroy()
  }
  instance = new ChatClient(config)
  return instance
}

export function getChatClient(): ChatClient | null {
  return instance
}