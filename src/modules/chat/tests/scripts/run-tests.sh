#!/bin/bash

# 聊天模块测试运行脚本
# 提供多种测试运行选项和环境配置

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../../../.." && pwd)"
CHAT_MODULE_ROOT="$PROJECT_ROOT/src/modules/chat"
TEST_ROOT="$CHAT_MODULE_ROOT/tests"

# 默认配置
TEST_TYPE="all"
ENVIRONMENT="test"
COVERAGE=false
WATCH=false
VERBOSE=false
PARALLEL=true
BROWSER="chromium"
HEADLESS=true
REPORT_FORMAT="html"
CLEAN_BEFORE=false
UPDATE_SNAPSHOTS=false
FAIL_FAST=false
RETRY_COUNT=0
TIMEOUT=30000
MAX_WORKERS=4

# 显示帮助信息
show_help() {
    echo -e "${BLUE}聊天模块测试运行脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --type TYPE           测试类型 (unit|integration|e2e|all) [默认: all]"
    echo "  -e, --env ENV             环境 (test|ci|dev) [默认: test]"
    echo "  -c, --coverage            启用代码覆盖率"
    echo "  -w, --watch               监听模式"
    echo "  -v, --verbose             详细输出"
    echo "  -s, --serial              串行运行（禁用并行）"
    echo "  -b, --browser BROWSER     浏览器 (chromium|firefox|webkit) [默认: chromium]"
    echo "  -h, --headed              有头模式（显示浏览器）"
    echo "  -r, --report FORMAT       报告格式 (html|json|junit|text) [默认: html]"
    echo "  -C, --clean               运行前清理"
    echo "  -u, --update-snapshots    更新快照"
    echo "  -f, --fail-fast           遇到失败立即停止"
    echo "  -R, --retry COUNT         重试次数 [默认: 0]"
    echo "  -T, --timeout MS          超时时间（毫秒） [默认: 30000]"
    echo "  -j, --max-workers NUM     最大工作进程数 [默认: 4]"
    echo "  --help                    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -t unit -c             运行单元测试并生成覆盖率报告"
    echo "  $0 -t e2e -b firefox -h    在 Firefox 有头模式下运行 E2E 测试"
    echo "  $0 -w -v                  监听模式运行所有测试，详细输出"
    echo "  $0 -e ci -c -f            CI 环境下运行测试，启用覆盖率和快速失败"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            TEST_TYPE="$2"
            shift 2
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -w|--watch)
            WATCH=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -s|--serial)
            PARALLEL=false
            shift
            ;;
        -b|--browser)
            BROWSER="$2"
            shift 2
            ;;
        -h|--headed)
            HEADLESS=false
            shift
            ;;
        -r|--report)
            REPORT_FORMAT="$2"
            shift 2
            ;;
        -C|--clean)
            CLEAN_BEFORE=true
            shift
            ;;
        -u|--update-snapshots)
            UPDATE_SNAPSHOTS=true
            shift
            ;;
        -f|--fail-fast)
            FAIL_FAST=true
            shift
            ;;
        -R|--retry)
            RETRY_COUNT="$2"
            shift 2
            ;;
        -T|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -j|--max-workers)
            MAX_WORKERS="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 验证参数
if [[ ! "$TEST_TYPE" =~ ^(unit|integration|e2e|all)$ ]]; then
    echo -e "${RED}错误: 无效的测试类型 '$TEST_TYPE'${NC}"
    exit 1
fi

if [[ ! "$ENVIRONMENT" =~ ^(test|ci|dev)$ ]]; then
    echo -e "${RED}错误: 无效的环境 '$ENVIRONMENT'${NC}"
    exit 1
fi

if [[ ! "$BROWSER" =~ ^(chromium|firefox|webkit)$ ]]; then
    echo -e "${RED}错误: 无效的浏览器 '$BROWSER'${NC}"
    exit 1
fi

# 设置环境变量
export NODE_ENV="$ENVIRONMENT"
export CI=${CI:-false}
export FORCE_COLOR=1

# 进入聊天模块目录
cd "$CHAT_MODULE_ROOT"

# 显示配置信息
echo -e "${BLUE}=== 聊天模块测试配置 ===${NC}"
echo -e "测试类型: ${GREEN}$TEST_TYPE${NC}"
echo -e "环境: ${GREEN}$ENVIRONMENT${NC}"
echo -e "覆盖率: ${GREEN}$COVERAGE${NC}"
echo -e "监听模式: ${GREEN}$WATCH${NC}"
echo -e "并行运行: ${GREEN}$PARALLEL${NC}"
echo -e "浏览器: ${GREEN}$BROWSER${NC}"
echo -e "无头模式: ${GREEN}$HEADLESS${NC}"
echo -e "报告格式: ${GREEN}$REPORT_FORMAT${NC}"
echo -e "最大工作进程: ${GREEN}$MAX_WORKERS${NC}"
echo ""

# 清理函数
cleanup() {
    echo -e "${YELLOW}清理测试环境...${NC}"
    
    # 停止可能运行的进程
    pkill -f "jest" 2>/dev/null || true
    pkill -f "playwright" 2>/dev/null || true
    pkill -f "vitest" 2>/dev/null || true
    
    # 清理临时文件
    if [[ "$CLEAN_BEFORE" == "true" ]]; then
        rm -rf "$TEST_ROOT/coverage" 2>/dev/null || true
        rm -rf "$TEST_ROOT/results" 2>/dev/null || true
        rm -rf "$TEST_ROOT/screenshots" 2>/dev/null || true
        rm -rf "$TEST_ROOT/videos" 2>/dev/null || true
        rm -rf "$TEST_ROOT/.cache" 2>/dev/null || true
    fi
}

# 设置清理陷阱
trap cleanup EXIT INT TERM

# 运行前清理
if [[ "$CLEAN_BEFORE" == "true" ]]; then
    cleanup
fi

# 确保依赖已安装
echo -e "${YELLOW}检查依赖...${NC}"
if [[ ! -d "node_modules" ]]; then
    echo -e "${YELLOW}安装依赖...${NC}"
    npm install
fi

# 构建通用参数
build_common_args() {
    local args=()
    
    if [[ "$VERBOSE" == "true" ]]; then
        args+=("--verbose")
    fi
    
    if [[ "$FAIL_FAST" == "true" ]]; then
        args+=("--bail")
    fi
    
    if [[ "$UPDATE_SNAPSHOTS" == "true" ]]; then
        args+=("--updateSnapshot")
    fi
    
    if [[ "$PARALLEL" == "false" ]]; then
        args+=("--runInBand")
    else
        args+=("--maxWorkers=$MAX_WORKERS")
    fi
    
    echo "${args[@]}"
}

# 构建覆盖率参数
build_coverage_args() {
    local args=()
    
    if [[ "$COVERAGE" == "true" ]]; then
        args+=("--coverage")
        args+=("--coverageDirectory=tests/coverage")
        
        case "$REPORT_FORMAT" in
            html)
                args+=("--coverageReporters=html,text-summary")
                ;;
            json)
                args+=("--coverageReporters=json,text-summary")
                ;;
            text)
                args+=("--coverageReporters=text,text-summary")
                ;;
            *)
                args+=("--coverageReporters=html,text-summary")
                ;;
        esac
    fi
    
    echo "${args[@]}"
}

# 运行单元测试
run_unit_tests() {
    echo -e "${BLUE}=== 运行单元测试 ===${NC}"
    
    local args=()
    args+=($(build_common_args))
    args+=($(build_coverage_args))
    
    if [[ "$WATCH" == "true" ]]; then
        args+=("--watch")
    fi
    
    # 使用 Jest 运行单元测试
    npx jest tests/unit "${args[@]}" --config=tests/jest.config.js
}

# 运行集成测试
run_integration_tests() {
    echo -e "${BLUE}=== 运行集成测试 ===${NC}"
    
    local args=()
    args+=($(build_common_args))
    args+=($(build_coverage_args))
    
    if [[ "$WATCH" == "true" ]]; then
        args+=("--watch")
    fi
    
    # 使用 Vitest 运行集成测试
    npx vitest run tests/integration "${args[@]}" --config=tests/vitest.config.ts
}

# 运行 E2E 测试
run_e2e_tests() {
    echo -e "${BLUE}=== 运行 E2E 测试 ===${NC}"
    
    local args=()
    args+=("--project=$BROWSER")
    
    if [[ "$HEADLESS" == "false" ]]; then
        args+=("--headed")
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        args+=("--reporter=list")
    fi
    
    if [[ "$RETRY_COUNT" -gt 0 ]]; then
        args+=("--retries=$RETRY_COUNT")
    fi
    
    args+=("--timeout=$TIMEOUT")
    
    if [[ "$PARALLEL" == "false" ]]; then
        args+=("--workers=1")
    else
        args+=("--workers=$MAX_WORKERS")
    fi
    
    # 设置报告格式
    case "$REPORT_FORMAT" in
        html)
            args+=("--reporter=html")
            ;;
        json)
            args+=("--reporter=json")
            ;;
        junit)
            args+=("--reporter=junit")
            ;;
        *)
            args+=("--reporter=html")
            ;;
    esac
    
    # 使用 Playwright 运行 E2E 测试
    npx playwright test "${args[@]}" --config=tests/playwright.config.ts
}

# 生成测试报告
generate_report() {
    echo -e "${BLUE}=== 生成测试报告 ===${NC}"
    
    local report_dir="$TEST_ROOT/reports"
    mkdir -p "$report_dir"
    
    # 合并覆盖率报告
    if [[ "$COVERAGE" == "true" && -d "$TEST_ROOT/coverage" ]]; then
        echo -e "${YELLOW}合并覆盖率报告...${NC}"
        
        # 生成覆盖率徽章
        if command -v coverage-badges &> /dev/null; then
            coverage-badges -i "$TEST_ROOT/coverage/coverage-summary.json" -o "$report_dir/coverage-badge.svg"
        fi
        
        # 生成覆盖率摘要
        if [[ -f "$TEST_ROOT/coverage/coverage-summary.json" ]]; then
            node -e "
                const fs = require('fs');
                const summary = JSON.parse(fs.readFileSync('$TEST_ROOT/coverage/coverage-summary.json'));
                const total = summary.total;
                console.log('覆盖率摘要:');
                console.log('  行覆盖率:', total.lines.pct + '%');
                console.log('  分支覆盖率:', total.branches.pct + '%');
                console.log('  函数覆盖率:', total.functions.pct + '%');
                console.log('  语句覆盖率:', total.statements.pct + '%');
            "
        fi
    fi
    
    # 生成测试摘要
    echo -e "${GREEN}测试完成！${NC}"
    echo -e "报告位置: ${BLUE}$report_dir${NC}"
    
    if [[ "$COVERAGE" == "true" ]]; then
        echo -e "覆盖率报告: ${BLUE}$TEST_ROOT/coverage/html/index.html${NC}"
    fi
}

# 主执行逻辑
main() {
    local exit_code=0
    
    case "$TEST_TYPE" in
        unit)
            run_unit_tests || exit_code=$?
            ;;
        integration)
            run_integration_tests || exit_code=$?
            ;;
        e2e)
            run_e2e_tests || exit_code=$?
            ;;
        all)
            echo -e "${BLUE}=== 运行所有测试 ===${NC}"
            
            # 按顺序运行所有测试
            run_unit_tests || exit_code=$?
            
            if [[ $exit_code -eq 0 || "$FAIL_FAST" != "true" ]]; then
                run_integration_tests || exit_code=$?
            fi
            
            if [[ $exit_code -eq 0 || "$FAIL_FAST" != "true" ]]; then
                run_e2e_tests || exit_code=$?
            fi
            ;;
    esac
    
    # 生成报告
    generate_report
    
    # 显示结果
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}✅ 所有测试通过！${NC}"
    else
        echo -e "${RED}❌ 测试失败，退出码: $exit_code${NC}"
    fi
    
    return $exit_code
}

# 运行主函数
main
exit $?