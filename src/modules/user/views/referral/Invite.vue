<!--
 * 用户邀请好友页面
 * 展示邀请二维码，提供复制邀请链接及分享功能
 * 通过该页面可邀请好友注册成为新用户
-->
<template>
  <div class="invite-page">
    <!-- 页面标题 -->
    <div class="invite-header">
      <h1>邀请好友</h1>
      <p>邀请好友注册成为新用户，双方都可获得奖励</p>
    </div>
    
    <!-- 邀请卡片 -->
    <div class="invite-card">
      <!-- 邀请说明 -->
      <div class="invite-header">
        <h3>邀请好友</h3>
        <p>扫描下方二维码或分享链接邀请好友，获得丰厚奖励</p>
      </div>
      
      <div class="invite-content">
        <!-- 邀请链接 -->
        <div class="invite-method">
          <h4>邀请链接</h4>
          <div class="link-container">
            <el-input v-model="inviteLink" readonly>
              <template #append>
                <el-button @click="copyLink">复制</el-button>
              </template>
            </el-input>
          </div>
        </div>
        
        <!-- 二维码 -->
        <div class="invite-method">
          <h4>邀请二维码</h4>
          <div class="qrcode-container">
            <div class="qrcode-wrapper">
              <canvas ref="qrcode" class="qrcode"></canvas>
              <div v-if="loading" class="qrcode-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
              </div>
            </div>
            <div class="qrcode-actions">
              <el-button @click="downloadQRCode" :disabled="loading">下载二维码</el-button>
            </div>
          </div>
        </div>
        
        <!-- 社交分享 -->
        <div class="invite-method">
          <h4>社交分享</h4>
          <div class="share-buttons">
            <el-button type="success" @click="shareToWechat">
              <el-icon><ChatRound /></el-icon> 微信
            </el-button>
            <el-button type="primary" @click="shareToQQ">
              <el-icon><Comment /></el-icon> QQ
            </el-button>
            <el-button type="danger" @click="shareToWeibo">
              <el-icon><Share /></el-icon> 微博
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 邀请规则 -->
      <div class="invite-rules">
        <h4>邀请规则</h4>
        <ul>
          <li>好友通过您的邀请链接注册成功，您将获得一级分销资格</li>
          <li>好友首次消费，您将获得订单金额5%的佣金</li>
          <li>好友再次邀请其他用户，您将获得二级分销佣金3%</li>
          <li>最多支持三级分销，三级佣金为1%</li>
        </ul>
      </div>
    </div>

    <!-- 最近邀请记录 -->
    <div class="recent-invites" v-if="recentInvites.length > 0">
      <h3>最近邀请</h3>
      <el-table :data="recentInvites" stripe style="width: 100%">
        <el-table-column prop="nickname" label="用户" width="180">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :src="row.avatar" :size="40">
                {{ row.nickname.charAt(0) }}
              </el-avatar>
              <span>{{ row.nickname }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="referral_time" label="邀请时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.referral_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'info'">
              {{ row.status === 1 ? '已注册' : '未注册' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, ChatRound, Comment, Share } from '@element-plus/icons-vue';
import QRCode from 'qrcode';
import { getUserReferralInfo, getReferrals } from '../../api/referral';
import { useUserStore } from '@/modules/user/stores/userStore';

// 状态定义
const inviteLink = ref('');
const loading = ref(true);
const qrcode = ref<HTMLCanvasElement | null>(null);
const userStore = useUserStore();
const recentInvites = ref<any[]>([]);

// 初始化
onMounted(async () => {
  await generateInviteLink();
  await loadRecentInvites();
  await nextTick();
  await generateQRCode();
});

// 生成邀请链接
async function generateInviteLink() {
  try {
    loading.value = true;
    const response = await getUserReferralInfo();
    if (response && response.referral_link) {
      inviteLink.value = response.referral_link;
    } else {
      // 如果API未返回链接，则使用默认方式生成
      const userId = userStore.userId;
      inviteLink.value = `${window.location.origin}/user/register?referrer=${userId}`;
    }
  } catch (error) {
    console.error('获取邀请链接失败:', error);
    ElMessage.error('获取邀请链接失败，请稍后再试');
    // 使用默认方式生成
    const userId = userStore.userId;
    inviteLink.value = `${window.location.origin}/user/register?referrer=${userId}`;
  }
}

// 生成二维码
async function generateQRCode() {
  if (!qrcode.value) {
    console.error('QRCode canvas element not found');
    loading.value = false;
    return;
  }
  
  try {
    await QRCode.toCanvas(qrcode.value, inviteLink.value, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
  } catch (error) {
    console.error('生成二维码失败:', error);
    ElMessage.error('生成二维码失败，请稍后再试');
  } finally {
    loading.value = false;
  }
}

// 加载最近邀请记录
async function loadRecentInvites() {
  try {
    const response = await getReferrals({
      page: 1,
      page_size: 5,
      level: 1,
      only_direct: true
    });
    
    if (response && response.list) {
      recentInvites.value = response.list;
    }
  } catch (error) {
    console.error('获取最近邀请记录失败:', error);
  }
}

// 复制邀请链接
async function copyLink() {
  try {
    await navigator.clipboard.writeText(inviteLink.value);
    ElMessage.success('邀请链接已复制到剪贴板');
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = inviteLink.value;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    ElMessage.success('邀请链接已复制到剪贴板');
  }
}

// 下载二维码
function downloadQRCode() {
  if (!qrcode.value) return;
  
  const link = document.createElement('a');
  link.download = '邀请二维码.png';
  link.href = qrcode.value.toDataURL('image/png');
  link.click();
  ElMessage.success('二维码已下载');
}

// 社交分享功能
function shareToWechat() {
  // 微信分享逻辑
  if (navigator.share) {
    navigator.share({
      title: '邀请您加入我们的平台',
      text: '通过我的邀请链接注册，我们都能获得奖励！',
      url: inviteLink.value
    }).catch(() => {
      copyLink();
      ElMessage.info('请在微信中粘贴分享链接');
    });
  } else {
    copyLink();
    ElMessage.info('请在微信中粘贴分享链接');
  }
}

function shareToQQ() {
  const shareUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(inviteLink.value)}&title=${encodeURIComponent('邀请您加入我们的平台')}`;
  window.open(shareUrl, '_blank');
}

function shareToWeibo() {
  const shareUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(inviteLink.value)}&title=${encodeURIComponent('邀请您加入我们的平台')}`;
  window.open(shareUrl, '_blank');
}

// 格式化日期
function formatDate(date: string | number) {
  return new Date(date).toLocaleString('zh-CN');
}
</script>

<style scoped>
.invite-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.invite-header {
  text-align: center;
  margin-bottom: 20px;
}

.invite-header h1 {
  font-size: 24px;
  color: #333;
}

.invite-header p {
  color: #666;
  margin-top: 10px;
}

.invite-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.invite-method {
  margin-bottom: 30px;
}

.invite-method h4 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
}

.link-container {
  margin-bottom: 10px;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-wrapper {
  position: relative;
  width: 200px;
  height: 200px;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode {
  max-width: 100%;
  border: 1px solid #eee;
}

.qrcode-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
}

.share-buttons {
  display: flex;
  gap: 10px;
}

.invite-rules {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.invite-rules h4 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
}

.invite-rules ul {
  padding-left: 20px;
}

.invite-rules li {
  margin-bottom: 8px;
  color: #666;
}

.recent-invites {
  margin-top: 30px;
}

.recent-invites h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
