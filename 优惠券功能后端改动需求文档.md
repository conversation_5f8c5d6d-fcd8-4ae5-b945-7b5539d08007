# 优惠券功能后端改动需求文档

## 问题概述

当前优惠券功能存在以下问题：
1. 优惠券列表中虽然优惠券处于待发布状态，但缺少发布按钮
2. 新建优惠券和编辑优惠券页面中，虽然点选了立即发布，但保存后列表依然显示待发布状态
3. 前后端状态值映射不一致

## 状态定义规范

### 实际后端状态定义（根据API返回数据确认）
- `0`: 待发布（草稿状态）
- `1`: 已发布（活动状态）- 后端返回status_text为"已发布"
- `3`: 已结束
- `4`: 已暂停

### 前端状态常量定义（已修正）
```javascript
const COUPON_STATUS = {
  PENDING: 0,    // 待发布（草稿状态）
  ACTIVE: 1,     // 已发布（活动状态）
  ENDED: 3,      // 已结束
  PAUSED: 4      // 已暂停
}
```

## 需要新增的API接口

### 1. 发布优惠券接口

**接口路径**: `PUT /v1/merchant/takeout/coupons/{id}/publish`

**功能描述**: 将待发布状态（status=0）的优惠券发布为活动状态（status=1）

**请求参数**:
- `id` (路径参数): 优惠券ID

**响应格式**:
```json
{
  "code": 200,
  "message": "优惠券发布成功",
  "data": {
    "id": 123,
    "status": 1,
    "updated_at": "2025-01-20T10:30:00Z"
  }
}
```

**业务逻辑**:
1. 验证优惠券是否存在
2. 验证优惠券当前状态是否为待发布（status=0）
3. 验证优惠券的开始时间和结束时间是否合理
4. 将优惠券状态更新为活动状态（status=1）
5. 记录状态变更日志

## 需要修改的现有接口

### 1. 创建优惠券接口

**接口路径**: `POST /v1/merchant/takeout/coupons`

**需要修改的逻辑**:
- 当前端传递 `status: 1` 时，后端应保存为待发布状态
- 当前端传递 `status: 2` 时，后端应保存为活动状态（立即发布）
- 确保状态值与前端定义保持一致

### 2. 更新优惠券接口

**接口路径**: `PUT /v1/merchant/takeout/coupons/{id}`

**需要修改的逻辑**:
- 支持状态值的正确映射
- 当状态从待发布（1）改为活动（2）时，执行发布逻辑
- 当状态从活动（2）改为待发布（1）时，执行下架逻辑

### 3. 优惠券列表接口

**接口路径**: `GET /v1/merchant/takeout/coupons`

**需要确认的逻辑**:
- 返回的 `status` 字段值应与前端状态常量定义一致
- 确保状态筛选功能正常工作

## 状态转换规则

### 允许的状态转换
1. `待发布(1)` → `进行中(2)`: 通过发布接口或编辑接口
2. `进行中(2)` → `已暂停(4)`: 通过暂停接口
3. `已暂停(4)` → `进行中(2)`: 通过启用接口
4. `进行中(2)` → `已结束(3)`: 系统自动或手动结束
5. `待发布(1)` → `已结束(3)`: 直接废弃

### 不允许的状态转换
- `已结束(3)` 不能转换为其他任何状态
- `已暂停(4)` 不能直接转换为 `待发布(1)`

## 数据库表结构建议

### 优惠券表 (coupons)
确保以下字段存在并正确设置：
```sql
-- 状态字段，使用整型存储
status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待发布，2-进行中，3-已结束，4-已暂停'

-- 创建时间和更新时间
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

-- 发布时间（可选）
published_at TIMESTAMP NULL COMMENT '发布时间'
```

## 错误处理

### 发布接口可能的错误情况
1. 优惠券不存在: 返回404
2. 优惠券已经是活动状态: 返回400，提示"优惠券已经是活动状态"
3. 优惠券已过期: 返回400，提示"优惠券已过期，无法发布"
4. 优惠券开始时间在未来: 可以发布，但状态逻辑需要考虑时间因素

## 测试用例建议

### 1. 发布功能测试
- 创建待发布状态的优惠券
- 调用发布接口
- 验证状态是否正确更新为活动状态
- 验证前端列表页面显示是否正确

### 2. 状态一致性测试
- 创建优惠券时选择"立即发布"
- 验证保存后状态是否为活动状态（2）
- 验证前端列表显示是否为"进行中"

### 3. 状态转换测试
- 测试所有允许的状态转换
- 测试不允许的状态转换是否正确拒绝

## 实施优先级

1. **高优先级**: 修复创建/编辑优惠券的状态映射问题
2. **高优先级**: 新增发布优惠券接口
3. **中优先级**: 完善状态转换规则和错误处理
4. **低优先级**: 添加发布时间字段和相关日志

## 注意事项

1. 确保所有状态变更都有相应的日志记录
2. 考虑并发情况下的状态一致性
3. 发布操作应该是幂等的
4. 需要考虑优惠券的时间有效性与状态的关系
