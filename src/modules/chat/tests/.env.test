# 聊天模块测试环境配置文件
# 用于配置测试环境的各种参数和设置

# =============================================================================
# 基础配置
# =============================================================================

# 环境标识
NODE_ENV=test
VUE_APP_ENV=test

# 应用配置
VUE_APP_NAME="聊天模块测试"
VUE_APP_VERSION=1.0.0
VUE_APP_DEBUG=true

# =============================================================================
# API 配置
# =============================================================================

# API 基础地址
VUE_APP_API_BASE_URL=http://localhost:3001/api
VUE_APP_API_TIMEOUT=10000
VUE_APP_API_RETRY_COUNT=3
VUE_APP_API_RETRY_DELAY=1000

# WebSocket 配置
VUE_APP_WS_URL=ws://localhost:3001/ws
VUE_APP_WS_RECONNECT_INTERVAL=5000
VUE_APP_WS_MAX_RECONNECT_ATTEMPTS=5
VUE_APP_WS_HEARTBEAT_INTERVAL=30000

# 文件上传配置
VUE_APP_UPLOAD_URL=http://localhost:3001/api/upload
VUE_APP_MAX_FILE_SIZE=10485760
VUE_APP_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/plain,video/mp4,audio/mp3

# =============================================================================
# 测试配置
# =============================================================================

# Jest 配置
JEST_TIMEOUT=30000
JEST_MAX_WORKERS=4
JEST_COVERAGE_THRESHOLD_GLOBAL_BRANCHES=80
JEST_COVERAGE_THRESHOLD_GLOBAL_FUNCTIONS=85
JEST_COVERAGE_THRESHOLD_GLOBAL_LINES=85
JEST_COVERAGE_THRESHOLD_GLOBAL_STATEMENTS=85

# Playwright 配置
PLAYWRIGHT_TIMEOUT=30000
PLAYWRIGHT_EXPECT_TIMEOUT=5000
PLAYWRIGHT_NAVIGATION_TIMEOUT=30000
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_BROWSER=chromium
PLAYWRIGHT_VIEWPORT_WIDTH=1280
PLAYWRIGHT_VIEWPORT_HEIGHT=720
PLAYWRIGHT_WORKERS=4
PLAYWRIGHT_RETRIES=2

# Vitest 配置
VITEST_TIMEOUT=10000
VITEST_HOOK_TIMEOUT=10000
VITEST_TEARDOWN_TIMEOUT=10000

# =============================================================================
# 模拟服务配置
# =============================================================================

# 模拟 API 服务器
MOCK_API_PORT=3001
MOCK_API_HOST=localhost
MOCK_API_DELAY=100
MOCK_API_ERROR_RATE=0.1

# 模拟 WebSocket 服务器
MOCK_WS_PORT=3002
MOCK_WS_HOST=localhost
MOCK_WS_HEARTBEAT_INTERVAL=30000

# 模拟数据配置
MOCK_USERS_COUNT=5
MOCK_SESSIONS_COUNT=4
MOCK_MESSAGES_COUNT=15
MOCK_FILES_COUNT=4

# =============================================================================
# 数据库配置
# =============================================================================

# 测试数据库
TEST_DB_TYPE=json
TEST_DB_PATH=./tests/fixtures/test-database.json
TEST_DB_RESET_ON_START=true
TEST_DB_BACKUP_ON_CHANGE=false

# =============================================================================
# 存储配置
# =============================================================================

# 本地存储
VUE_APP_STORAGE_PREFIX=chat_test_
VUE_APP_STORAGE_ENCRYPTION=false
VUE_APP_STORAGE_COMPRESSION=false

# 会话存储
VUE_APP_SESSION_TIMEOUT=3600000
VUE_APP_SESSION_REFRESH_THRESHOLD=300000

# 缓存配置
VUE_APP_CACHE_TTL=300000
VUE_APP_CACHE_MAX_SIZE=100
VUE_APP_CACHE_STRATEGY=lru

# =============================================================================
# 功能开关
# =============================================================================

# 聊天功能
VUE_APP_ENABLE_FILE_UPLOAD=true
VUE_APP_ENABLE_VOICE_MESSAGE=true
VUE_APP_ENABLE_VIDEO_CALL=false
VUE_APP_ENABLE_SCREEN_SHARE=false
VUE_APP_ENABLE_MESSAGE_REACTIONS=true
VUE_APP_ENABLE_MESSAGE_EDITING=true
VUE_APP_ENABLE_MESSAGE_DELETING=true
VUE_APP_ENABLE_MESSAGE_FORWARDING=true
VUE_APP_ENABLE_MESSAGE_SEARCH=true
VUE_APP_ENABLE_USER_MENTIONS=true
VUE_APP_ENABLE_CHANNEL_MENTIONS=true
VUE_APP_ENABLE_EMOJI_PICKER=true
VUE_APP_ENABLE_CUSTOM_EMOJIS=false
VUE_APP_ENABLE_MESSAGE_THREADS=false
VUE_APP_ENABLE_MESSAGE_SCHEDULING=false

# 通知功能
VUE_APP_ENABLE_NOTIFICATIONS=true
VUE_APP_ENABLE_SOUND_NOTIFICATIONS=true
VUE_APP_ENABLE_DESKTOP_NOTIFICATIONS=true
VUE_APP_ENABLE_PUSH_NOTIFICATIONS=false

# 安全功能
VUE_APP_ENABLE_ENCRYPTION=true
VUE_APP_ENABLE_MESSAGE_VERIFICATION=false
VUE_APP_ENABLE_USER_VERIFICATION=false

# =============================================================================
# 性能配置
# =============================================================================

# 虚拟滚动
VUE_APP_VIRTUAL_SCROLL_ENABLED=true
VUE_APP_VIRTUAL_SCROLL_ITEM_HEIGHT=60
VUE_APP_VIRTUAL_SCROLL_BUFFER_SIZE=10

# 分页配置
VUE_APP_MESSAGES_PAGE_SIZE=50
VUE_APP_SESSIONS_PAGE_SIZE=20
VUE_APP_USERS_PAGE_SIZE=20

# 防抖配置
VUE_APP_SEARCH_DEBOUNCE_DELAY=300
VUE_APP_TYPING_DEBOUNCE_DELAY=1000
VUE_APP_RESIZE_DEBOUNCE_DELAY=100

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别
VUE_APP_LOG_LEVEL=debug
VUE_APP_LOG_CONSOLE=true
VUE_APP_LOG_FILE=false
VUE_APP_LOG_REMOTE=false

# 错误追踪
VUE_APP_ERROR_TRACKING=false
VUE_APP_ERROR_REPORTING=false

# =============================================================================
# 测试工具配置
# =============================================================================

# 截图配置
TEST_SCREENSHOT_ON_FAILURE=true
TEST_SCREENSHOT_MODE=only-on-failure
TEST_SCREENSHOT_FULL_PAGE=false

# 视频录制
TEST_VIDEO_RECORDING=retain-on-failure
TEST_VIDEO_SIZE={width:1280,height:720}

# 测试数据
TEST_DATA_RESET_BETWEEN_TESTS=true
TEST_DATA_SEED=12345
TEST_DATA_FAKER_LOCALE=zh_CN

# 网络模拟
TEST_NETWORK_SIMULATION=false
TEST_NETWORK_DELAY=0
TEST_NETWORK_THROTTLE=false

# =============================================================================
# CI/CD 配置
# =============================================================================

# CI 环境检测
CI=false
GITHUB_ACTIONS=false
JENKINS=false

# 报告配置
TEST_REPORT_FORMAT=html
TEST_REPORT_OUTPUT_DIR=./tests/reports
TEST_COVERAGE_OUTPUT_DIR=./tests/coverage

# 通知配置
TEST_NOTIFICATION_WEBHOOK=
TEST_NOTIFICATION_EMAIL=
TEST_NOTIFICATION_SLACK=

# =============================================================================
# 开发配置
# =============================================================================

# 热重载
VUE_APP_HOT_RELOAD=true
VUE_APP_HOT_RELOAD_PORT=8080

# 开发服务器
VUE_APP_DEV_SERVER_PORT=8080
VUE_APP_DEV_SERVER_HOST=localhost
VUE_APP_DEV_SERVER_HTTPS=false

# 代理配置
VUE_APP_PROXY_API=true
VUE_APP_PROXY_WS=true

# =============================================================================
# 第三方服务配置
# =============================================================================

# 文件存储服务
VUE_APP_FILE_STORAGE_TYPE=local
VUE_APP_FILE_STORAGE_BASE_URL=http://localhost:3001/files

# CDN 配置
VUE_APP_CDN_ENABLED=false
VUE_APP_CDN_BASE_URL=

# 分析服务
VUE_APP_ANALYTICS_ENABLED=false
VUE_APP_ANALYTICS_ID=

# =============================================================================
# 安全配置
# =============================================================================

# CORS 配置
VUE_APP_CORS_ENABLED=true
VUE_APP_CORS_ORIGINS=http://localhost:8080,http://localhost:3000

# CSP 配置
VUE_APP_CSP_ENABLED=false
VUE_APP_CSP_REPORT_URI=

# 认证配置
VUE_APP_AUTH_TYPE=mock
VUE_APP_AUTH_TOKEN_KEY=auth_token
VUE_APP_AUTH_REFRESH_TOKEN_KEY=refresh_token
VUE_APP_AUTH_TOKEN_EXPIRY=3600

# =============================================================================
# 实验性功能
# =============================================================================

# 实验性功能开关
VUE_APP_EXPERIMENTAL_FEATURES=false
VUE_APP_EXPERIMENTAL_AI_ASSISTANT=false
VUE_APP_EXPERIMENTAL_VOICE_RECOGNITION=false
VUE_APP_EXPERIMENTAL_TRANSLATION=false

# =============================================================================
# 调试配置
# =============================================================================

# Vue 调试
VUE_APP_VUE_DEVTOOLS=true
VUE_APP_VUE_DEBUG=true

# 性能监控
VUE_APP_PERFORMANCE_MONITORING=false
VUE_APP_MEMORY_MONITORING=false

# 网络调试
VUE_APP_NETWORK_DEBUG=false
VUE_APP_WS_DEBUG=false

# =============================================================================
# 自定义配置
# =============================================================================

# 主题配置
VUE_APP_DEFAULT_THEME=light
VUE_APP_THEME_PERSISTENCE=true

# 语言配置
VUE_APP_DEFAULT_LOCALE=zh-CN
VUE_APP_LOCALE_PERSISTENCE=true
VUE_APP_FALLBACK_LOCALE=en-US

# 用户偏好
VUE_APP_DEFAULT_NOTIFICATIONS=true
VUE_APP_DEFAULT_SOUND=true
VUE_APP_DEFAULT_VIBRATION=false