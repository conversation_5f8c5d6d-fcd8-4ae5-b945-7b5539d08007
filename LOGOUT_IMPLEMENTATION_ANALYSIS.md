# 登出功能实现分析与修复方案

## 问题分析

根据 `frontend-integration-guide.md` 文档要求，各模块的登出功能应该按照以下规范实现：

### 文档要求的登出API

1. **用户模块**: `POST /api/v1/user/devices/{device_id}/logout`
2. **商户模块**: `POST /api/v1/merchant/devices/{device_id}/logout`
3. **管理员模块**: `POST /api/v1/admin/devices/{device_id}/logout`

### 当前实现存在的问题

#### 1. API路径不符合文档要求

**用户模块当前实现**:
```javascript
// 当前: /v1/user/secured/logout
// 应该: /api/v1/user/devices/{device_id}/logout
```

**商户模块当前实现**:
```javascript
// 当前: /v1/merchant/logout
// 应该: /api/v1/merchant/devices/{device_id}/logout
```

**管理员模块当前实现**:
```javascript
// 当前: /v1/admin/logout
// 应该: /api/v1/admin/devices/{device_id}/logout
```

#### 2. device_id 存储和使用问题

**用户模块**:
- ✅ 登录成功后正确存储了 `device_id` 到 `user_current_device_info`
- ❌ 登出时没有使用 `device_id` 作为URL参数

**商户模块**:
- ✅ 登录成功后存储了 `device_id` 到 `merchant_device_id`
- ✅ 同时存储完整设备信息到 `merchant_current_device_info`
- ❌ 登出时没有使用 `device_id` 作为URL参数

**管理员模块**:
- ✅ 登录成功后存储了完整设备信息到 `admin_current_device_info`
- ❌ 没有单独存储 `device_id`
- ❌ 登出时没有调用任何登出API

#### 3. 登出逻辑不完整

**管理员模块问题最严重**:
- 登出函数中完全没有调用后端登出API
- 只是清除了本地存储，没有通知服务器

## 修复方案

### 1. 修复API接口实现

需要修改各模块的 `auth.ts` 文件中的 `logout` 函数，使其符合文档要求的API路径。

### 2. 修复Store中的登出逻辑

需要修改各模块Store中的登出函数，确保：
1. 正确获取 `device_id`
2. 使用正确的API路径调用登出接口
3. 传递 `device_id` 作为URL参数而不是请求体参数

### 3. 统一device_id存储策略

建议所有模块都采用统一的存储方式：
- 存储完整设备信息到 `{module}_current_device_info`
- 单独存储 `device_id` 到 `{module}_device_id` 便于快速获取

## 具体修复步骤

1. 修复用户模块登出API
2. 修复商户模块登出API
3. 修复管理员模块登出API
4. 修复各模块Store中的登出逻辑
5. 确保device_id的正确存储和获取
6. 添加错误处理和降级方案

## 预期效果

修复后，各模块的登出功能将：
1. 使用正确的API路径
2. 正确传递device_id参数
3. 符合多设备登录管理的要求
4. 提供完整的错误处理机制