/**
 * ChatWindow 组件单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import ChatWindow from '../../../components/ChatWindow.vue'
import { useChatStore } from '../../../stores/chat'
import { useSessionStore } from '../../../stores/session'
import { useMessageStore } from '../../../stores/message'
import { useFileStore } from '../../../stores/file'

// Mock 子组件
vi.mock('../../../components/SessionList.vue', () => ({
  default: {
    name: 'SessionList',
    template: '<div data-testid="session-list">SessionList</div>'
  }
}))

vi.mock('../../../components/MessageList.vue', () => ({
  default: {
    name: 'MessageList',
    template: '<div data-testid="message-list">MessageList</div>'
  }
}))

vi.mock('../../../components/MessageInput.vue', () => ({
  default: {
    name: 'MessageInput',
    template: '<div data-testid="message-input">MessageInput</div>'
  }
}))

vi.mock('../../../components/SessionInfo.vue', () => ({
  default: {
    name: 'SessionInfo',
    template: '<div data-testid="session-info">SessionInfo</div>'
  }
}))

vi.mock('../../../components/NotificationToast.vue', () => ({
  default: {
    name: 'NotificationToast',
    template: '<div data-testid="notification-toast">NotificationToast</div>'
  }
}))

vi.mock('../../../components/FileUploadProgress.vue', () => ({
  default: {
    name: 'FileUploadProgress',
    template: '<div data-testid="file-upload-progress">FileUploadProgress</div>'
  }
}))

// Mock ChatClient
vi.mock('../../../services/client', () => ({
  ChatClient: {
    getInstance: vi.fn().mockReturnValue({
      connect: vi.fn().mockResolvedValue(undefined),
      disconnect: vi.fn().mockResolvedValue(undefined),
      on: vi.fn(),
      off: vi.fn(),
      isConnected: vi.fn().mockReturnValue(false)
    })
  }
}))

describe('ChatWindow', () => {
  let wrapper: any
  let chatStore: any
  let sessionStore: any
  let messageStore: any
  let fileStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    
    chatStore = useChatStore()
    sessionStore = useSessionStore()
    messageStore = useMessageStore()
    fileStore = useFileStore()
    
    // Mock store 方法
    vi.spyOn(chatStore, 'initializeClient').mockResolvedValue(undefined)
    vi.spyOn(chatStore, 'disconnectClient').mockResolvedValue(undefined)
    vi.spyOn(sessionStore, 'loadSessions').mockResolvedValue(undefined)
    
    wrapper = mount(ChatWindow, {
      global: {
        plugins: [createPinia()]
      }
    })
  })

  afterEach(() => {
    wrapper?.unmount()
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染聊天窗口结构', () => {
      expect(wrapper.find('.chat-window').exists()).toBe(true)
      expect(wrapper.find('.chat-header').exists()).toBe(true)
      expect(wrapper.find('.chat-body').exists()).toBe(true)
    })

    it('应该渲染所有子组件', () => {
      expect(wrapper.find('[data-testid="session-list"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="message-list"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="message-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="session-info"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="notification-toast"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="file-upload-progress"]').exists()).toBe(true)
    })

    it('应该显示窗口标题', () => {
      const title = wrapper.find('.window-title')
      expect(title.exists()).toBe(true)
      expect(title.text()).toContain('聊天')
    })

    it('应该显示窗口控制按钮', () => {
      expect(wrapper.find('.minimize-btn').exists()).toBe(true)
      expect(wrapper.find('.maximize-btn').exists()).toBe(true)
      expect(wrapper.find('.close-btn').exists()).toBe(true)
    })
  })

  describe('窗口状态管理', () => {
    it('应该能够最小化窗口', async () => {
      const minimizeBtn = wrapper.find('.minimize-btn')
      await minimizeBtn.trigger('click')
      
      expect(wrapper.vm.isMinimized).toBe(true)
      expect(wrapper.classes()).toContain('minimized')
    })

    it('应该能够最大化/还原窗口', async () => {
      const maximizeBtn = wrapper.find('.maximize-btn')
      
      // 最大化
      await maximizeBtn.trigger('click')
      expect(wrapper.vm.isMaximized).toBe(true)
      expect(wrapper.classes()).toContain('maximized')
      
      // 还原
      await maximizeBtn.trigger('click')
      expect(wrapper.vm.isMaximized).toBe(false)
      expect(wrapper.classes()).not.toContain('maximized')
    })

    it('应该能够关闭窗口', async () => {
      const closeBtn = wrapper.find('.close-btn')
      await closeBtn.trigger('click')
      
      expect(wrapper.emitted('close')).toBeTruthy()
    })

    it('应该能够切换侧边栏显示', async () => {
      expect(wrapper.vm.showSidebar).toBe(true)
      
      await wrapper.vm.toggleSidebar()
      expect(wrapper.vm.showSidebar).toBe(false)
      
      await wrapper.vm.toggleSidebar()
      expect(wrapper.vm.showSidebar).toBe(true)
    })

    it('应该能够切换信息面板显示', async () => {
      expect(wrapper.vm.showInfoPanel).toBe(false)
      
      await wrapper.vm.toggleInfoPanel()
      expect(wrapper.vm.showInfoPanel).toBe(true)
      
      await wrapper.vm.toggleInfoPanel()
      expect(wrapper.vm.showInfoPanel).toBe(false)
    })
  })

  describe('会话管理', () => {
    it('应该能够选择会话', async () => {
      const sessionId = 'session1'
      vi.spyOn(sessionStore, 'selectSession').mockResolvedValue(undefined)
      vi.spyOn(messageStore, 'loadMessages').mockResolvedValue(undefined)
      
      await wrapper.vm.handleSessionSelect(sessionId)
      
      expect(sessionStore.selectSession).toHaveBeenCalledWith(sessionId)
      expect(messageStore.loadMessages).toHaveBeenCalledWith(sessionId)
    })

    it('应该能够创建新会话', async () => {
      const sessionData = {
        name: 'New Session',
        type: 'private',
        participants: ['user1']
      }
      
      const mockSession = { id: 'new-session', ...sessionData }
      vi.spyOn(sessionStore, 'createSession').mockResolvedValue(mockSession)
      
      await wrapper.vm.handleSessionCreate(sessionData)
      
      expect(sessionStore.createSession).toHaveBeenCalledWith(sessionData)
    })

    it('应该能够更新会话', async () => {
      const sessionId = 'session1'
      const updates = { name: 'Updated Session' }
      
      vi.spyOn(sessionStore, 'updateSession').mockResolvedValue(undefined)
      
      await wrapper.vm.handleSessionUpdate(sessionId, updates)
      
      expect(sessionStore.updateSession).toHaveBeenCalledWith(sessionId, updates)
    })

    it('应该能够关闭会话', async () => {
      const sessionId = 'session1'
      
      sessionStore.currentSessionId = sessionId
      vi.spyOn(sessionStore, 'unselectSession').mockImplementation(() => {})
      vi.spyOn(messageStore, 'clearMessages').mockImplementation(() => {})
      
      await wrapper.vm.handleSessionClose(sessionId)
      
      expect(sessionStore.unselectSession).toHaveBeenCalled()
      expect(messageStore.clearMessages).toHaveBeenCalled()
    })
  })

  describe('消息处理', () => {
    it('应该能够发送消息', async () => {
      const messageData = {
        content: 'Hello world',
        sessionId: 'session1'
      }
      
      vi.spyOn(messageStore, 'sendMessage').mockResolvedValue({ id: 'msg1', ...messageData })
      
      await wrapper.vm.handleMessageSend(messageData)
      
      expect(messageStore.sendMessage).toHaveBeenCalledWith(
        messageData.content,
        messageData.sessionId
      )
    })

    it('应该能够回复消息', async () => {
      const replyData = {
        content: 'Reply message',
        sessionId: 'session1',
        replyTo: 'original-msg'
      }
      
      vi.spyOn(messageStore, 'sendMessage').mockResolvedValue({ id: 'reply-msg', ...replyData })
      
      await wrapper.vm.handleMessageReply(replyData)
      
      expect(messageStore.sendMessage).toHaveBeenCalledWith(
        replyData.content,
        replyData.sessionId,
        { replyTo: replyData.replyTo }
      )
    })

    it('应该能够转发消息', async () => {
      const forwardData = {
        messageId: 'msg1',
        targetSessionId: 'session2'
      }
      
      vi.spyOn(messageStore, 'forwardMessage').mockResolvedValue({ id: 'forward-msg' })
      
      await wrapper.vm.handleMessageForward(forwardData)
      
      expect(messageStore.forwardMessage).toHaveBeenCalledWith(
        forwardData.messageId,
        forwardData.targetSessionId
      )
    })

    it('应该能够删除消息', async () => {
      const messageId = 'msg1'
      
      vi.spyOn(messageStore, 'deleteMessage').mockResolvedValue(undefined)
      
      await wrapper.vm.handleMessageDelete(messageId)
      
      expect(messageStore.deleteMessage).toHaveBeenCalledWith(messageId)
    })
  })

  describe('文件处理', () => {
    it('应该能够处理文件上传', async () => {
      const files = [
        new File(['test'], 'test.txt', { type: 'text/plain' })
      ]
      
      vi.spyOn(fileStore, 'addFilesToUploadQueue').mockImplementation(() => {})
      vi.spyOn(fileStore, 'startAllUploads').mockResolvedValue(undefined)
      
      await wrapper.vm.handleFileUpload(files)
      
      expect(fileStore.addFilesToUploadQueue).toHaveBeenCalledWith(files)
      expect(fileStore.startAllUploads).toHaveBeenCalled()
    })

    it('应该能够处理文件拖拽', async () => {
      const mockEvent = {
        preventDefault: vi.fn(),
        stopPropagation: vi.fn(),
        dataTransfer: {
          files: [
            new File(['test'], 'test.txt', { type: 'text/plain' })
          ]
        }
      }
      
      vi.spyOn(wrapper.vm, 'handleFileUpload').mockResolvedValue(undefined)
      
      await wrapper.vm.handleFileDrop(mockEvent)
      
      expect(mockEvent.preventDefault).toHaveBeenCalled()
      expect(mockEvent.stopPropagation).toHaveBeenCalled()
      expect(wrapper.vm.handleFileUpload).toHaveBeenCalledWith(mockEvent.dataTransfer.files)
    })
  })

  describe('生命周期', () => {
    it('应该在挂载时初始化聊天客户端', () => {
      expect(chatStore.initializeClient).toHaveBeenCalled()
    })

    it('应该在挂载时加载会话列表', () => {
      expect(sessionStore.loadSessions).toHaveBeenCalled()
    })

    it('应该在卸载时断开聊天客户端', async () => {
      await wrapper.unmount()
      
      expect(chatStore.disconnectClient).toHaveBeenCalled()
    })
  })

  describe('响应式布局', () => {
    it('应该在移动端隐藏侧边栏', async () => {
      // 模拟移动端屏幕尺寸
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768
      })
      
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.isMobile).toBe(true)
      expect(wrapper.vm.showSidebar).toBe(false)
    })

    it('应该在桌面端显示侧边栏', async () => {
      // 模拟桌面端屏幕尺寸
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200
      })
      
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.isMobile).toBe(false)
      expect(wrapper.vm.showSidebar).toBe(true)
    })
  })

  describe('键盘快捷键', () => {
    it('应该支持 Ctrl+N 创建新会话', async () => {
      vi.spyOn(wrapper.vm, 'handleSessionCreate').mockResolvedValue(undefined)
      
      const event = new KeyboardEvent('keydown', {
        key: 'n',
        ctrlKey: true
      })
      
      document.dispatchEvent(event)
      await wrapper.vm.$nextTick()
      
      // 应该触发创建会话的逻辑
      expect(wrapper.vm.showCreateSessionDialog).toBe(true)
    })

    it('应该支持 Ctrl+F 搜索', async () => {
      const event = new KeyboardEvent('keydown', {
        key: 'f',
        ctrlKey: true
      })
      
      document.dispatchEvent(event)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.showSearchDialog).toBe(true)
    })

    it('应该支持 Escape 关闭对话框', async () => {
      wrapper.vm.showCreateSessionDialog = true
      
      const event = new KeyboardEvent('keydown', {
        key: 'Escape'
      })
      
      document.dispatchEvent(event)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.showCreateSessionDialog).toBe(false)
    })
  })

  describe('错误处理', () => {
    it('应该处理会话加载错误', async () => {
      const error = new Error('Failed to load sessions')
      vi.spyOn(sessionStore, 'loadSessions').mockRejectedValue(error)
      
      // 重新挂载组件以触发初始化
      wrapper.unmount()
      wrapper = mount(ChatWindow, {
        global: {
          plugins: [createPinia()]
        }
      })
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.error).toBe(error.message)
    })

    it('应该处理消息发送错误', async () => {
      const error = new Error('Failed to send message')
      vi.spyOn(messageStore, 'sendMessage').mockRejectedValue(error)
      
      await wrapper.vm.handleMessageSend({
        content: 'Test message',
        sessionId: 'session1'
      })
      
      expect(wrapper.vm.error).toBe(error.message)
    })

    it('应该处理文件上传错误', async () => {
      const error = new Error('Failed to upload file')
      vi.spyOn(fileStore, 'startAllUploads').mockRejectedValue(error)
      
      const files = [new File(['test'], 'test.txt', { type: 'text/plain' })]
      await wrapper.vm.handleFileUpload(files)
      
      expect(wrapper.vm.error).toBe(error.message)
    })
  })

  describe('事件发射', () => {
    it('应该发射会话选择事件', async () => {
      const sessionId = 'session1'
      await wrapper.vm.handleSessionSelect(sessionId)
      
      expect(wrapper.emitted('session-select')).toBeTruthy()
      expect(wrapper.emitted('session-select')[0]).toEqual([sessionId])
    })

    it('应该发射消息发送事件', async () => {
      const messageData = {
        content: 'Hello',
        sessionId: 'session1'
      }
      
      vi.spyOn(messageStore, 'sendMessage').mockResolvedValue({ id: 'msg1', ...messageData })
      
      await wrapper.vm.handleMessageSend(messageData)
      
      expect(wrapper.emitted('message-send')).toBeTruthy()
    })

    it('应该发射窗口状态变化事件', async () => {
      await wrapper.vm.toggleSidebar()
      
      expect(wrapper.emitted('layout-change')).toBeTruthy()
    })
  })
})