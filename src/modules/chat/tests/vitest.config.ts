/**
 * Vitest 配置文件 - 聊天模块测试
 */

import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  test: {
    // 测试环境
    environment: 'jsdom',
    
    // 全局设置
    globals: true,
    
    // 设置文件
    setupFiles: [
      './setup/test-setup.ts'
    ],
    
    // 包含的测试文件
    include: [
      './unit/**/*.test.ts',
      './integration/**/*.test.ts'
    ],
    
    // 排除的文件
    exclude: [
      'node_modules',
      'dist',
      './e2e/**/*'
    ],
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      include: [
        'src/modules/chat/**/*.{ts,vue}'
      ],
      exclude: [
        'src/modules/chat/tests/**/*',
        'src/modules/chat/**/*.d.ts',
        'src/modules/chat/**/*.config.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // 测试超时
    testTimeout: 10000,
    
    // 钩子超时
    hookTimeout: 10000,
    
    // 并发运行
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1
      }
    },
    
    // 监听模式配置
    watch: {
      ignore: [
        'node_modules/**',
        'dist/**',
        'coverage/**'
      ]
    },
    
    // 报告器
    reporter: [
      'verbose',
      'json',
      'html'
    ],
    
    // 输出目录
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html'
    },
    
    // 模拟配置
    deps: {
      inline: [
        '@vue/test-utils'
      ]
    },
    
    // 环境变量
    env: {
      NODE_ENV: 'test',
      VITE_API_BASE_URL: 'http://localhost:3000/api',
      VITE_WS_URL: 'ws://localhost:3000/ws'
    }
  },
  
  // 解析配置
  resolve: {
    alias: {
      '@': resolve(__dirname, '../../../'),
      '@chat': resolve(__dirname, '../'),
      '@tests': resolve(__dirname, './')
    }
  },
  
  // 定义全局变量
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
  }
})