<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备信息测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .info-value {
            color: #6c757d;
            word-break: break-all;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
        }
        .success:hover {
            background: #1e7e34;
        }
        .warning {
            background: #ffc107;
            color: #212529;
        }
        .warning:hover {
            background: #e0a800;
        }
        .button-group {
            text-align: center;
            margin-top: 30px;
        }
        .json-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 设备信息功能测试</h1>
        
        <div class="info-section">
            <div class="info-label">设备ID:</div>
            <div class="info-value" id="deviceId">-</div>
        </div>
        
        <div class="info-section">
            <div class="info-label">设备名称:</div>
            <div class="info-value" id="deviceName">-</div>
        </div>
        
        <div class="info-section">
            <div class="info-label">设备类型:</div>
            <div class="info-value" id="deviceType">-</div>
        </div>
        
        <div class="info-section">
            <div class="info-label">操作系统:</div>
            <div class="info-value" id="platform">-</div>
        </div>
        
        <div class="info-section">
            <div class="info-label">浏览器:</div>
            <div class="info-value" id="browser">-</div>
        </div>
        
        <div class="info-section">
            <div class="info-label">User Agent:</div>
            <div class="info-value" id="userAgent">-</div>
        </div>
        
        <div class="button-group">
            <button class="test-button" onclick="generateDeviceInfo()">🔄 生成设备信息</button>
            <button class="test-button success" onclick="testLocalStorage()">💾 测试本地存储</button>
            <button class="test-button warning" onclick="clearDeviceInfo()">🗑️ 清除设备信息</button>
        </div>
        
        <div class="json-output" id="jsonOutput"></div>
    </div>

    <script>
        // 生成设备信息的函数
        function generateDeviceInfo() {
            // 生成设备ID
            const generateDeviceId = () => {
                const stored = localStorage.getItem('device_id');
                if (stored) return stored;
                
                const deviceId = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('device_id', deviceId);
                return deviceId;
            };
            
            // 获取设备类型
            const getDeviceType = () => {
                const userAgent = navigator.userAgent.toLowerCase();
                if (/mobile|android|iphone|ipad|phone/i.test(userAgent)) {
                    return 'mobile';
                }
                return 'web';
            };
            
            // 获取平台信息
            const getPlatform = () => {
                const userAgent = navigator.userAgent.toLowerCase();
                if (userAgent.includes('mac')) return 'macOS';
                if (userAgent.includes('win')) return 'Windows';
                if (userAgent.includes('linux')) return 'Linux';
                if (userAgent.includes('android')) return 'Android';
                if (userAgent.includes('iphone') || userAgent.includes('ipad')) return 'iOS';
                return 'Unknown';
            };
            
            // 获取浏览器信息
            const getBrowser = () => {
                const userAgent = navigator.userAgent.toLowerCase();
                if (userAgent.includes('chrome')) return 'Chrome';
                if (userAgent.includes('firefox')) return 'Firefox';
                if (userAgent.includes('safari') && !userAgent.includes('chrome')) return 'Safari';
                if (userAgent.includes('edge')) return 'Edge';
                return 'Unknown';
            };
            
            // 获取设备名称
            const getDeviceName = () => {
                const platform = getPlatform();
                const browser = getBrowser();
                return `${platform} - ${browser}`;
            };
            
            const deviceInfo = {
                device_id: generateDeviceId(),
                device_name: getDeviceName(),
                device_type: getDeviceType(),
                platform: getPlatform(),
                browser: getBrowser(),
                app_version: '1.0.0',
                os_version: 'Unknown',
                user_agent: navigator.userAgent
            };
            
            // 更新页面显示
            document.getElementById('deviceId').textContent = deviceInfo.device_id;
            document.getElementById('deviceName').textContent = deviceInfo.device_name;
            document.getElementById('deviceType').textContent = deviceInfo.device_type;
            document.getElementById('platform').textContent = deviceInfo.platform;
            document.getElementById('browser').textContent = deviceInfo.browser;
            document.getElementById('userAgent').textContent = deviceInfo.user_agent;
            
            // 显示JSON格式
            document.getElementById('jsonOutput').textContent = JSON.stringify(deviceInfo, null, 2);
            
            return deviceInfo;
        }
        
        // 测试本地存储功能
        function testLocalStorage() {
            const deviceInfo = generateDeviceInfo();
            
            // 保存到本地存储
            localStorage.setItem('current_device_info', JSON.stringify(deviceInfo));
            localStorage.setItem('device_id', deviceInfo.device_id);
            
            // 验证存储
            const stored = localStorage.getItem('current_device_info');
            const storedDeviceId = localStorage.getItem('device_id');
            
            if (stored && storedDeviceId) {
                alert('✅ 设备信息已成功保存到本地存储！\n\n设备ID: ' + storedDeviceId);
            } else {
                alert('❌ 保存到本地存储失败！');
            }
        }
        
        // 清除设备信息
        function clearDeviceInfo() {
            localStorage.removeItem('device_id');
            localStorage.removeItem('current_device_info');
            localStorage.removeItem('user_device_id');
            localStorage.removeItem('merchant_device_id');
            localStorage.removeItem('admin_device_id');
            
            // 清空页面显示
            document.getElementById('deviceId').textContent = '-';
            document.getElementById('deviceName').textContent = '-';
            document.getElementById('deviceType').textContent = '-';
            document.getElementById('platform').textContent = '-';
            document.getElementById('browser').textContent = '-';
            document.getElementById('userAgent').textContent = '-';
            document.getElementById('jsonOutput').textContent = '';
            
            alert('🗑️ 设备信息已清除！');
        }
        
        // 页面加载时自动生成设备信息
        window.onload = function() {
            generateDeviceInfo();
        };
    </script>
</body>
</html>