# Request模块拆分完成总结

## 拆分概述

已成功将原来的统一 `src/utils/request.ts` 文件按照功能拆分为三个独立的模块请求工具：

### 1. 已创建的文件

- ✅ `src/modules/admin/utils/request.ts` - 管理员模块专用请求工具
- ✅ `src/modules/merchant/utils/request.ts` - 商家模块专用请求工具  
- ✅ `src/modules/user/utils/request.ts` - 用户模块专用请求工具（包含runner功能）
- ✅ `MIGRATION_GUIDE.md` - 详细的迁移指南
- ✅ `REQUEST_SPLIT_SUMMARY.md` - 本总结文档

### 2. 示例迁移

已完成以下文件的示例迁移：
- ✅ `src/modules/admin/api/auth.ts`
- ✅ `src/modules/merchant/api/auth.ts`
- ✅ `src/modules/user/api/auth.ts`

## 拆分后的架构优势

### 1. 模块独立性
- **独立的token管理**: 每个模块管理自己的access_token和refresh_token
- **独立的错误处理**: 401错误时跳转到对应模块的登录页
- **独立的白名单配置**: 每个模块有自己的免认证API列表

### 2. 代码简化
- **移除复杂的namespace判断**: 不再需要从URL动态提取namespace
- **简化的store导入**: 每个模块直接导入对应的store
- **清晰的业务边界**: 每个模块只关心自己的业务逻辑

### 3. 维护性提升
- **更好的可测试性**: 每个模块可以独立测试
- **降低耦合度**: 模块间不再共享复杂的全局状态
- **更容易调试**: 问题定位更加精确

## 各模块特性

### Admin模块 (`src/modules/admin/utils/request.ts`)
- **命名空间**: `admin`
- **Token存储**: `admin_access_token`, `admin_refresh_token`
- **白名单**: admin登录、刷新token、重置密码接口
- **错误跳转**: `/admin/login`
- **Store依赖**: `@/modules/admin/stores/adminStore`

### Merchant模块 (`src/modules/merchant/utils/request.ts`)
- **命名空间**: `merchant`
- **Token存储**: `merchant_access_token`, `merchant_refresh_token`
- **白名单**: merchant登录、刷新token、重置密码接口
- **错误跳转**: `/merchant/login`
- **Store依赖**: `@/modules/merchant/stores/merchantStore`

### User模块 (`src/modules/user/utils/request.ts`)
- **命名空间**: `user`
- **Token存储**: `user_access_token`, `user_refresh_token`
- **白名单**: user和runner的登录、刷新token、重置密码接口
- **错误跳转**: `/user/login`
- **Store依赖**: `@/modules/user/stores/userStore`
- **特殊说明**: 同时处理runner模块的请求

## 需要完成的后续工作

### 1. 批量迁移API文件

**Admin模块** (约15个文件):
```bash
# 需要修改的文件
src/modules/admin/api/admin.ts
src/modules/admin/api/apidoc.ts
src/modules/admin/api/communityAddress.ts
src/modules/admin/api/file.ts
src/modules/admin/api/fileUsageConfig.ts
src/modules/admin/api/gridInfos.ts
src/modules/admin/api/merchant.ts
src/modules/admin/api/order.ts
src/modules/admin/api/permission.ts
src/modules/admin/api/product.ts
src/modules/admin/api/referral.ts
src/modules/admin/api/role.ts
src/modules/admin/api/runner.ts
src/modules/admin/api/smsConfig.ts
src/modules/admin/api/system.ts
src/modules/admin/api/systemConfig.ts
src/modules/admin/api/takeout.ts
src/modules/admin/api/task.ts
src/modules/admin/api/uiConfig.ts
src/modules/admin/api/uploadConfig.ts
src/modules/admin/api/user.ts
```

**Merchant模块** (约12个文件):
```bash
# 需要修改的文件
src/modules/merchant/api/business-hours.ts
src/modules/merchant/api/category.ts
src/modules/merchant/api/coupon.ts
src/modules/merchant/api/financial.ts
src/modules/merchant/api/frontendPath.ts
src/modules/merchant/api/notification.ts
src/modules/merchant/api/order.ts
src/modules/merchant/api/product.ts
src/modules/merchant/api/profile.ts
src/modules/merchant/api/promotion.ts
src/modules/merchant/api/refund.ts
src/modules/merchant/api/reviews.ts
src/modules/merchant/api/system.ts
src/modules/merchant/api/takeout.ts
src/modules/merchant/api/takeoutCategory.ts
```

**User模块** (约10个文件):
```bash
# 需要修改的文件
src/modules/user/api/address.ts
src/modules/user/api/order.ts
src/modules/user/api/payment.ts
src/modules/user/api/profile.ts
src/modules/user/api/runnerApply.ts
src/modules/user/api/runnerIncome.ts
src/modules/user/api/runnerOrder.ts
src/modules/user/api/runnerSettings.ts
src/modules/user/api/runnerWorkStatus.ts
src/modules/user/api/takeout.ts
src/modules/user/api/runner/runnerApply.ts
src/modules/user/api/runner/runnerIncome.ts
src/modules/user/api/runner/runnerOrder.ts
src/modules/user/api/runner/runnerSettings.ts
src/modules/user/api/runner/runnerSettingsApi.ts
src/modules/user/api/runner/runnerWorkStatus.ts
```

### 2. 修改Vue组件

需要检查并修改直接在Vue组件中使用request的文件：
- `src/modules/admin/views/user/UserView.vue`
- `src/modules/user/views/Orders.vue`
- 其他可能直接导入request的Vue组件

### 3. 测试验证

**功能测试清单**:
- [ ] Admin模块登录/登出功能
- [ ] Admin模块token自动刷新
- [ ] Admin模块401错误处理和路由跳转
- [ ] Merchant模块登录/登出功能
- [ ] Merchant模块token自动刷新
- [ ] Merchant模块401错误处理和路由跳转
- [ ] User模块登录/登出功能
- [ ] User模块token自动刷新
- [ ] User模块401错误处理和路由跳转
- [ ] Runner功能使用User模块request正常工作
- [ ] 各模块API调用正常
- [ ] 跨模块token隔离正常

### 4. 清理工作

迁移完成并测试通过后：
- [ ] 备份原始的 `src/utils/request.ts` 文件
- [ ] 删除或重命名原始文件（避免误用）
- [ ] 更新项目文档
- [ ] 通知团队成员新的导入规范

## 使用建议

### 1. 渐进式迁移
建议按以下顺序进行迁移：
1. 先迁移一个模块（如admin）
2. 充分测试该模块功能
3. 确认无问题后再迁移下一个模块

### 2. 自动化工具
可以使用提供的批量替换命令：
```bash
# Admin模块
find src/modules/admin -name "*.ts" -o -name "*.vue" | xargs sed -i '' "s|from '@/utils/request'|from '@/modules/admin/utils/request'|g"

# Merchant模块
find src/modules/merchant -name "*.ts" -o -name "*.vue" | xargs sed -i '' "s|from '@/utils/request'|from '@/modules/merchant/utils/request'|g"

# User模块
find src/modules/user -name "*.ts" -o -name "*.vue" | xargs sed -i '' "s|from '@/utils/request'|from '@/modules/user/utils/request'|g"
```

### 3. 代码审查
建议在迁移过程中进行代码审查，确保：
- 导入路径正确
- 没有遗漏的文件
- 功能测试通过

## 总结

本次request模块拆分成功实现了：
- ✅ 模块化架构设计
- ✅ 独立的token管理机制
- ✅ 简化的代码逻辑
- ✅ 更好的可维护性
- ✅ 完整的迁移指南

拆分后的架构更加清晰，各模块职责明确，为后续的开发和维护提供了良好的基础。